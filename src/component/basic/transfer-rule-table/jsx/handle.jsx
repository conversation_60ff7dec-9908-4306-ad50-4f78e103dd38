import React, { Component } from 'react';
import PropTypes from 'prop-types';
import {
  Button, Modal, Radio, Select,
} from 'shineout';
import RuleInput from '@shein-components/WmsInput';
import globalStyles from '@src/component/style.less';
import { t } from '@shein-bbl/react';
import store from '../reducers';
import style from '../style.css';

// 是否禁用 确认按钮
const handleEditButtonBool = (editObj) => {
  const {
    srcWarehouseId,
    destWarehouseId,
    allocationType,
    transferType,
    enabled,
    isPallet,
    isTransport,
    isQualityCheck,
    isUpper,
    isSplitBox, // 是否分箱
    splitBoxSkuNumFlag, // 分箱SKU个数 >= <=
    splitBoxSkuNum, // 分箱SKU个数
    boxNumLimit, // 是否箱件限制
    boxSkuNumFlag, // 设置SKU箱件 >= <=
    boxSkuNum, // 设置SKU箱件
    belongSystemList,
    isAllowNonGenuineTransfer,
  } = editObj;
  if (belongSystemList.length === 0) {
    return true;
  }
  // 是否分箱为否的情况
  if (Number(editObj.isSplitBox) === 0) {
    return [
      srcWarehouseId,
      destWarehouseId,
      allocationType,
      transferType,
      enabled,
      isPallet,
      isTransport,
      isQualityCheck,
      isUpper,
    ].some((val) => ['', undefined].includes(val));
  }
  // 是否分箱为是 && 箱件限制为否的情况
  if (Number(editObj.isSplitBox) === 1 && !editObj.boxNumLimit) {
    return [
      srcWarehouseId,
      destWarehouseId,
      allocationType,
      transferType,
      enabled,
      isPallet,
      isTransport,
      isQualityCheck,
      isUpper,
      isSplitBox,
      splitBoxSkuNumFlag,
      splitBoxSkuNum,
    ].some((val) => ['', undefined].includes(val));
  }
  // 是否分箱为是 && 箱件限制均为是的情况
  if (Number(editObj.isSplitBox) === 1 && editObj.boxNumLimit) {
    return [
      srcWarehouseId,
      destWarehouseId,
      allocationType,
      transferType,
      enabled,
      isPallet,
      isTransport,
      isQualityCheck,
      isUpper,
      isSplitBox, // 是否分箱
      splitBoxSkuNumFlag, // 分箱SKU个数 >= <=
      splitBoxSkuNum, // 分箱SKU个数
      boxNumLimit, // 是否箱件限制
      boxSkuNumFlag, // 设置SKU箱件 >= <=
      boxSkuNum, // 设置SKU箱件
    ].some((val) => ['', undefined].includes(val));
  }
  // 当调拨类型为基础调拨/退货调拨时，没有填写是否允许非正品调出，则不允许确认
  if ([0, 4].includes(allocationType) && !isAllowNonGenuineTransfer) {
    return true;
  }
};

class Handle extends Component {
  render() {
    const {
      loading,
      selectedRows,
      showEditModal,
      editObj,
      warehouseList,
      allocationTypeList,
      enabledList,
      isList,
      transferTypeList,
      isSplitBoxList,
      boxNumLimitList,
      warehouseBelongSystemList,
      isAllowNonGenuineTransferList,
    } = this.props;
    return (
      <section className={[globalStyles.handle, 'handleSection'].join(' ')}>
        <Button
          type="primary"
          disabled={!loading}
          onClick={() => {
            store.changeData({
              showEditModal: 1,
              editObj: {
                srcWarehouseId: '',
                destWarehouseId: '',
                allocationType: '',
                transferType: '',
                enabled: '',
                isPallet: '',
                isTransport: '',
                isQualityCheck: '',
                isUpper: '',
                isSplitBox: 0, // 是否分箱
                splitBoxSkuNumFlag: 2, // 分箱SKU个数 >= <=
                splitBoxSkuNum: 0, // 分箱SKU个数
                boxNumLimit: false, // 是否箱件限制
                boxSkuNumFlag: 1, // 设置SKU箱件 >= <=
                boxSkuNum: 0, // 设置SKU箱件
                belongSystemList: [],
                isAllowNonGenuineTransfer: '',
              },
            });
          }}
        >
          {t('新增')}
        </Button>
        <Button
          type="primary"
          disabled={selectedRows.length !== 1 || !loading}
          onClick={() => {
            store.changeData({
              showEditModal: 2,
              editObj: {
                ...selectedRows[0],
                belongSystemList: selectedRows[0].belongSystemList || [],
                isSplitBox: selectedRows[0]?.isSplitBox ? 1 : 0,
              },
            });
          }}
        >
          {t('修改')}
        </Button>
        <Modal
          title={showEditModal === 1 ? t('新增调拨规则') : t('修改调拨规则')}
          visible={showEditModal}
          maskCloseAble={null}
          onClose={() => store.changeData({ showEditModal: 0 })}
          footer={[
            <Button
              onClick={() => {
                store.changeData({ showEditModal: 0 });
              }}
            >
              {t('取消')}
            </Button>,
            <Button
              type="primary"
              disabled={handleEditButtonBool(editObj)}
              loading={!loading}
              onClick={() => {
                store.editData();
              }}
            >
              {t('确认')}
            </Button>,
          ]}
        >
          <div className={style.compileModalCont}>
            <div>
              <span className={style.selectLabel}>
                <span style={{ color: 'red' }}>*</span>
                {t('出发仓')}
                :
              </span>
              <Select
                data-bind="editObj.srcWarehouseId"
                disabled={showEditModal !== 1}
                placeholder={t('请选择')}
                width={220}
                data={warehouseList}
                format="id"
                keygen="id"
                renderItem="nameZh"
                onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
              />
            </div>
            <div>
              <span className={style.selectLabel}>
                <span style={{ color: 'red' }}>*</span>
                {t('目的仓')}
                :
              </span>
              <Select
                data-bind="editObj.destWarehouseId"
                disabled={showEditModal !== 1}
                placeholder={t('请选择')}
                width={220}
                data={warehouseList.filter((item) => {
                  // 新增的时候,接口仓库过滤掉type为5
                  if (showEditModal === 1) {
                    return item.type !== 5;
                  }
                  return item;
                })}
                format="id"
                keygen="id"
                renderItem="nameZh"
                onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
              />
            </div>
            <div>
              <span className={style.selectLabel}>
                <span style={{ color: 'red' }}>*</span>
                {t('调拨类型')}
                :
              </span>
              <Select
                placeholder={t('请选择')}
                width={220}
                data={allocationTypeList}
                disabled={showEditModal === 2}
                format="dictCode"
                keygen="dictCode"
                renderItem="dictNameZh"
                value={editObj.allocationType}
                onChange={(val) => {
                  // 整箱出库调拨
                  if (val === 5) {
                    store.changeEditObj({
                      isQualityCheck: 1,
                      isUpper: 1,
                      allocationType: val,
                    });
                  } else {
                    store.changeEditObj({
                      allocationType: val,
                    });
                  }
                }}
              />
            </div>
            <div>
              <span className={style.selectLabel}>
                <span style={{ color: 'red' }}>*</span>
                {t('运输方式')}
                :
              </span>
              <Select
                data-bind="editObj.transferType"
                placeholder={t('请选择')}
                width={220}
                data={transferTypeList}
                format="dictCode"
                keygen="dictCode"
                renderItem="dictNameZh"
              />
            </div>
            <div>
              <span className={style.selectLabel}>
                <span style={{ color: 'red' }}>*</span>
                {t('是否启用')}
                :
              </span>
              <Select
                data-bind="editObj.enabled"
                placeholder={t('请选择')}
                width={220}
                data={enabledList}
                format="dictCode"
                keygen="dictCode"
                renderItem="dictNameZh"
              />
            </div>
            {/* 当调拨类型=基础调拨/退货调拨时展示 */}
            {[0, 4].includes(editObj.allocationType) && (
            <div>
              <span className={style.selectLabel}>
                <span style={{ color: 'red' }}>*</span>
                {t('是否允许非正品调出')}
                :
              </span>
              <Select
                data-bind="editObj.isAllowNonGenuineTransfer"
                placeholder={t('请选择')}
                width={220}
                data={isAllowNonGenuineTransferList}
                format="dictCode"
                keygen="dictCode"
                renderItem="dictNameZh"
              />
            </div>
            )}
            <div>
              <span className={style.selectLabel}>
                <span style={{ color: 'red' }}>*</span>
                {t('是否装托')}
                :
              </span>
              <Select
                data-bind="editObj.isPallet"
                placeholder={t('请选择')}
                width={220}
                data={isList}
                format="dictCode"
                keygen="dictCode"
                renderItem="dictNameZh"
              />
            </div>
            <div>
              <span className={style.selectLabel}>
                <span style={{ color: 'red' }}>*</span>
                {t('是否装车')}
                :
              </span>
              <Select
                data-bind="editObj.isTransport"
                placeholder={t('请选择')}
                width={220}
                data={isList}
                format="dictCode"
                keygen="dictCode"
                renderItem="dictNameZh"
              />
            </div>
            <div>
              <span className={style.selectLabel}>
                <span style={{ color: 'red' }}>*</span>
                {t('是否质检')}
                :
              </span>
              <Select
                data-bind="editObj.isQualityCheck"
                placeholder={t('请选择')}
                width={220}
                data={isList}
                format="dictCode"
                keygen="dictCode"
                renderItem="dictNameZh"
                disabled={editObj.allocationType === 5}
                onChange={() => {
                  store.changeEditObj({
                    isSplitBox: 0, // 是否分箱
                    splitBoxSkuNumFlag: 2, // 分箱SKU个数 >= <=
                    splitBoxSkuNum: 0, // 分箱SKU个数
                    boxNumLimit: false, // 是否箱件限制
                    boxSkuNumFlag: 1, // 设置SKU箱件 >= <=
                    boxSkuNum: 0, // 设置SKU箱件
                  });
                }}
              />
            </div>
            <div>
              <span className={style.selectLabel}>
                <span style={{ color: 'red' }}>*</span>
                {t('是否上架')}
                :
              </span>
              <Select
                data-bind="editObj.isUpper"
                placeholder={t('请选择')}
                width={220}
                data={isList}
                format="dictCode"
                keygen="dictCode"
                renderItem="dictNameZh"
                disabled={editObj.allocationType === 5}
                onChange={() => {
                  store.changeEditObj({
                    isSplitBox: 0, // 是否分箱
                    splitBoxSkuNumFlag: 2, // 分箱SKU个数 >= <=
                    splitBoxSkuNum: 0, // 分箱SKU个数
                    boxNumLimit: false, // 是否箱件限制
                    boxSkuNumFlag: 1, // 设置SKU箱件 >= <=
                    boxSkuNum: 0, // 设置SKU箱件
                  });
                }}
              />
            </div>
            {/*  当【是否质检=否】+【是否上架=是】时，联动出现【是否分箱】 */}
            {editObj.isQualityCheck === 1 && editObj.isUpper === 0 && (
              <div>
                <span className={style.selectLabel}>
                  <span style={{ color: 'red' }}>*</span>
                  {t('是否分箱')}
                  :
                </span>
                <Select
                  data-bind="editObj.isSplitBox"
                  placeholder={t('请选择')}
                  width={220}
                  data={isSplitBoxList}
                  format="dictCode"
                  keygen="dictCode"
                  renderItem="dictNameZh"
                  onChange={() => {
                    store.changeEditObj({
                      splitBoxSkuNumFlag: 2, // 分箱SKU个数 >= <=
                      splitBoxSkuNum: 0, // 分箱SKU个数
                      boxNumLimit: false, // 是否箱件限制
                      boxSkuNumFlag: 1, // 设置SKU箱件 >= <=
                      boxSkuNum: 0, // 设置SKU箱件
                    });
                  }}
                />
              </div>
            )}
            {/* 【是否分箱=是】时，联动出现【分箱SKU个数】，否则，用户不可见  */}
            {editObj.isQualityCheck === 1 && editObj.isUpper === 0 && editObj.isSplitBox === 1 && (
              <div>
                <span className={style.selectLabel}>
                  <span style={{ color: 'red' }}>*</span>
                  {t('分箱SKU个数')}
                  :
                </span>
                <Select
                  data-bind="editObj.splitBoxSkuNumFlag"
                  placeholder={t('请选择')}
                  width={100}
                  data={[
                    { dictCode: 2, dictNameZh: '<=' },
                  ]}
                  format="dictCode"
                  keygen="dictCode"
                  renderItem="dictNameZh"
                />
                <RuleInput.Number
                  style={{ marginLeft: '5px' }}
                  digits={0}
                  data-bind="editObj.splitBoxSkuNum"
                  placeholder={t('请输入数量')}
                  width={115}
                  min={1}
                  max={99}
                />
              </div>
            )}
            {/* 是否分箱=是显示，箱件限制默认否 */}
            {editObj.isQualityCheck === 1 && editObj.isUpper === 0 && editObj.isSplitBox === 1 && (
              <div>
                <span className={style.selectLabel}>
                  <span style={{ color: 'red' }}>*</span>
                  {t('箱件限制')}
                  :
                </span>
                <div style={{ display: 'inline-block' }}>
                  <Radio.Group
                    data-bind="editObj.boxNumLimit"
                    onChange={() => {
                      store.changeEditObj({
                        boxSkuNumFlag: 1, // 设置SKU箱件 >= <=
                        boxSkuNum: 0, // 设置SKU箱件
                      });
                    }}
                  >
                    {(boxNumLimitList || []).map((d) => (
                      <Radio key={d.dictCode} htmlValue={d.dictCode}>
                        {d.dictNameZh}
                      </Radio>
                    ))}
                  </Radio.Group>
                </div>
              </div>
            )}
            {/* 当【箱件限制=是】时，联动出现【设置SKU箱件】，否则，用户不可见  */}
            {editObj.isQualityCheck === 1 && editObj.isUpper === 0 && editObj.isSplitBox === 1 && editObj.boxNumLimit && (
              <div>
                <span className={style.selectLabel}>
                  <span style={{ color: 'red' }}>*</span>
                  {t('设置SKU箱件')}
                  :
                </span>
                <Select
                  data-bind="editObj.boxSkuNumFlag"
                  placeholder={t('请选择')}
                  width={100}
                  data={[
                    { dictCode: 1, dictNameZh: '>=' },
                  ]}
                  format="dictCode"
                  keygen="dictCode"
                  renderItem="dictNameZh"
                />
                <RuleInput.Number
                  style={{ marginLeft: '5px' }}
                  digits={0}
                  data-bind="editObj.boxSkuNum"
                  placeholder={t('请输入数量')}
                  width={115}
                  min={1}
                  max={999999}
                />
              </div>
            )}
            <div>
              <span className={style.selectLabel}>
                <span style={{ color: 'red' }}>*</span>
                {t('涉及的数据中心')}
                :
              </span>
              <Select
                disabled={showEditModal === 2}
                data-bind="editObj.belongSystemList"
                placeholder={t('请选择')}
                width={220}
                data={warehouseBelongSystemList}
                format="dictCode"
                keygen="dictCode"
                renderItem="dictNameZh"
                multiple
                compressed
                clearable
              />
            </div>
          </div>
        </Modal>
      </section>
    );
  }
}

Handle.propTypes = {
  loading: PropTypes.number,
  editObj: PropTypes.shape(),
  showEditModal: PropTypes.number.isRequired,
  isList: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  warehouseList: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  enabledList: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  allocationTypeList: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  transferTypeList: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  selectedRows: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  isSplitBoxList: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  warehouseBelongSystemList: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  boxNumLimitList: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  isAllowNonGenuineTransferList: PropTypes.arrayOf(PropTypes.shape()),
};
export default Handle;
