import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Select } from 'shineout';
import DateRangePicker from '@shein-components/dateRangePicker2';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import { formatSearchData } from '@public-component/search-queries/utils';
// import { validateNatural } from '@src/lib/validate';
import store, { defaultLimit } from '../reducers';

class Header extends Component {
  render() {
    const {
      loading,
      limit,
      isList,
      warehouseList,
      enabledList,
      allocationTypeList,
      transferTypeList,
      isSplitBoxList,
      warehouseBelongSystemList,
      isAllowNonGenuineTransferList,
    } = this.props;
    return (
      <section>
        <SearchAreaContainer
          value={limit}
          collapseOnSearch={false}
          clearUndefined={false}
          labelStyle={{ width: 100 }} // 统一修改label宽度，默认60
          searching={!loading}
          onSearch={() => {
            store.handlePaginationChange({ pageNum: 1 });
          }}
          onChange={(val) => {
            store.changeLimitData(formatSearchData(defaultLimit, val));
          }}
          onClear={() => store.clearLimitData()}
          onToggle={() => {}}
          formRef={(f) => {
            store.changeData({
              formRef: f,
            });
          }}

        >
          <Select
            label={t('出发仓')}
            placeholder={t('全部')}
            name="srcWarehouseId"
            keygen="id"
            format="id"
            renderItem="nameZh"
            data={warehouseList}
            clearable
            onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
          <Select
            label={t('目的仓')}
            placeholder={t('全部')}
            name="destWarehouseId"
            keygen="id"
            format="id"
            renderItem="nameZh"
            data={warehouseList}
            clearable
            onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
          <Select
            label={t('调拨类型')}
            placeholder={t('全部')}
            name="allocationType"
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            data={allocationTypeList}
            clearable
          />
          <Select
            label={t('涉及的数据中心')}
            placeholder={t('全部')}
            name="belongSystemList"
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            data={warehouseBelongSystemList}
            multiple
            compressed
            clearable
          />
          <Select
            label={t('运输方式')}
            placeholder={t('全部')}
            name="transferType"
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            data={transferTypeList}
            clearable
          />
          <Select
            label={t('状态')}
            placeholder={t('全部')}
            name="enabled"
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            data={enabledList}
            multiple
            compressed
            clearable
          />
          <Select
            label={t('是否装托')}
            placeholder={t('全部')}
            name="isPallet"
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            data={isList}
            multiple
            compressed
            clearable
          />
          <Select
            label={t('是否质检')}
            placeholder={t('全部')}
            name="isQualityCheck"
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            data={isList}
            multiple
            compressed
            clearable
          />
          <Select
            label={t('是否上架')}
            placeholder={t('全部')}
            name="isUpper"
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            data={isList}
            multiple
            compressed
            clearable
          />
          <Select
            label={t('是否装车')}
            placeholder={t('全部')}
            name="isTransport"
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            data={isList}
            multiple
            compressed
            clearable
          />
          <Select
            label={t('是否分箱')}
            placeholder={t('全部')}
            name="isSplitBox"
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            data={isSplitBoxList}
            multiple
            compressed
            clearable
          />
          <Select
            label={t('是否允许非正品调出')}
            placeholder={t('全部')}
            name="isAllowNonGenuineTransfer"
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            data={isAllowNonGenuineTransferList}
            clearable
          />
          <DateRangePicker
            span={2}
            label={t('创建时间')}
            placeholder={[t('开始时间'), t('结束时间')]}
            type="datetime"
            range
            inputable
            name={['createTimeStart', 'createTimeEnd']}
            format="yyyy-MM-dd HH:mm:ss"
            defaultTime={['00:00:00', '23:59:59']}
          />
        </SearchAreaContainer>
      </section>
    );
  }
}

Header.propTypes = {
  loading: PropTypes.number.isRequired,
  limit: PropTypes.shape(),
  isList: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  warehouseList: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  enabledList: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  allocationTypeList: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  transferTypeList: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  isSplitBoxList: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  warehouseBelongSystemList: PropTypes.arrayOf(PropTypes.shape()),
  isAllowNonGenuineTransferList: PropTypes.arrayOf(PropTypes.shape()),
};
export default Header;
