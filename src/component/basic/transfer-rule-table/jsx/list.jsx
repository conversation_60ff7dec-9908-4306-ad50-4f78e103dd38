import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Table, Button } from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import OperationModal from '@public-component/modal/operation-modal';
import globalStyles from '@src/component/style.less';
import store from '../reducers';

function List(props) {
  const {
    loading,
    list,
    selectedRows,
    pageInfo,
    recordVisible,
    recordId,
  } = props;

  const columns = [
    {
      title: t('出发仓'),
      render: 'srcWarehouseName',
      width: 100,
    },
    {
      title: t('目的仓'),
      render: 'destWarehouseName',
      width: 100,
    },
    {
      title: t('调拨类型'),
      render: 'allocationTypeName',
      width: 100,
    },
    {
      title: t('运输方式'),
      render: 'transferTypeName',
      width: 100,
    },
    {
      title: t('状态'),
      render: 'enabledName',
      width: 120,
    },
    {
      title: t('是否允许非正品调出'),
      render: 'isAllowNonGenuineTransferDesc',
      width: 160,
    },
    {
      title: t('是否装托'),
      render: 'isPalletName',
      width: 100,
    },
    {
      title: t('是否装车'),
      render: 'isTransportName',
      width: 100,
    },
    {
      title: t('是否质检'),
      render: 'isQualityCheckName',
      width: 100,
    },
    {
      title: t('是否上架'),
      render: 'isUpperName',
      width: 100,
    },
    {
      title: t('涉及的数据中心'),
      width: 140,
      render: (row) => (row.belongSystemNameList || []).map((item) => (<p>{item}</p>)),
    },
    {
      title: t('是否分箱'),
      render: 'isSplitBoxName',
      width: 100,
    },
    {
      title: t('分箱SKU个数'),
      render: (r) => `${r.splitBoxSkuNumFlagName}${r.splitBoxSkuNum}`,
      width: 140,
    },
    {
      title: t('SKU箱件'),
      render: (r) => `${r.boxSkuNumFlagName}${r.boxSkuNum}`,
      width: 120,
    },
    {
      title: t('创建人'),
      render: 'createUser',
      width: 100,
    },
    {
      title: t('创建时间'),
      render: 'createTime',
      width: 190,
    },
    {
      title: t('操作'),
      width: 120,
      fixed: 'right',
      render: (record) => (
        <div>
          <Button
            size="small"
            text
            type="primary"
            className={globalStyles.tableTextButton}
            onClick={() => store.changeData({ recordVisible: true, recordId: record.id })}
          >
            {t('操作日志')}
          </Button>
        </div>
      ),
    },
  ];

  return (
    <section className={globalStyles.tableSection}>
      <SearchAreaTable>
        <Table
          style={{ height: '100%' }}
          rowClassName={() => globalStyles.borderInner}
          bordered
          fixed="both"
          loading={!loading}
          data={list}
          columns={columns}
          keygen="id"
          empty={t('暂无数据')}
          size="small"
          width={columns.reduce((pre, current) => pre + (current.width || 100), 50)}
          value={selectedRows}
          onRowSelect={(rows) => {
            store.changeData({ selectedRows: rows });
          }}
          pagination={{
            align: 'right',
            current: pageInfo.pageNum,
            pageSize: pageInfo.pageSize,
            layout: [({ total }) => `${t('共')} ${total} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
            onChange: (page, size) => {
              store.handlePaginationChange({
                pageNum: page,
                pageSize: size,
              });
            },
            className: globalStyles.pagination,
            pageSizeList: pageInfo.pageSizeList,
            total: pageInfo.count,
          }}
        />
      </SearchAreaTable>

      {/* 操作记录 */}
      <OperationModal
        visible={recordVisible}
        param={{
          operateId: recordId,
          operateCode: 'WTS_ALLOCATION_RULE_OPERATE',
        }}
        onCancel={() => store.changeData({ recordVisible: false })}
      />
    </section>
  );
}

List.propTypes = {
  loading: PropTypes.number.isRequired,
  list: PropTypes.arrayOf(PropTypes.shape).isRequired,
  selectedRows: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  // count: PropTypes.number.isRequired,
  recordId: PropTypes.string,
  pageInfo: PropTypes.shape(),
  recordVisible: PropTypes.bool,
};

export default List;
