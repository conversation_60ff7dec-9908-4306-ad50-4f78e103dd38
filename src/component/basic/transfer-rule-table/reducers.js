import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { select } from 'redux-saga/effects';
import { Modal, Message } from 'shineout';
import { getWarehouseApi, dictSelect } from '@src/server/basic/dictionary';
import { getSize, changeSize } from '../../../middlewares/pagesize';
import { clearEmpty } from '../../../lib/deal-func';
import { getListApi, addApi, editApi } from './server';

export const defaultLimit = {
  srcWarehouseId: undefined, // 出发仓
  destWarehouseId: undefined, // 目的仓
  allocationType: undefined, // 调拨类型
  transferType: undefined, // 运输方式
  enabled: [], // 状态
  isPallet: [], // 是否装托
  isQualityCheck: [], // 是否质检
  isUpper: [], // 是否上架
  isTransport: [], // 是否装车
  createTimeStart: '', // 创建时间
  createTimeEnd: '',
  isSplitBox: '', // 是否分箱
  belongSystemList: [], // 涉及的数据中心
  isAllowNonGenuineTransfer: '', // 是否允许非正品调出
};

const defaultState = {
  formRef: {}, // 绑定 form 的引用, 可以调用某些 form 的方法  validate(校验表单) 具体可以查看 https://shineout.biz.sheincorp.cn/1.6.x/cn/components/Form
  loading: 1, // 0 loading, 1 load success, 2 load fail
  selectedRows: [],
  limit: defaultLimit,
  pageInfo: {
    pageNum: 1, // 页码
    count: 0, // 表格总条数
    pageSize: getSize(),
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  list: [], // 表格数据
  // 相关下拉数据
  isList: [
    { dictCode: 0, dictNameZh: t('是') },
    { dictCode: 1, dictNameZh: t('否') },
  ], // 是否下拉数据
  enabledList: [
    { dictCode: 1, dictNameZh: t('启用') },
    { dictCode: 2, dictNameZh: t('注销') },
  ], // 是否启用数据
  warehouseList: [], // 仓库下拉数据
  statusList: [], // 调拨单状态下拉数据
  allocationTypeList: [], // 调拨类型下拉数据
  transferTypeList: [], // 运输方式下拉数据
  editObj: {
    srcWarehouseId: '',
    destWarehouseId: '',
    allocationType: '',
    transferType: '',
    enabled: '',
    isPallet: '',
    isTransport: '',
    isQualityCheck: '',
    isUpper: '',
    isSplitBox: 0, // 是否分箱
    splitBoxSkuNumFlag: 2, // 分箱SKU个数 >= <=
    splitBoxSkuNum: 0, // 分箱SKU个数
    boxNumLimit: false, // 是否箱件限制
    boxSkuNumFlag: 1, // 设置SKU箱件 >= <=
    boxSkuNum: 0, // 设置SKU箱件
    belongSystemList: [],
    isAllowNonGenuineTransfer: '', // 是否允许非正品调出
  }, // 新增/编辑弹窗数据
  showEditModal: 0, // 1新增 / 2编辑 弹窗
  recordVisible: false, // 操作记录弹窗
  recordId: '', // 操作记录弹窗
  isSplitBoxList: [
    { dictCode: 0, dictNameZh: t('否') },
    { dictCode: 1, dictNameZh: t('是') },
  ], // 是否分箱
  flagList: [
    { dictCode: 1, dictNameZh: '>=' },
    { dictCode: 2, dictNameZh: '<=' },
  ],
  boxNumLimitList: [
    { dictCode: true, dictNameZh: t('是') },
    { dictCode: false, dictNameZh: t('否') },
  ],
  warehouseBelongSystemList: [],
  isAllowNonGenuineTransferList: [
    { dictCode: 1, dictNameZh: t('是') },
    { dictCode: 2, dictNameZh: t('否') },
  ],
};

export default {
  state: defaultState,
  // 改state数据
  changeData(state, data) {
    Object.assign(state, data);
  },
  changeLimitData(state, data) {
    Object.assign(state, {
      limit: {
        ...state.limit,
        ...data,
      },
    });
  },
  changeEditObj(state, data) {
    Object.assign(state, {
      editObj: {
        ...state.editObj,
        ...data,
      },
    });
  },
  // 重置查询条件
  * clearLimitData() {
    yield this.changeLimitData({ ...defaultLimit });
  },
  * init() {
    markStatus('loading');
    // 请求下拉框数据
    const selectParam = {
      catCode: ['ALLOCATION_STATUS', 'ALLOCATION_TYPE', 'ALLOCATION_RULE_TRANSFER_TYPE', 'WAREHOUSE_BELONG_SYSTEM'],
    };
    const [warehouseData, selectData] = yield Promise.all([
      getWarehouseApi({ enabled: 1 }),
      dictSelect(selectParam),
    ]);
    // 仓库下拉数据
    if (warehouseData.code === '0') {
      yield this.changeData({
        warehouseList: warehouseData.info.data || [],
      });
    } else {
      Modal.error({ title: warehouseData.msg || t('后台数据出错') });
    }
    // 数据字典下拉数据
    if (selectData.code === '0') {
      yield this.changeData({
        statusList: selectData.info.data.find((v) => v.catCode === 'ALLOCATION_STATUS').dictListRsps,
        allocationTypeList: selectData.info.data.find((v) => v.catCode === 'ALLOCATION_TYPE').dictListRsps,
        transferTypeList: selectData.info.data.find((v) => v.catCode === 'ALLOCATION_RULE_TRANSFER_TYPE').dictListRsps,
        warehouseBelongSystemList: selectData.info.data.find((v) => v.catCode === 'WAREHOUSE_BELONG_SYSTEM').dictListRsps,
      });
    } else {
      Modal.error({ title: selectData.msg || t('后台数据出错') });
    }
  },
  // 查询
  * search() {
    markStatus('loading');
    const params = clearEmpty(this.state.limit, [0]);
    const res = yield getListApi({
      ...params,
      pageNum: this.state.pageInfo.pageNum,
      pageSize: this.state.pageInfo.pageSize,
    });
    if (res.code === '0') {
      yield this.changeData({
        list: res.info.data,
        pageInfo: {
          ...this.state.pageInfo,
          count: res.info.meta.count,
        },
        selectedRows: [], // 查询后清空原先选中的
      });
    } else {
      Modal.error({ title: res.msg || t('后台数据出错') });
    }
  },
  // 页签改变
  * handlePaginationChange(arg = {}) {
    yield this.changeData({
      pageInfo: {
        ...this.state.pageInfo,
        ...arg,
      },
    });
    if (arg.pageSize) {
      yield changeSize(arg.pageSize);
    }
    yield this.search();
  },

  // 1新增  2编辑
  * editData() {
    markStatus('loading');
    const { warehouseList, editObj } = yield '';
    const { userName } = yield select((state) => state.nav);
    const api = this.state.showEditModal === 1 ? addApi : editApi;
    const tips = this.state.showEditModal === 1 ? t('新增成功') : t('编辑成功');
    const params = {
      ...editObj,
      srcWarehouseName: warehouseList.find((v) => v.id === editObj.srcWarehouseId).nameZh,
      userName,
      isSplitBox: editObj.isSplitBox ? 1 : 0,
    };
    if (![0, 4].includes(editObj.allocationType)) {
      Reflect.deleteProperty(params, 'isAllowNonGenuineTransfer');
    }
    const res = yield api({ ...params });
    if (res.code === '0') {
      yield this.changeData({ showEditModal: 0 });
      Message.success(tips);
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: res.msg || t('后台数据出错') });
    }
  },
};
