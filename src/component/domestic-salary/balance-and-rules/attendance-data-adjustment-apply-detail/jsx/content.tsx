import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Button, Table, Modal } from 'shineout';
import FileDelayUpload from '@public-component/upload/fileDelayUpload';
import { TableColumnItem } from '@src/typing/base';
import { IDataItem, IPageProps } from '../types';
import store from '../reducers';
import style from '../style.less';

const columns: TableColumnItem<IDataItem>[] = [
  {
    title: t('序号'),
    render: (_, idx) => idx + 1,
    width: 100,
  },
  {
    title: t('调整人员工号'),
    render: 'uname',
    width: 160,
  },
  {
    title: t('调整人员'),
    render: 'userName',
    width: 140,
  },
  {
    title: t('调整日期'),
    render: 'adjustDate',
    width: 200,
  },
  {
    title: t('调整原因'),
    render: 'adjustReason',
    width: 220,
  },
  {
    title: t('星期'),
    render: 'week',
    width: 140,
  },
  {
    title: t('日历类型'),
    render: 'calendarTypeName',
    width: 140,
  },
  {
    title: t('岗位'),
    render: 'posnJbName',
    width: 160,
  },
  {
    title: t('职级'),
    render: 'supvLevelName',
    width: 120,
  },
  {
    title: t('工时制'),
    render: 'workerSystemName',
    width: 120,
  },
  {
    title: t('入职日期'),
    render: 'lastHireDate',
    width: 180,
  },
  {
    title: t('离职日期'),
    render: 'terminationDate',
    width: 180,
  },
  {
    title: t('五级部门'),
    render: 'dept5Desc',
    width: 160,
  },
  {
    title: t('六级部门'),
    render: 'dept6Desc',
    width: 160,
  },
];

function Handle(props: IPageProps) {
  const {
    dataLoading,
    detailTableData,
    detaileTableInfo,
    importModalVisible,
    file,
    currId,
    viewType,
  } = props;

  const handlePaginationChange = (page, size) => {
    store.changeData({
      detaileTableInfo: {
        ...detaileTableInfo,
        pageNum: page,
        pageSize: size,
      },
    });
    store.handleQueryRefeshDetailListById();
  };

  return (
    <div className={style.contentStyle}>
      <div className={style.detaileOprions}>
        <div>
          <Button
            type="primary"
            size="default"
            style={{ marginBottom: 10 }}
            disabled={[null, undefined, ''].includes(currId) || dataLoading === 0 || viewType === '3'}
            loading={dataLoading === 0}
            onClick={() => {
              store.changeData({
                importModalVisible: true,
              });
            }}
          >
            {t('导入明细')}
          </Button>
          <Button
            type="primary"
            size="default"
            style={{ marginBottom: 10 }}
            loading={dataLoading === 0}
            disabled={[null, undefined, ''].includes(currId) || dataLoading === 0 || viewType === '3' || !detailTableData.length}
            onClick={() => {
              Modal.confirm({
                title: t('系统提示'),
                content: t('确认清除所有导入信息？'),
                onOk: () => {
                  store.handleClearAllDetail();
                },
                text: { ok: t('确认'), cancel: t('取消') },
              });
            }}
          >
            {t('清除所有明细')}
          </Button>
          <Button
            type="primary"
            size="default"
            style={{ marginBottom: 10 }}
            loading={dataLoading === 0}
            disabled={[null, undefined, ''].includes(currId) || dataLoading === 0 || viewType === '3'}
            onClick={() => {
              store.handleQueryRefeshDetailListById();
            }}
          >
            {t('刷新明细列表')}
          </Button>
        </div>
      </div>
      <Table<IDataItem, IDataItem[]>
        data={detailTableData}
        columns={columns}
        bordered
        empty={t('暂无数据')}
        dataLoading={!dataLoading}
        keygen="id"
        pagination={{
          align: 'right',
          style: { marginTop: 10 },
          current: detaileTableInfo.pageNum,
          pageSize: detaileTableInfo.pageSize,
          layout: [({ total }) => `${t('共')} ${total} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
          onChange: handlePaginationChange,
          pageSizeList: detaileTableInfo.pageSizeList,
          total: detaileTableInfo.count,
        }}
        height={viewType === '3' ? 390 : 300}
        fixed="both"
        radio
        width={columns.reduce((pre, current) => pre + current.width, 0)}
      />

      <Modal
        maskCloseAble={null}
        visible={importModalVisible}
        title={t('导入')}
        onClose={() => {
          store.changeData({
            importModalVisible: false,
            file: '',
          });
        }}
        footer={[
          <Button
            key="cancel"
            onClick={() => {
              store.changeData({
                importModalVisible: false,
                file: '',
              });
            }}
          >
            {t('取消')}
          </Button>,
          <Button
            type="primary"
            disabled={!file || dataLoading === 0}
            loading={dataLoading === 0}
            onClick={() => {
              store.uploadFile();
            }}
          >
            {t('确认上传')}
          </Button>,
        ]}
      >
        <div style={{ padding: '20px 0', display: 'flex' }}>
          <span style={{ display: 'inline-block', marginRight: 20 }}>
            {t('选择文件')}
            :
          </span>
          <FileDelayUpload
            value={file}
            loading={dataLoading === 0}
            maxSize={5}
            onChange={(f) => {
              store.changeData({
                file: f,
              });
            }}
            accept=".xls,.xlsx"
          />
        </div>
        <div className={style.buttonStyle}>
          <Button
            type="primary"
            text
            size="default"
            disabled={dataLoading === 0}
            onClick={() => store.downloadTemplate()}
          >
            {t('考勤调整导入模板')}
          </Button>
        </div>
        <span className={style.tipsRed}>{t('注：只能导入所选四级部门人员，请确认')}</span>
      </Modal>
    </div>
  );
}

Handle.propTypes = {
  dataLoading: PropTypes.number.isRequired,
  detailTableData: PropTypes.arrayOf(PropTypes.shape),
  detaileTableInfo: PropTypes.shape(),
  importModalVisible: PropTypes.bool.isRequired,
  file: PropTypes.shape(),
  currId: PropTypes.string,
  viewType: PropTypes.string,
};

export default Handle;
