import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import {
  Button, Message, Popover, Modal,
} from 'shineout';
import UploadPlus from '@shein-components/upload_plus';
import Icon from '@shein-components/Icon';
import { uploadFileURL } from '@src/server/basic/upload';
import {
  IPageProps,
} from '../types';
import store from '../reducers';
import style from '../style.less';

function Footer(props: IPageProps) {
  const {
    detailInfo,
    detailTableData,
    currId,
    dataLoading,
    viewType,
    reAdjustModalVisible, // 重新调整弹框是否显示
    reAdjustModalMsgList, // 重新调整弹框显示信息
  } = props;

  return (
    <div className={style.contentStyle}>
      <div className={style.footerItem}>
        <span style={{ fontSize: 16, fontWeight: 500 }}>{t('附件：')}</span>
        {t('上限10个附件，单个上限50M')}
        <Icon name="pc-help-circle" className={style.cancelHelpIcon}>
          <Popover
            position="top-left"
            style={{ padding: 5, width: 300 }}
          >
            <div>{t('附件允许的格式：')}</div>
            <div>{t('图片：jpg、jpeg、png、bmp、gif')}</div>
            <div>{t('视频：mp4、mov')}</div>
            <div>{t('文档：doc/docx、xls/xlsx、ppt/pptx、pdf、zip')}</div>
          </Popover>
        </Icon>
        <div style={{ marginTop: 5 }}>
          <UploadPlus
            accept=".xls,.xlsx,.pdf,.docx,.doc,.ppt,.pptx,.zip,.jpg,.png,.jpeg,.bmp,.gif,.mp4,.mov"
            autoUpload
            action={uploadFileURL}
            fileList={detailInfo.fileList || []}
            disabled={[null, undefined, ''].includes(currId) || dataLoading === 0 || viewType === '3'}
            maxSize={50}
            limit={10}
            autoUploadKeyName="file"
            filePathKeyName="fileUrl"
            onDelete={async (_, newFileList) => {
              store.changeData({
                detailInfo: {
                  ...detailInfo,
                  fileList: newFileList,
                },
              });
            }}
            onFailUpload={async (_, info) => Message.error(info)}
            onSuccessUpload={async ({ file, info }) => {
              const newFileList = [
                ...detailInfo.fileList,
                {
                  fileName: file.name,
                  fileUrl: info.image_url,
                  name: file.name,
                },
              ];
              store.changeData({
                detailInfo: {
                  ...detailInfo,
                  fileList: newFileList,
                },
              });
            }}
          />
        </div>
      </div>
      <div className={style.backStyle}>
        <Button
          size="default"
          style={{ width: 60 }}
          onClick={() => {
            if (detailInfo?.fileList && detailInfo.fileList.length && viewType !== '3') {
              Modal.confirm({
                title: t('系统提示'),
                content: t('是否暂存当前页面编辑数据未暂存会造成数据丢失，下次编辑时需要补充'),
                onOk: () => {
                  store.handleSaveFiles(true);
                },
                onCancel: () => {
                  window.location.href = '#/domestic-salary/balance-and-rules/attendance-data-adjustment-apply';
                },
                text: { ok: t('暂存'), cancel: t('直接返回') },
              });
            } else {
              window.location.href = '#/domestic-salary/balance-and-rules/attendance-data-adjustment-apply';
            }
          }}
        >
          {t('返回')}
        </Button>
        {viewType !== '3'
        && (
        <>
          <Button
            size="default"
            style={{ width: 60 }}
            disabled={[null, undefined, ''].includes(currId) || dataLoading === 0 || !detailTableData.length}
            loading={dataLoading === 0}
            onClick={() => {
              store.handleSaveFiles();
            }}
          >
            {t('暂存')}
          </Button>
          <Button
            size="default"
            type="primary"
            disabled={[null, undefined, ''].includes(currId) || dataLoading === 0 || detailTableData.length === 0}
            loading={dataLoading === 0}
            onClick={() => {
              store.handleCommitApproval({
                submitFlag: false,
              });
            }}
          >
            {([null, undefined, ''].includes(currId) || dataLoading === 0 || detailTableData.length === 0) && (
            <Popover
              position="top"
              style={{ padding: 5 }}
            >
              {t('暂无明细数据，无需提交审批')}
            </Popover>
            )}
            {t('提交审批')}
          </Button>
        </>
        )}
      </div>
      <Modal
        maskCloseAble={null}
        visible={reAdjustModalVisible}
        title={t('系统提示')}
        onClose={() => {
          store.changeData({
            reAdjustModalVisible: false,
            reAdjustModalMsgList: [],
          });
        }}
        footer={[
          <Button
            key="cancel"
            onClick={() => {
              store.changeData({
                reAdjustModalVisible: false,
                reAdjustModalMsgList: [],
              });
            }}
          >
            {t('取消')}
          </Button>,
          <Button
            type="primary"
            loading={dataLoading === 0}
            onClick={() => {
              store.handleCommitApproval({
                submitFlag: true,
              });
            }}
          >
            {t('确认')}
          </Button>,
        ]}
      >
        <div>
          {t('以下调整，在已提交申请中存在重复，确认重新发起调整申请？')}
        </div>
        <div style={{ padding: '20px 0', maxHeight: '150px', overflowY: 'scroll' }}>
          {reAdjustModalMsgList.map((ri) => (
            <div>{ri}</div>
          ))}
        </div>
      </Modal>
    </div>
  );
}

Footer.propTypes = {
  detailInfo: PropTypes.shape(),
  detailTableData: PropTypes.arrayOf(PropTypes.shape),
  currId: PropTypes.string,
  dataLoading: PropTypes.number,
  viewType: PropTypes.string,
  reAdjustModalVisible: PropTypes.bool,
  reAdjustModalMsgList: PropTypes.arrayOf(PropTypes.shape),
};

export default Footer;
