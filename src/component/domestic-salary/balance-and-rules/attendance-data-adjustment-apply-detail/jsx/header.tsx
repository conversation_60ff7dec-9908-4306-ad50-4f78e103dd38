import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import moment from 'moment';
import {
  DatePicker, Form, Select, Rule, Button, Input,
} from 'shineout';
import classnames from 'classnames';
import {
  IPageProps,
} from '../types';
import store from '../reducers';
import style from '../style.less';

const rules = Rule();

const getDatePiackDisabled = (value) => {
  const beforeCurrentTime = moment().subtract(7, 'month');
  const currentTime = moment().subtract(1, 'month');
  const selectTime = moment(value, 'yyyyMM');
  // 选择时间不能大于 T-1
  return selectTime.isBefore(beforeCurrentTime) || selectTime.isAfter(currentTime);
};

function Header(props: IPageProps) {
  const {
    detailInfo,
    dataLoading,
    currId,
    viewType,
    dept3List,
    dept4List,
  } = props;

  return (
    <div className={classnames(style.contentStyle, style.searchHeader)}>
      <Form
        labelWidth={140}
        labelAlign="right"
        value={detailInfo}
        onChange={(val) => {
          store.changeData({
            detailInfo: val,
          });
        }}
        inline
        formRef={(f) => store.changeData({ searchForm: f })}
      >
        {viewType === '3' && (
        <Form.Item required label={t('申请账单号')}>
          <Input style={{ width: 230 }} disabled name="adjustNo" />
        </Form.Item>
        )}
        <Form.Item required label={t('申请账单月')}>
          <div className={style.formStyle}>
            <DatePicker
              name="ym"
              label={t('申请账单月')}
              disabled={(viewType !== '1' || ![null, undefined, ''].includes(currId)) ? true : (value) => getDatePiackDisabled(value)}
              width={230}
              type="month"
              placeholder={t('请选择')}
              format="YYYYMM"
              rules={[rules.required(t('请选择申请账单月'))]}
              clearable
            />
            <span style={{ marginTop: 3, color: 'red' }}>{t('当前月-6≤申请调整月≤当前月-1')}</span>
          </div>
        </Form.Item>
        <Form.Item
          required
          name="dept3_id"
          label={t('三级部门(片区)')}
          tip={t('只能查看数据权限范围内的片区数据')}
        >
          <Select
            autoAdapt
            name="dept3_id"
            data={dept3List}
            keygen="id"
            format="id"
            width={230}
            disabled={viewType !== '1' || ![null, undefined, ''].includes(currId)}
            renderItem="name"
            onFilter={(text) => (d) => d.name.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
            placeholder={t('全部')}
            onChange={(val) => {
              store.changeDetailInfo({
                dept3_id: val,
                dept4_id: '',
              });
              store.changeData({
                dept4List: [],
              });
              store.getDeptChildList({
                parentPermissionIdList: [val],
                type: 1, // 1: 四级部门;
              });
            }}
          />
        </Form.Item>
        <Form.Item required label={t('四级部门')}>
          <Select
            autoAdapt
            label={t('四级部门')}
            keygen="deptId"
            format="deptId"
            data={dept4List}
            clearable
            width={230}
            disabled={viewType !== '1' || ![null, undefined, ''].includes(currId)}
            onFilter={(text) => (d) => d.pdDescrLong.indexOf(text) >= 0}
            renderItem="pdDescrLong"
            name="dept4_id"
            rules={[rules.required(t('请选择四级部门'))]}
            placeholder={t('全部')}
          />
        </Form.Item>
      </Form>
      <Button
        type="primary"
        size="default"
        style={{ marginBottom: 10 }}
        disabled={![null, undefined, ''].includes(currId) || dataLoading === 0}
        loading={dataLoading === 0}
        onClick={() => {
          store.handleConfirmImportDetail();
        }}
      >
        {t('确认并导入明细')}
      </Button>
    </div>
  );
}

Header.propTypes = {
  detailInfo: PropTypes.shape(),
  dataLoading: PropTypes.number,
  currId: PropTypes.string,
  viewType: PropTypes.string,
  dept3List: PropTypes.arrayOf(PropTypes.shape()),
  dept4List: PropTypes.arrayOf(PropTypes.shape()),
};

export default Header;
