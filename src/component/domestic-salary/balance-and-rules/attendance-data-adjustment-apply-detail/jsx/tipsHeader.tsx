import React from 'react';
import { t } from '@shein-bbl/react';
import style from '../style.less';

function Header() {
  return (
    <div className={style.contentStyle}>
      <div className={style.headerTitle}>
        {t('O岗人员考勤结算数据调整申请')}
      </div>
      <div>{t('说明：')}</div>
      <div>{t('1、流程审批：发起人->申请调整人四级部门负责人->考勤组考勤员，只有相同四级部门人员，才可一张单批量申请单；')}</div>
      <div>
        {t('2、若流程归档时间≤当前月6日23:59:59，')}
        <span className={style.tipsRed}>{t('本月')}</span>
        {t('进行结算；若流程归档时间＞当前月7日0:0:0，随')}
        <span className={style.tipsRed}>{t('次月补发补扣')}</span>
        {t('结算；')}
      </div>
      <div>
        {t('3、申请调整月限制范围：')}
        <span className={style.tipsRed}>{t('当前月-6≤申请调整月≤当前月-1')}</span>
        {t('；')}
      </div>
    </div>
  );
}

export default Header;
