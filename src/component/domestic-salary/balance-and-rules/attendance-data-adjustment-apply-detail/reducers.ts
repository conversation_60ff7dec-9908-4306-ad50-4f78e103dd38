import { markStatus } from 'rrc-loader-helper';
import { Modal, Message } from 'shineout';
import { getSize } from '@src/middlewares/pagesize';
import { formdataPost } from '@src/server/common/fileFetch';
import { STATISTICAL_IMPORT_CENTER } from '@src/lib/jump-url';
import { t } from '@shein-bbl/react';
/* eslint-disable-next-line @typescript-eslint/ban-ts-comment */
// @ts-expect-error
import fileSaver from 'file-saver';
import assign from 'object-assign';
import {
  IDetailInfo,
  IStateType,
} from './types';
import {
  getDetailListByIdAPI,
  uploadImportURL,
  clearAllDetailAPI,
  confirmImportDetailAPI,
  saveFilesAPI,
  downloadTemplateAPI,
  commitApprovalAPI,
  queryDeptListAPI,
  queryDeptByParentAPI,
} from './server';

export const defaultDetailInfo = {
  ym: '', // 调整月份
  fileList: [], // 附件
  dept3_id: '', // 三级部门（片区）
  dept4_id: '', // 四级部门
};

const defaultState = {
  currId: '', // 当前主表id
  viewType: '1', // 1-新增 2-编辑 3-查看
  detailInfo: { ...defaultDetailInfo }, // 详情信息
  dataLoading: 1, // 0加载中 1加载成功 2加载失败
  detailTableData: [], // 明细列表
  detaileTableInfo: {
    pageNum: 1,
    pageSize: getSize(),
    pageSizeList: [10, 20, 50, 100],
  },
  allDeptList: [], // 三级部门下拉
  importModalVisible: false, // 导入弹窗
  file: '', // 导入文件
  searchForm: {}, // 搜索表单
  dept3List: [], // 三级部门下拉
  dept4List: [], // 四级部门下拉
  reAdjustModalVisible: false, // 重新调整弹框是否显示
  reAdjustModalMsgList: [], // 重新调整弹框显示信息
};

export default {
  state: defaultState,
  $init: (draft) => {
    assign(draft, { ...defaultState, searchForm: draft.searchForm });
  },
  // 页面初始化
  * init() {
    // 获取部门数据
    yield this.queryDeptList();
  },

  // 改state属性值
  changeData(state: Partial<IStateType>, data?: Partial<IStateType>) {
    Object.assign(state, data);
  },
  /**
   * 改搜索条件limit属性值
   */
  changeDetailInfo(state: Partial<IDetailInfo>, data?: Partial<IDetailInfo>) {
    // !!! LCD对于action方法有改动state实际为IState，为保证程序正常强制as一下
    const stateData = state as IStateType;
    Object.assign(stateData, {
      detailInfo: {
        ...stateData.detailInfo,
        ...data,
      },
    });
  },

  // 获取详情
  * getDetail() {
    const { currId, detaileTableInfo } = yield '';
    markStatus('dataLoading');
    const params = {
      attendanceAdjustFlowId: currId,
      pageNum: detaileTableInfo.pageNum,
      pageSize: detaileTableInfo.pageSize,
    };
    const { code, info, msg } = yield getDetailListByIdAPI(params);
    if (code === '0') {
      const newDetailInfo = info?.adjustInfo || {};
      newDetailInfo.ym = String(newDetailInfo.ym);
      newDetailInfo.fileList = info?.fileList || [];
      newDetailInfo.fileList.map((item) => {
        item.name = item.fileName;
        return item;
      });
      newDetailInfo.dept3_id = newDetailInfo?.bmStdPermissionId;
      newDetailInfo.dept4_id = newDetailInfo?.deptId;
      yield this.changeData({
        detailInfo: newDetailInfo,
        detaileTableInfo: {
          ...detaileTableInfo,
          count: info.meta.count || 0,
        },
        detailTableData: info?.adjustDetailInfoList || [],
      });
      if (newDetailInfo.dept4_id) {
        yield this.getDeptChildList({
          parentPermissionIdList: [newDetailInfo.dept3_id],
          type: 1, // 1: 四级部门;
        });
      }
    } else {
      Modal.error({ title: msg });
    }
  },

  /**
   * 下载导入模板
   * @returns {Generator<*, void, *>}
   */
  * downloadTemplate() {
    try {
      const res = yield downloadTemplateAPI();
      const b = yield res.blob();
      const blob = new Blob([b], { type: 'application/octet-stream' });
      const fileName = t('考勤调整导入模板');
      fileSaver.saveAs(blob, `${fileName}.xlsx`);
    } catch (e) {
      Modal.error({ title: e.reason?.message });
    }
  },

  /**
   * 导入文件
   */
  * uploadFile() {
    const { file, currId } = yield '';

    if (!file) {
      Message.error(t('请先选择需要导入的文件'));
      return;
    }
    markStatus('dataLoading');
    const formData = new FormData();
    formData.append('file', file);
    formData.append('request_json', JSON.stringify({ id: currId }));
    formData.append('function_node', '245');
    const res = yield formdataPost(uploadImportURL, formData);
    if (res.code === '0') {
      window.open(STATISTICAL_IMPORT_CENTER);
      yield this.changeData({
        importModalVisible: false,
        file: '',
      });
    } else {
      Modal.error({ content: res.msg });
    }
  },

  /**
   * 清除所有明细
   */
  * handleClearAllDetail() {
    const { currId } = yield '';
    markStatus('dataLoading');
    const res = yield clearAllDetailAPI({ attendanceAdjustFlowId: currId });
    if (res.code === '0') {
      yield this.getDetail();
    } else {
      Modal.error({ title: res.msg });
    }
  },

  /**
   * 确认导入明细
   */
  * handleConfirmImportDetail() {
    const { detailInfo, searchForm } = yield '';
    // 兼容跳转进来触发搜索时，dom还没渲染完，执行formRef.validate()报错
    if (!searchForm || !searchForm.validate) {
      return true;
    }
    // 校验表单
    let validateFlag = false;
    yield searchForm?.validate().then(() => {
      validateFlag = true;
    }).catch(() => {
      validateFlag = false;
    });
    if (!validateFlag) {
      return;
    }
    markStatus('dataLoading');
    // 校验通过请求
    const params = {
      dept3_id: detailInfo.dept3_id,
      dept4_id: detailInfo.dept4_id,
      ym: [undefined, null, ''].includes(detailInfo.ym) ? '' : Number(detailInfo.ym),
    };
    const { code, info, msg } = yield confirmImportDetailAPI(params);
    if (code === '0') {
      yield this.changeData({
        currId: info?.id || '',
        detailTableData: [],
      });
      yield this.getDetail();
    } else {
      Modal.error({ title: msg });
    }
  },

  /**
   * 暂存附件
   */
  * handleSaveFiles(isBack = false) {
    const { currId, detailInfo } = yield '';
    const params = {
      attendanceAdjustFlowId: currId,
      files: detailInfo?.fileList || [],
    };
    const { code, msg } = yield saveFilesAPI(params);
    if (code === '0') {
      Message.success(t('暂存成功'));
      if (isBack) {
        window.location.href = '#/domestic-salary/balance-and-rules/attendance-data-adjustment-apply?isSaveBack=1';
      }
    } else {
      Modal.error({ title: msg });
    }
  },

  /**
   * 根据id查询明细表数据
   */
  * handleQueryRefeshDetailListById() {
    const { currId, detaileTableInfo, detailInfo } = yield '';
    const params = {
      attendanceAdjustFlowId: currId,
      pageNum: detaileTableInfo.pageNum,
      pageSize: detaileTableInfo.pageSize,
    };
    const { code, info, msg } = yield getDetailListByIdAPI(params);
    if (code === '0') {
      if (info?.adjustDetailInfoList && !info?.adjustDetailInfoList.length) {
        Message.info(t('暂无数据，请先导入数据'));
      }
      yield this.changeData({
        detailTableData: info?.adjustDetailInfoList || [],
        detailInfo: {
          ...detailInfo,
        },
        detaileTableInfo: {
          ...detaileTableInfo,
          count: info.meta.count || 0,
        },
      });
    } else {
      Modal.error({ title: msg });
    }
  },

  /**
   * 提交审批
   */
  * handleCommitApproval(param) {
    const { currId, detailInfo } = yield '';
    const params = {
      id: currId,
      files: detailInfo?.fileList || [],
      ...param,
    };
    const { code, msg, info } = yield commitApprovalAPI(params);
    if (code === '0') {
      Message.success(t('提交成功'));
      setTimeout(() => {
        // 提交成功跳转到列表页
        window.location.href = '#/domestic-salary/balance-and-rules/attendance-data-adjustment-apply?isSaveBack=1';
      }, 500);
    } else if (code === 'wbms0113') {
      // 以下调整，在已提交申请中存在重复，确认重新发起调整申请？
      yield this.changeData({
        reAdjustModalVisible: true, // 重新调整弹框是否显示
        reAdjustModalMsgList: info?.data || [], // 重新调整弹框显示信息
      });
    } else if (code === '1') {
      // 申请数据已核实，确认提交审批？
      const status = yield new Promise((r) => {
        Modal.confirm({
          title: t('系统提示'),
          content: t('申请数据已核实，确认提交审批？'),
          onOk: () => r('ok'),
          onCancel: () => r('cancel'),
          onClose: () => r('cancel'),
        });
      });
      if (status === 'ok') {
        yield this.handleCommitApproval({
          ...param,
          submitFlag: true,
        });
      }
    } else {
      Modal.error({ title: msg });
    }
  },
  // 获取当前用户的片区权限
  * queryDeptList() {
    const deptRes = yield queryDeptListAPI({ type: 1 });
    if (deptRes.code === '0') {
      yield this.changeData({
        dept3List: deptRes.info.data,
      });
    } else {
      Modal.error({
        title: deptRes.msg,
      });
    }
  },
  // 获取四级部门
  * getDeptChildList(params) {
    const { type, ...others } = params || {};
    const res = yield queryDeptByParentAPI({
      dept1: 800000, // 写死
      ...others,
    });
    if (res.code === '0') {
      if (type === 1) {
        yield this.changeData({
          dept4List: res.info.data, // 四级部门下拉
        });
      }
    } else {
      Modal.error({
        title: res.msg,
      });
    }
  },
};
