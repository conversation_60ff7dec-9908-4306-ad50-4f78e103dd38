import fileFetch from '@src/server/common/fileFetch';
import { sendPostRequest } from '@src/server/common/public';

// 考勤调整导入模板
export const downloadTemplateAPI = () => fileFetch('/wbms/front/salary_attendance_adjust/import_detail/download_template', {
  method: 'POST',
  credentials: 'include',
});

// 导入接口
export const uploadImportURL = `${process.env.WGS_FRONT}/file_import/record/wbms/salary_attendance_adjust_detail_import_actuator`;

// 清除所有明细
export const clearAllDetailAPI = (param) => sendPostRequest({
  url: '/salary_attendance_adjust/clear_all_detail',
  param,
}, process.env.WBMS_FRONT);

// 确认并导入明细
export const confirmImportDetailAPI = (param) => sendPostRequest({
  url: '/salary_attendance_adjust/create_adjust_flow',
  param,
}, process.env.WBMS_FRONT);

// 根据id查询数据
export const getDetailListByIdAPI = (param) => sendPostRequest({
  url: '/salary_attendance_adjust/detail_list',
  param,
}, process.env.WBMS_FRONT);

// 暂存附件
export const saveFilesAPI = (param) => sendPostRequest({
  url: '/salary_attendance_adjust/attachment_temp_store',
  param,
}, process.env.WBMS_FRONT);

// 提交审批
export const commitApprovalAPI = (param) => sendPostRequest({
  url: '/salary_attendance_adjust/submit',
  param,
}, process.env.WBMS_FRONT);

// 片区数据权限配置-获取当前用户的片区权限
export const queryDeptListAPI = (param) => sendPostRequest({
  url: '/permission/biz/query_list',
  param,
}, process.env.WBMS_FRONT);

// 通过上级部门获取
export const queryDeptByParentAPI = (param) => sendPostRequest({
  url: '/std_dept/get_dept_by_parent',
  param,
}, process.env.WBMS_FRONT);
