.contentStyle {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;

  .detaileOprions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-right: 30px;

    .infoDisplay {
      font-size: 16px;
      font-weight: 500;
    }
  }
}

.searchHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .formStyle {
    display: flex;
    flex-direction: column;
  }
}

.headerTitle {
  font-size: 18px;
  font-weight: 700;
  color: #333;
  margin-bottom: 20px;
}

.tipsRed {
  color: #f00;
}

.detailHeader {
  font-weight: 700;
  color: #333;
  margin-bottom: 10px;
}

.footerItem {
  width: 100%;
  margin-bottom: 10px;
}

.remarkInput {
  width: calc(100% - 60px);
  display: inline-block;
}

.backStyle {
  display: flex;
  justify-content: center;
}

.buttonStyle {
  margin-bottom: 10px;
}

.cancelHelpIcon {
  color: #f00;
  font-size: 18px;
  margin-left: 10px;
}

.hidden {
  display: none;
}
.visible {
  display: block;
}
