import { IPageInfo, IViewBaseProps } from '@src/typing/base';
import { FormRef } from 'shineout/lib/Form/Props';

export interface IDataItem {
  /** 调整日期 */
  adjustDate?:string;
  /** 调整原因 */
  adjustReason?:string;
  /** 日历类型 */
  calendarTypeName?:string;
  /** 五级部门名称 */
  dept5Desc?:string;
  /** 六级部门名称 */
  dept6Desc?:string;
  /** 末次入职时间 */
  lastHireDate?:string;
  /** 岗位 */
  posnJbName?:string;
  /** 职级 */
  supvLevelName?:string;
  /** 离职时间 */
  terminationDate?:string;
  /** 调整人员工号 */
  uname?:string;
  /** 姓名 */
  userName?:string;
  /** 星期 */
  week?:string;
  /** 工时制 */
  workerSystemName?:string;
}

export interface IDetailInfo {
  ym: string, // 调整月份
  fileList: Array<{
    fileName: string;
    fileUrl: string;
    name: string;
  }>, // 附件
  dept3_id: string, // 三级部门（片区）
  dept4_id: string, // 四级部门
}
export interface IStateType {
  searchForm?: FormRef<IDetailInfo>
  currId: string;
  viewType: string;
  detailInfo: IDetailInfo
  dataLoading: number; // 0 loading, 1 load success, 2 load fail
  detailTableData: IDataItem[];
  detaileTableInfo: IPageInfo;
  importModalVisible: boolean;
  file: Blob | string;
  dept3List: {
    id: string;
    name: string
  }[];
  dept4List: {
    id: string;
    name: string
  }[];
  reAdjustModalVisible: boolean,
  reAdjustModalMsgList: Array<string>,
}

export type IPageProps = IViewBaseProps<IStateType>;
