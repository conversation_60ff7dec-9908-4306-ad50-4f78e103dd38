import React, { useEffect } from 'react';
import { i18n } from '@shein-bbl/react';
import ContainerPage from '@public-component/search-queries/container';
import store, { defaultDetailInfo } from './reducers';
import TipsHeader from './jsx/tipsHeader';
import Header from './jsx/header';
import Content from './jsx/content';
import Footer from './jsx/footer';
import style from './style.less';
import { IPageProps } from './types';

function Container(props: IPageProps) {
  const { match: { query } }: {
    id: string;
    viewType: string;
  } = props;
  useEffect(() => {
    store.init();
    const id = query?.id || '';
    const viewType = query?.viewType || '1'; // 1-新增 2-编辑 3-查看
    if (id || viewType) {
      store.changeData({
        currId: id,
        viewType,
        detailInfo: { ...defaultDetailInfo },
        detailTableData: [], // 明细列表
        detaileTableInfo: {
          pageNum: 1,
          pageSize: 50,
          pageSizeList: [10, 20, 50, 100],
          count: 0,
        },
      });
      if (![null, undefined, ''].includes(id)) store.getDetail();
    }
  }, []);

  return (
    <ContainerPage>
      <div className={query?.viewType !== '3' ? style.visible : style.hidden}>
        <TipsHeader {...props} />
      </div>
      <Header {...props} />
      <Content {...props} />
      <Footer {...props} />
    </ContainerPage>
  );
}

export default i18n(Container);
