import React from 'react';
import style from '@src/component/style.less';
import {
  Button, Modal, Popover, Radio, Table,
} from 'shineout';
import { t } from '@shein-bbl/react';
import { Link } from 'react-router-dom';
import Icon from '@shein-components/Icon';
import { TableColumnItem } from '@src/typing/base';
import styles from '../style.less';
import store from '../reducers';
import { IPageProps, IApproveLogItem } from '../types';

function Handle(props: IPageProps) {
  const {
    loading,
    tmpList,
    pageInfo,
    filterStatusCode,
    statusList, // 状态下拉
    approveLogVisible,
    approveLog,
  } = props;
  const approveLogColumns: TableColumnItem<IApproveLogItem>[] = [
    {
      title: t('节点名称'),
      render: 'taskName',
      width: 150,
    },
    {
      title: t('审批人'),
      render: 'assignee',
      width: 150,
    },
    {
      title: t('审批意见'),
      render: 'approvalMsg',
      width: 150,
    },
    {
      title: t('审核时间'),
      render: 'approvalTime',
      width: 150,
    },
  ];
  // 统计各个状态的条数
  const allStatusKindCount = (tmpList || [])?.reduce((countInfo, cur) => {
    if (countInfo[cur?.approvalStatus]) {
      countInfo[cur?.approvalStatus] += 1;
    } else {
      countInfo[cur?.approvalStatus] = 1;
    }
    return countInfo;
  }, {}) || {};

  return (
    <section className={[style.handle].join(' ')}>
      <Button
        style={{ marginLeft: '8px' }}
        type="primary"
        onClick={() => {
          window.location.href = '#/domestic-salary/balance-and-rules/attendance-data-adjustment-apply-detail?viewType=1';
        }}
      >
        {t('申请考勤调整')}
      </Button>
      <Button
        type="primary"
        loading={loading === 0}
        onClick={() => {
          store.exportData();
        }}
      >
        {t('导出')}
        <Icon name="pc-help-circle" className={styles.helpIcon}>
          <Popover
            position="top-left"
            style={{ padding: 5, width: 300 }}
          >
            <div>{t('单次导出数据上限6w，若需要导出高于上限数据量，请分批导出；')}</div>
          </Popover>
        </Icon>
      </Button>
      <Link to="/domestic-salary/balance-and-rules/settlement/settlement-data" style={{ marginLeft: '8px' }}>
        <Button
          type="warning"
          loading={loading === 0}
        >
          {t('返回')}
        </Button>
      </Link>
      <div className={styles.radioGroupList}>
        <Radio.Group
          value={filterStatusCode}
          button
          onChange={(val) => {
            const filterList = (tmpList || [])?.filter((li) => (li?.approvalStatus === val));
            store.changeData({
              filterStatusCode: val,
              list: filterList,
              pageInfo: {
                ...pageInfo,
                pageNum: 1, // 页码
                count: filterList?.length || 0,
              },
            });
          }}
        >
          {(statusList || []).map((d) => (
            <Radio key={d.dictCode} htmlValue={d.dictCode}>
              {d.dictNameZh}
              (
              {allStatusKindCount[d.dictCode] || 0}
              )
            </Radio>
          ))}
        </Radio.Group>
      </div>
      <Modal
        destroy
        width={1024}
        maskCloseAble={null}
        visible={approveLogVisible}
        title={t('审批日志')}
        bodyStyle={{ maxHeight: '550px', overflow: 'auto' }}
        onClose={() => {
          store.changeData({
            approveLogVisible: false,
          });
        }}
        footer={undefined}
      >
        <Table<IApproveLogItem, IApproveLogItem[]>
          style={{ maxHeight: '350px' }}
          rowClassName={() => style.borderInner}
          bordered
          fixed="both"
          loading={!loading}
          data={approveLog.list}
          columns={approveLogColumns}
          keygen="approvalTime"
          empty={t('暂无数据')}
          size="small"
          width={approveLogColumns.reduce((pre, current) => pre + (current.width || 100), 50)}
          pagination={undefined}
        />
      </Modal>
    </section>
  );
}

export default Handle;
