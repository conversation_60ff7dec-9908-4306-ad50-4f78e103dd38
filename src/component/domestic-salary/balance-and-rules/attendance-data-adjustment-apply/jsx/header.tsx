import React from 'react';
import { t } from '@shein-bbl/react';
import {
  DatePicker, Form, Rule, Select,
} from 'shineout';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import { formatSearchData } from '@public-component/search-queries/utils';
import type { FormRef } from 'shineout/lib/Form/Props';
import { getCloudConfig } from '@src/lib/dealFunc';
import store, { defaultLimit } from '../reducers';
import { ILimitType, IPageProps } from '../types';

const rules = Rule();
function Header(props: IPageProps) {
  const {
    loading,
    limit,
    userList,
    dept4IdListOptions,
  } = props;

  return (
    <section>
      {/* 高级搜索 */}
      <SearchAreaContainer
        value={limit}
        labelStyle={{ width: 90 }}
        searching={!loading}
        collapseOnSearch={false}
        clearUndefined={false}
        onSearch={() => {
          store.handlePaginationChange({ pageNum: 1 });
        }}
        {...getCloudConfig({ type: 'search' })}
        onChange={(val: ILimitType) => {
          // 业务需求隐藏表单就设置默认值
          store.changeLimitData(formatSearchData(defaultLimit, val));
        }}
        onClear={() => store.clearLimitData()}
        formRef={(f: FormRef<ILimitType>) => {
          store.changeData({
            formRef: f,
          });
        }}
      >
        <Form.Field
          label={t('三级部门（片区）')}
          required labelNode={(
            <>
              <span style={{ color: 'red' }}>
                *
              </span>
              {t('三级部门（片区）')}
            </>
        )}
        >
          {() => (
            <Select
              autoAdapt
              style={{ width: '200px' }}
              name="deptPermissionIdList"
              data={dept4IdListOptions}
              keygen="id"
              format="id"
              renderItem="name"
              onFilter={(text) => (d) => d.name.toLowerCase().indexOf(text.toLowerCase()) >= 0}
              clearable
              multiple
              compressed
              placeholder={t('请选择')}
              rules={[rules.required(t('请输入三级部门（片区）'))]}
            />
          )}
        </Form.Field>
        <DatePicker
          label={t('调整月份')}
          name="ym"
          max={new Date()}
          type="month"
          format="yyyyMM"
        />
        <Select
          label={t('调整人员工号')}
          keygen="uname"
          format="uname"
          data={userList}
          clearable
          onFilter={(v) => { store.handleUnameFilter(v); }}
          renderItem="fullName"
          name="uname"
          placeholder={t('请输入')}
        />

      </SearchAreaContainer>
    </section>
  );
}

export default Header;
