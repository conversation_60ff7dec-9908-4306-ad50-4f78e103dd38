import React from 'react';
import { t } from '@shein-bbl/react';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import style from '@src/component/style.less';
import {
  Button, Modal,
  Table,
} from 'shineout';
import { TableColumnItem } from '@src/typing/base';
import store from '../reducers';
import { IDataItem, IPageProps } from '../types';

function List(props: IPageProps) {
  const {
    loading,
    list,
    pageInfo,
  } = props;

  const columns: TableColumnItem<IDataItem>[] = [
    {
      title: t('调整月份'),
      render: 'ym',
      width: 130,
    },
    {
      title: t('状态'),
      render: 'approvalStatusName',
      width: 130,
    },
    {
      title: t('四级部门'),
      render: 'dept5Desc',
      width: 130,
    },
    {
      title: t('三级部门（片区)'),
      render: 'dept4Desc',
      width: 130,
    },
    {
      title: t('调整人员'),
      render: (d) => (d?.unameList || []).join('、'),
      width: 130,
    },
    {
      title: t('提交人'),
      render: 'submitUser',
      width: 130,
    },
    {
      title: t('提交时间'),
      render: 'submitTime',
      width: 130,
    },
    {
      title: t('流程编号'),
      render: 'flowNum',
      width: 130,
    },
    {
      title: t('归档时间'),
      render: 'archiveTime',
      width: 130,
    },
    {
      title: t('操作'),
      render: (d) => (
        <>
          {[0, 2].includes(Number(d?.approvalStatus)) && (
            <>
              <Button
                type="link"
                onClick={() => {
                  window.location.href = `#/domestic-salary/balance-and-rules/attendance-data-adjustment-apply-detail?id=${d?.id}&viewType=2`;
                }}
              >
                {t('编辑')}
              </Button>
              <Button
                type="primary"
                text
                onClick={() => {
                  Modal.confirm({
                    title: t(''),
                    content: t('确认作废当前申请单？'),
                    onOk: () => store.cancel({
                      id: d.id,
                    }),
                    text: { ok: t('作废'), cancel: t('取消') },
                  });
                }}
              >
                {t('作废')}
              </Button>
            </>
          )}
          {d?.flowNum && (
          <Button
            type="link"
            onClick={() => store.getApproveLog({
              id: d.id,
            })}
          >
            {t('审批日志')}
          </Button>
          )}
        </>
      ),
      width: 220,
    },
  ];
  return (
    <section className={style.tableSection}>
      <SearchAreaTable>
        <Table<IDataItem, IDataItem[]>
          style={{ height: '100%' }}
          rowClassName={() => style.borderInner}
          bordered
          fixed="both"
          loading={!loading}
          data={list}
          columns={columns}
          keygen="id"
          empty={t('暂无数据')}
          size="small"
          width={columns.reduce((pre, current) => pre + (current.width || 100), 50)}
          pagination={{
            align: 'right',
            current: pageInfo.pageNum,
            pageSize: pageInfo.pageSize,
            className: style.pagination,
            layout: [({ total }) => `${t('共')} ${total} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
            onChange: (page, size) => {
              store.handlePaginationChange({
                pageNum: page,
                pageSize: size,
              });
            },
            pageSizeList: pageInfo.pageSizeList,
            total: pageInfo.count,
          }}
        />
      </SearchAreaTable>
    </section>
  );
}

export default List;
