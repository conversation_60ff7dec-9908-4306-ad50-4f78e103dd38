import { sendPostRequest } from '@src/server/common/public';

// 搜索接口
export const getListAPI = (param) => sendPostRequest({
  url: '/salary_attendance_adjust/list',
  param,
}, process.env.WBMS_FRONT);

// 导出
export const exportListAPI = (param) => sendPostRequest({
  url: '/salary_attendance_adjust/list/export',
  param,
}, process.env.WBMS_FRONT);

// 作废
export const cancelAPI = (param) => sendPostRequest({
  url: '/salary_attendance_adjust/cancel',
  param,
}, process.env.WBMS_FRONT);

// 片区数据权限配置-获取当前用户的片区权限
export const queryDeptListAPI = (param) => sendPostRequest({
  url: '/permission/biz/query_list',
  param,
}, process.env.WBMS_FRONT);

// 查看审批日志
export const getApproveLogAPI = (param) => sendPostRequest({
  url: '/salary/attendance_adjust/approval_log',
  param,
}, process.env.WBMS_FRONT);
