import {
  IFetchResponse, IPageInfo, IViewBaseProps, IDictItem,
} from '@src/typing/base';

import { FormRef } from 'shineout/lib/Form/Props';

export interface IDataItem {
  approvalStatus?: string | number;
  /** 单据状态 */
  approvalStatusName?:string;
  /** 归档时间 */
  archiveTime?:string;
  /** 三级部门（片区名称） */
  dept4Desc?:string;
  /** 四级部门名称 */
  dept5Desc?:string;
  /** 流程编号 */
  flowNum?:string;
  /** 流程id */
  id?:number;
  /** 提交时间 */
  submitTime?:string;
  /** 提交人工号 */
  submitUser?:string;
  /** 调整人员工号 */
  unameList?:string[];
  /** 申请调整月 */
  ym?:number;
}
export interface IApproveLogItem {
  /** 审核意见 */
  approvalMsg?:string;
  /** 审核时间 */
  approvalTime?:string;
  /** 审批人 */
  assignee?:string;
  /** 审批节点 */
  taskName?:string;
}
type IData = IDataItem[];

interface IMeta {
  /** 数据记录数 */
  count?:number;
  /** 用户自定义扩展 */
  customObj?:unknown;
}

interface IResponseBodyInfo {
  /** 结果 */
  data:IData;
  /** 元数据 */
  meta:IMeta;
}

export type IGetListAPIResponse = IFetchResponse<IResponseBodyInfo>;

export interface ILimitType {
  /** 三级部门（片区编码） */
  deptPermissionIdList?:string[];
  /** 调整人员工号 */
  uname?:string;
  /** 申请调整月 */
  ym?:string;
}

export interface IStateType {
  loading: number; // 0 loading, 1 load success, 2 load fail
  limit: ILimitType;
  ready: boolean;
  list: IDataItem[];
  pageInfo: IPageInfo;
  formRef?: FormRef<ILimitType>
  userList: Array<{
    uname: string;
    hrStatus: string;
    fullName: string;
  }>
  originDeptPermissionIdList: Array<string>
  dept4IdListOptions: Array<{
    id: string;
    name: string;
  }>
  filterStatusCode: string;
  tmpList: IDataItem[]
  statusList: IDictItem[]
  approveLogVisible: boolean; // 审批日志弹框
  approveLog: {
    list: IApproveLogItem[];
  }; // 审批日志弹框列表
}

export type IPageProps = IViewBaseProps<IStateType>;
