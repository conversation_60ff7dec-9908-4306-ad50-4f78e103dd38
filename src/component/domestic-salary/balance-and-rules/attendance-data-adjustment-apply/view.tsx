import React, { useEffect } from 'react';
import { i18n } from '@shein-bbl/react';
import ContainerPage from '@public-component/search-queries/container';
import store from './reducers';
import Header from './jsx/header';
import List from './jsx/list';
import Handle from './jsx/handle';
import { IPageProps } from './types';

function Container(props: IPageProps) {
  useEffect(() => {
    store.init();
  }, []);
  return (
    <ContainerPage>
      <Header {...props} />
      <Handle {...props} />
      <List {...props} />
    </ContainerPage>
  );
}

export default i18n(Container);
