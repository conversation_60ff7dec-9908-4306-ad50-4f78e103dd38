import React from 'react';
import { t } from '@shein-bbl/react';
import {
  Modal, Table, Button, Textarea,
} from 'shineout';
import { TableColumnItem } from '@src/typing/base';
import styles from '../style.less';
import store from '../reducers';
import {
  IStateType,
  IApplyListItem,
  IAttendanceDetailOneListItem,
  IAttendanceDetailTwoListItem,
  IAttendanceDetailThreeListItem,
  IAttendanceDetailFourListItem,
} from '../types';

/**
 * O岗人员考勤结算数据调整（查看调整）信息弹框
 */
function CheckAdjustmentInfoModal({
  checkAdjustmentInfoVisible,
  checkAdjustmentInfo,
}: IStateType) {
  // 基本信息部分
  const renderBasicInfo = () => (
    <div className={styles.basicInfoSection}>
      <div className={styles.infoGrid}>
        <div className={styles.infoRow}>
          <div className={styles.infoItem}>
            <span className={styles.infoLabel}>
              {t('申请调整月')}
              ：
            </span>
            <span className={styles.infoValue}>{checkAdjustmentInfo?.ym || '--'}</span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.infoLabel}>
              {t('三级部门')}
              ：
            </span>
            <span className={styles.infoValue}>{checkAdjustmentInfo?.dept4Desc || '--'}</span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.infoLabel}>
              {t('四级部门')}
              ：
            </span>
            <span className={styles.infoValue}>{checkAdjustmentInfo?.dept5Desc || '--'}</span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.infoLabel}>
              {t('流程编号')}
              ：
            </span>
            <span className={styles.infoValue}>{checkAdjustmentInfo?.flowNum || '--'}</span>
          </div>
        </div>
      </div>
    </div>
  );

  // 申请信息
  const renderApplyInfoTable = () => {
    const columns: TableColumnItem<IApplyListItem>[] = [
      {
        title: t('序号'),
        render: (_data, _index) => _index + 1,
        width: 60,
      },
      {
        title: t('调整人员工号'),
        render: 'uname',
        width: 120,
      },
      {
        title: t('调整人员'),
        render: 'fullName',
        width: 120,
      },
      {
        title: t('调整日期'),
        render: 'adjustDate',
        width: 120,
      },
      {
        title: t('{}调整原因', <span style={{ color: 'red' }}>*</span>),
        render: 'reason',
        width: 220,
      },
      {
        title: t('星期'),
        render: 'week',
        width: 80,
      },
      {
        title: t('日历类型'),
        render: 'calendarTypeName',
        width: 100,
      },
      {
        title: t('岗位'),
        render: 'jobName',
        width: 100,
      },
      {
        title: t('职级'),
        render: 'jobLevel',
        width: 100,
      },
      {
        title: t('工时制'),
        render: 'workHoursSystem',
        width: 100,
      },
      {
        title: t('入职日期'),
        render: 'lastHireDate',
        width: 150,
      },
      {
        title: t('离职日期'),
        render: 'terminalDate',
        width: 150,
      },
      {
        title: t('五级部门'),
        render: 'dept6Desc',
        width: 120,
      },
      {
        title: t('六级部门'),
        render: 'dept7Desc',
        width: 120,
      },
    ];
    return (
      <div>
        <Table<IApplyListItem, IApplyListItem[]>
          style={{ maxHeight: '350px' }}
          bordered
          fixed="both"
          data={checkAdjustmentInfo.applyList || []}
          columns={columns}
          keygen="id"
          empty={t('暂无数据')}
          size="small"
          width={columns.reduce((pre, current) => pre + (current.width || 100), 50)}
          pagination={undefined}
        />
      </div>
    );
  };

  // 考勤信息调整-第一个明细表格
  const renderAttendanceDetailOneListTable = () => {
    const columns: TableColumnItem<IAttendanceDetailOneListItem>[] = [
      {
        title: t('班次'),
        render: 'workTimeWay',
        width: 80,
      },
      {
        title: t('班次开始时间'),
        render: 'workBeginTime',
        width: 150,
      },
      {
        title: t('班次结束时间'),
        render: 'workEndTime',
        width: 150,
      },
      {
        title: t('加班开始时间'),
        render: 'otBeginTime',
        width: 150,
      },
      {
        title: t('班次时长'),
        render: 'workHour',
        width: 120,
      },
      {
        title: t('第一段休息时间'),
        render: 'firstRest',
        width: 150,
      },
      {
        title: t('第二段休息时间'),
        render: 'secondRest',
        width: 120,
      },
      {
        title: t('休息时长'),
        render: 'restHour',
        width: 150,
      },
      {
        title: t('上班卡时间'),
        render: 'startCard',
        width: 120,
      },
      {
        title: t('上班卡状态'),
        render: 'startStatusName',
        width: 120,
      },
      {
        title: t('下班卡时间'),
        render: 'endCard',
        width: 120,
      },
      {
        title: t('下班卡状态'),
        render: 'endStatusName',
        width: 120,
      },
    ];
    return (
      <div>
        <Table<IAttendanceDetailOneListItem, IAttendanceDetailOneListItem[]>
          style={{ maxHeight: '350px' }}
          bordered
          fixed="both"
          data={checkAdjustmentInfo.attendanceDetailOneList || []}
          columns={columns}
          keygen="id"
          empty={t('暂无数据')}
          size="small"
          width={columns.reduce((pre, current) => pre + (current.width || 100), 50)}
          pagination={undefined}
        />
      </div>
    );
  };

  // 考勤信息调整-第二个明细表格
  const renderAttendanceDetailTwoListTable = () => {
    const columns: TableColumnItem<IAttendanceDetailTwoListItem>[] = [
      {
        title: t('实际出勤(天)'),
        render: 'actualAttendDay',
        width: 120,
      },
      {
        title: t('实际出勤(小时)'),
        render: 'actualAttendHour',
        width: 120,
      },
      {
        title: t('计薪出勤(天)'),
        render: 'salaryAttendDay',
        width: 120,
      },
      {
        title: t('计薪出勤(小时)'),
        render: 'salaryAttendHour',
        width: 120,
      },
      {
        title: t('迟到（次）'),
        render: 'lateNum',
        width: 120,
      },
      {
        title: t('迟到(分钟)'),
        render: 'cdsczMinute',
        width: 120,
      },
      {
        title: t('早退（次）'),
        render: 'leaveEarlyNum',
        width: 120,
      },
      {
        title: t('早退(分钟)'),
        render: 'ztscMinute',
        width: 120,
      },
      {
        title: t('漏卡登记(次)'),
        render: 'bkcs',
        width: 120,
      },
    ];
    return (
      <div>
        <Table<IAttendanceDetailTwoListItem, IAttendanceDetailTwoListItem[]>
          style={{ maxHeight: '350px' }}
          bordered
          fixed="both"
          data={checkAdjustmentInfo.attendanceDetailTwoList || []}
          columns={columns}
          rowClassName={(record, recordIndex) => (recordIndex === 1 ? styles.bright : '')}
          keygen="id"
          empty={t('暂无数据')}
          size="small"
          width={columns.reduce((pre, current) => pre + (current.width || 100), 50)}
          pagination={undefined}
        />
      </div>
    );
  };

  // 考勤信息调整-第三个明细表格
  const renderAttendanceDetailThreeListTable = () => {
    const columns: TableColumnItem<IAttendanceDetailThreeListItem>[] = [
      {
        title: t('外出(天)'),
        render: 'wcsc',
        width: 120,
      },
      {
        title: t('出差(天)'),
        render: 'ccsc',
        width: 120,
      },
      {
        title: t('旷工(天)'),
        render: 'kgscDay',
        width: 120,
      },
      {
        title: t('事假(天)'),
        render: 'sjsc',
        width: 120,
      },
      {
        title: t('实际申请事假（小时）'),
        render: 'actualSjsc',
        width: 160,
      },
      {
        title: t('病假(天)'),
        render: 'bjsc',
        width: 120,
      },
      {
        title: t('产假（天）'),
        render: 'cjsc',
        width: 120,
      },
      {
        title: t('工伤假(天)'),
        render: 'gsjsc',
        width: 120,
      },
      {
        title: t('育儿假（天）'),
        render: 'parentalLeave',
        width: 120,
      },
      {
        title: t('护理假（天）'),
        render: 'careLeave',
        width: 120,
      },
    ];
    return (
      <div>
        <Table<IAttendanceDetailThreeListItem, IAttendanceDetailThreeListItem[]>
          style={{ maxHeight: '350px' }}
          bordered
          fixed="both"
          data={checkAdjustmentInfo.attendanceDetailThreeList || []}
          columns={columns}
          rowClassName={(record, recordIndex) => (recordIndex === 1 ? styles.bright : '')}
          keygen="id"
          empty={t('暂无数据')}
          size="small"
          width={columns.reduce((pre, current) => pre + (current.width || 100), 50)}
          pagination={undefined}
        />
      </div>
    );
  };

  // 考勤信息调整-第四个明细表格
  const renderAttendanceDetailFourListTable = () => {
    const columns: TableColumnItem<IAttendanceDetailFourListItem>[] = [
      {
        title: t('调休(天)'),
        render: 'txsc',
        width: 120,
      },
      {
        title: t('年假(天)'),
        render: 'njsc',
        width: 120,
      },
      {
        title: t('婚假(天)'),
        render: 'hjsc',
        width: 120,
      },
      {
        title: t('产检假(天)'),
        render: 'cjjsc',
        width: 120,
      },
      {
        title: t('陪产假(天)'),
        render: 'pcjsc',
        width: 120,
      },
      {
        title: t('丧假(天)'),
        render: 'sangjsc',
        width: 120,
      },
      {
        title: t('哺乳假(小时)'),
        render: 'brjsc',
        width: 120,
      },
      {
        title: t('平日加班时长(小时)'),
        render: 'usualOtHour',
        width: 160,
      },
      {
        title: t('节假日加班时长(小时)'),
        render: 'festivalOtHour',
        width: 160,
      },
      {
        title: t('夜班补贴标记'),
        render: 'nightBonusFlag',
        width: 120,
      },
    ];
    return (
      <div>
        <Table<IAttendanceDetailFourListItem, IAttendanceDetailFourListItem[]>
          style={{ maxHeight: '350px' }}
          bordered
          fixed="both"
          data={checkAdjustmentInfo.attendanceDetailFourList || []}
          columns={columns}
          rowClassName={(record, recordIndex) => (recordIndex === 1 ? styles.bright : '')}
          keygen="id"
          empty={t('暂无数据')}
          size="small"
          width={columns.reduce((pre, current) => pre + (current.width || 100), 50)}
          pagination={undefined}
        />
      </div>
    );
  };

  return (
    <Modal
      destroy
      width={1200}
      maskCloseAble={null}
      visible={checkAdjustmentInfoVisible}
      title={t('O岗人员考勤结算数据调整')}
      bodyStyle={{ maxHeight: '80vh', overflow: 'auto' }}
      onClose={() => {
        store.changeData({
          checkAdjustmentInfoVisible: false,
          checkAdjustmentInfo: {},
        });
      }}
      footer={[
        <Button
          key="close"
          onClick={() => {
            store.changeData({
              checkAdjustmentInfoVisible: false,
              checkAdjustmentInfo: {},
            });
          }}
        >
          {t('返回')}
        </Button>,
      ]}
    >
      <div className={styles.checkAdjustmentInfoModal}>
        {renderBasicInfo()}
        <div>
          <div className={styles.sectionTitle}>{t('申请信息')}</div>
          {renderApplyInfoTable()}
        </div>
        <div>
          <div className={styles.sectionTitle}>{t('考勤信息调整（白色为结算系统员数据，橙色为考勤员调整数据，重算时使用调整后数据）')}</div>
          {renderAttendanceDetailOneListTable()}
          {renderAttendanceDetailTwoListTable()}
          {renderAttendanceDetailThreeListTable()}
          {renderAttendanceDetailFourListTable()}
          <div style={{ display: 'flex', margin: '10px 0' }}>
            <div style={{ flex: '0 0 80px' }}>{t('调整备注')}</div>
            <Textarea
              rows={4}
              clearable
              disabled
              value={checkAdjustmentInfo?.adjustRemark}
              placeholder={t('请输入')}
            />
          </div>
        </div>
      </div>
    </Modal>
  );
}

export default CheckAdjustmentInfoModal;
