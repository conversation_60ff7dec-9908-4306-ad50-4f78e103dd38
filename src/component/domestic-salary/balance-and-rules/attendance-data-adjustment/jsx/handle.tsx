import React from 'react';
import {
  But<PERSON>, Modal, Popover, Table,
} from 'shineout';
import { t } from '@shein-bbl/react';
import { Link } from 'react-router-dom';
import Icon from '@shein-components/Icon';
import style from '@src/component/style.less';
import { TableColumnItem } from '@src/typing/base';
import store from '../reducers';
import styles from '../style.less';
import { IPageProps, IApproveLogItem } from '../types';

const approveLogColumns: TableColumnItem<IApproveLogItem>[] = [
  {
    title: t('节点名称'),
    render: 'taskName',
    width: 150,
  },
  {
    title: t('审批人'),
    render: 'assignee',
    width: 150,
  },
  {
    title: t('审批意见'),
    render: 'approvalMsg',
    width: 150,
  },
  {
    title: t('审核时间'),
    render: 'approvalTime',
    width: 150,
  },
];
function Handle(props: IPageProps) {
  const {
    loading,
    approveLogVisible,
    approveLog,
  } = props;
  return (
    <section className={styles.handleHandle}>
      <Button
        type="primary"
        loading={loading === 0}
        onClick={() => {
          store.exportData();
        }}
      >
        {t('导出')}
        <Icon name="pc-help-circle" className={styles.helpIcon}>
          <Popover
            position="top-left"
            style={{ padding: 5, width: 300 }}
          >
            <div>{t('单次导出数据上限6w，若需要导出高于上限数据量，请分批导出；')}</div>
          </Popover>
        </Icon>
      </Button>
      <Link to="/domestic-salary/balance-and-rules/performance-config" style={{ marginLeft: '10px' }}>
        <Button
          type="warning"
          loading={loading === 0}
        >
          {t('返回')}
        </Button>
      </Link>
      <Modal
        destroy
        width={1024}
        maskCloseAble={null}
        visible={approveLogVisible}
        title={t('审批日志')}
        bodyStyle={{ maxHeight: '550px', overflow: 'auto' }}
        onClose={() => {
          store.changeData({
            approveLogVisible: false,
          });
        }}
        footer={undefined}
      >
        <Table<IApproveLogItem, IApproveLogItem[]>
          style={{ maxHeight: '350px' }}
          rowClassName={() => style.borderInner}
          bordered
          fixed="both"
          loading={!loading}
          data={approveLog.list}
          columns={approveLogColumns}
          keygen="approvalTime"
          empty={t('暂无数据')}
          size="small"
          width={approveLogColumns.reduce((pre, current) => pre + (current.width || 100), 50)}
          pagination={undefined}
        />
      </Modal>
    </section>
  );
}

export default Handle;
