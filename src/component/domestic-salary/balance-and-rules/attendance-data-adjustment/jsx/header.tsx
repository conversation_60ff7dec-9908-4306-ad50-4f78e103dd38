import React from 'react';
import { t } from '@shein-bbl/react';
import {
  DatePicker, Rule,
  Select,
} from 'shineout';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import { formatSearchData } from '@public-component/search-queries/utils';
import type { FormRef } from 'shineout/lib/Form/Props';
import { getCloudConfig } from '@src/lib/dealFunc';
import store, { defaultLimit } from '../reducers';
import { ILimitType, IPageProps } from '../types';

const rules = Rule();

function Header(props: IPageProps) {
  const {
    loading,
    limit,
    userList,
  } = props;

  return (
    <section>
      <div style={{ color: '#999DA8', paddingBottom: '5px' }}>
        {t('考勤数据调整')}
      </div>
      {/* 高级搜索 */}
      <SearchAreaContainer
        value={limit}
        {...getCloudConfig({ type: 'search' })}
        labelStyle={{ width: 90 }}
        searching={!loading}
        collapseOnSearch={false}
        clearUndefined={false}
        onSearch={() => {
          store.handlePaginationChange({ pageNum: 1 });
        }}
        onChange={(val: ILimitType) => {
          // 业务需求隐藏表单就设置默认值
          store.changeLimitData(formatSearchData(defaultLimit, val));
        }}
        onClear={() => store.clearLimitData()}
        formRef={(f: FormRef<ILimitType>) => {
          store.changeData({
            formRef: f,
          });
        }}
      >
        <DatePicker
          width={200}
          label={t('调整月份')}
          name="ym"
          type="month"
          format="yyyy-MM"
        />
        <Select
          label={t('工号')}
          keygen="uname"
          format="uname"
          data={userList}
          clearable
          onFilter={(v) => { store.handleUnameFilter(v); }}
          renderItem="fullName"
          name="uname"
          placeholder={t('请输入')}
        />
      </SearchAreaContainer>
    </section>
  );
}

export default Header;
