import React from 'react';
import { t } from '@shein-bbl/react';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import style from '@src/component/style.less';
import {
  Table, Button,
} from 'shineout';
import { TableColumnItem } from '@src/typing/base';
import store from '../reducers';
import { IDataItem, IPageProps } from '../types';

function List(props: IPageProps) {
  const {
    loading,
    list,
    pageInfo,
    selectedRows,
  } = props;

  const columns: TableColumnItem<IDataItem>[] = [
    {
      title: t('工号'),
      render: 'uname',
      width: 130,
    },
    {
      title: t('姓名'),
      render: 'fullname',
      width: 130,
    },
    {
      title: t('调整月份'),
      render: 'ym',
      width: 160,
    },
    {
      title: t('调整日期'),
      render: 'adjustDate',
      width: 160,
    },
    {
      title: t('星期'),
      render: 'week',
      width: 160,
    },
    {
      title: t('日历类型'),
      render: 'calendarTypeName',
      width: 160,
    },
    {
      title: t('调整原因'),
      render: 'adjustReason',
      width: 160,
    },
    {
      title: t('三级部门（片区）'),
      render: 'dept3Desc',
      width: 160,
    },
    {
      title: t('四级部门'),
      render: 'dept4Desc',
      width: 160,
    },
    {
      title: t('五级部门'),
      render: 'dept5Desc',
      width: 160,
    },
    {
      title: t('六级部门'),
      render: 'dept6Desc',
      width: 160,
    },
    {
      title: t('岗位'),
      render: 'posnJbName',
      width: 160,
    },
    {
      title: t('职级'),
      render: 'supvLevelName',
      width: 140,
    },
    {
      title: t('工时制'),
      render: 'workerSystemName',
      width: 140,
    },
    {
      title: t('入职日期'),
      render: 'lastHireDate',
      width: 160,
    },
    {
      title: t('离职日期'),
      render: 'terminationDate',
      width: 160,
    },
    {
      title: t('提交人'),
      render: 'submitUser',
      width: 140,
    },
    {
      title: t('提交时间'),
      render: 'submitTime',
      width: 160,
    },
    {
      title: t('流程编号'),
      render: 'flowNum',
      width: 180,
    },
    {
      title: t('审批归档时间'),
      render: 'archiveTime',
      width: 160,
    },
    {
      title: t('操作'),
      render: (d) => (
        <>
          <Button
            type="link" onClick={() => {
              store.checkAdjustment({
                id: d.id,
              });
            }}
          >
            {t('查看')}
          </Button>
          <Button
            type="primary"
            text
            onClick={() => store.getApproveLog({
              id: d.flowId,
            })}
          >
            {t('审批日志')}
          </Button>
        </>
      ),
      width: 220,
    },
  ];

  const handelRowSelect = (val: IDataItem[]) => {
    store.changeData({
      selectedRows: val,
    });
  };
  return (
    <section className={style.tableSection}>
      <SearchAreaTable>
        <Table<IDataItem, IDataItem[]>
          style={{ height: '100%' }}
          rowClassName={() => style.borderInner}
          bordered
          fixed="both"
          loading={!loading}
          data={list}
          columns={columns}
          value={selectedRows}
          onRowSelect={handelRowSelect}
          keygen="id"
          empty={t('暂无数据')}
          size="small"
          width={columns.reduce((pre, current) => pre + (current.width || 100), 50)}
          pagination={{
            align: 'right',
            current: pageInfo.pageNum,
            pageSize: pageInfo.pageSize,
            className: style.pagination,
            layout: [({ total }) => `${t('共')} ${total} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
            onChange: (page, size) => {
              store.handlePaginationChange({
                pageNum: page,
                pageSize: size,
              });
            },
            pageSizeList: pageInfo.pageSizeList,
            total: pageInfo.count,
          }}
        />
      </SearchAreaTable>
    </section>
  );
}

export default List;
