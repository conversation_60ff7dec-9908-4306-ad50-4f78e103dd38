import { markStatus } from 'rrc-loader-helper';
import { Modal } from 'shineout';
import { getSize } from '@src/middlewares/pagesize';
import { paramTrim, clearEmpty } from '@src/lib/deal-func';
import { queryByFullNameAPI } from '@src/server/wbms/server';
import {
  getListAPI,
  exportListAPI,
  getApproveLogAPI,
  checkAdjustmentAPI,
} from './server';
import { IGetListAPIResponse, ILimitType, IStateType } from './types';

export const defaultLimit: ILimitType = {
  uname: '', // 工号
  ym: '', // 调整月份
};
const defaultState: IStateType = {
  limit: defaultLimit,
  loading: 1, // 0 loading, 1 load success, 2 load fail
  list: [],
  pageInfo: {
    pageNum: 1, // 页码
    count: 0, // 表格总条数
    pageSize: getSize(),
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  formRef: undefined,
  userList: [], // 工号搜索列表
  approveLogVisible: false, // 审批日志弹框
  approveLog: {
    list: [],
  }, // 审批日志弹框列表
  checkAdjustmentInfoVisible: false, // 查看调整弹框
  checkAdjustmentInfo: {}, // 查看调整信息
  selectedRows: [],
};

export default {
  state: defaultState,
  $init: () => defaultState, // 可选使用，在页面初始化的时候会重置state
  /**
   * 改变state的值
   */
  changeData(state: Partial<IStateType>, data?: Partial<IStateType>) {
    Object.assign(state, data);
  },
  /**
   * 改搜索条件limit属性值
   */
  changeLimitData(state: Partial<ILimitType>, data?: Partial<ILimitType>) {
    // !!! LCD对于action方法有改动state实际为IState，为保证程序正常强制as一下
    const stateData = state as IStateType;
    Object.assign(stateData, {
      limit: {
        ...stateData.limit,
        ...data,
      },
    });
  },
  /**
   * 重置查询条件
   */
  * clearLimitData() {
    const { formRef }: IStateType = yield '';
    yield this.changeLimitData(defaultLimit);
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },
  // 校验
  * handleFormValidate() {
    const { formRef }: IStateType = this.state;
    let validateFlag = false;
    if (formRef) {
      yield formRef.validate().then(() => {
        validateFlag = true;
      }).catch(() => {
        validateFlag = false;
      });
    }
    return validateFlag;
  },
  /**
   * 页签改变
   * @param {*} arg
   */
  * handlePaginationChange(arg = {}) {
    const validateFlag: boolean = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    const { pageInfo } = yield '';
    yield this.changeData({
      pageInfo: {
        ...pageInfo,
        ...arg,
      },
    });
    yield this.search();
  },
  /**
   * 查询表格数据
   */
  * search() {
    const {
      pageInfo: {
        pageNum,
        pageSize,
      },
      limit,
    }: IStateType = yield '';
    markStatus('loading');

    const data: IGetListAPIResponse = yield getListAPI(clearEmpty(paramTrim({
      ...limit,
      pageNum,
      pageSize,
    })));
    if (data.code === '0') {
      yield this.changeData({
        list: data?.info?.data || [],
        selectedRows: [],
        pageInfo: {
          ...this.state.pageInfo,
          count: data?.info?.meta?.count ?? 0,
        },
      });
    } else {
      Modal.error({
        title: data.msg,
      });
    }
  },
  // 输入人名进行搜索
  * handleUnameFilter(v) {
    if (!v) { return; }
    const { code, info, msg } = /^\d/.test(v) // 判断当前输入的是否是数字开头
      ? yield queryByFullNameAPI({
        uname: v, // 工号
        pageNum: 1,
        pageSize: 50,
      }) : yield queryByFullNameAPI({
        fullName: v, // 中文名
        pageNum: 1,
        pageSize: 50,
      });
    if (code === '0') {
      yield this.changeData({ userList: info });
    } else {
      Modal.error({ title: msg });
    }
  },
  // 导出
  * exportData() {
    // 校验
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    // 校验通过后执行
    const { limit, pageInfo } = yield '';
    // 一般导出接口不用传页数页码，因为是要导出全部数据：部分旧页面有传就与旧页面一致；新页面跟后端统一按规范不用传页数页码
    // 实际开发时，根据情况决定是否加仓库id，页数页码等字段
    const param = {
      ...limit,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
    };
    markStatus('loading');
    const { code, msg } = yield exportListAPI(clearEmpty(paramTrim({ ...param }), [0]));
    if (code === '0') {
      window.open('#/statistical/download');
    } else {
      Modal.error({ title: msg });
    }
  },
  * getApproveLog(params) {
    markStatus('loading');
    const res = yield getApproveLogAPI(params);
    if (res.code === '0') {
      yield this.changeData({
        approveLogVisible: true,
        approveLog: {
          list: res?.info?.data || [],
        },
      });
    } else {
      Modal.error({ content: res.msg });
    }
  },
  // 查看调整
  * checkAdjustment(params) {
    markStatus('loading');
    const res = yield checkAdjustmentAPI(params);
    if (res.code === '0') {
      yield this.changeData({
        checkAdjustmentInfoVisible: true,
        checkAdjustmentInfo: res?.info,
      });
    } else {
      Modal.error({ content: res.msg });
    }
  },
};
