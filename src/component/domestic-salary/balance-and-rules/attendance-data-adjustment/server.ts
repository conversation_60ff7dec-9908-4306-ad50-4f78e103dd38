import { sendPostRequest } from '@src/server/common/public';

// 查询列表
export const getListAPI = (param) => sendPostRequest({
  url: '/salary_attendance_adjust_manage/list',
  param,
}, process.env.WBMS_FRONT);

// 导出接口
export const exportListAPI = (param) => sendPostRequest({
  url: '/salary_attendance_adjust_manage/export',
  param,
}, process.env.WBMS_FRONT);

// 查看调整
export const checkAdjustmentAPI = (param) => sendPostRequest({
  url: '/salary_attendance_adjust_manage/detail',
  param,
}, process.env.WBMS_FRONT);

// 查看审批日志
export const getApproveLogAPI = (param) => sendPostRequest({
  url: '/salary/attendance_adjust/approval_log',
  param,
}, process.env.WBMS_FRONT);
