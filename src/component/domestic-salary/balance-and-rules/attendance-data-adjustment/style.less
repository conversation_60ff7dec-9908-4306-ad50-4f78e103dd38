.check {
    color: green;
    font-size: 18px;
    cursor: pointer;
    width: 80%;
    margin: 0 auto;
}

.formItemWrap>div {
    display: inline-block;
    margin-top: 8px;
}
.handleHandle {
    display: flex;
    align-items: center;
    vertical-align: top;
    padding: 12px;
    background-color: #fff;
    margin: 12px 0 0 0;
    width: 100%;
}
.helpIcon {
    margin-left: 10px;
    font-size: 16px;
    color: #f90;
}

.checkAdjustmentInfoModal {
  padding: 0 16px;

  .sectionTitle {
    font-size: 16px;
    font-weight: 500;
    margin: 16px 0;
    color: #333;
  }

  .basicInfoSection {
    margin-bottom: 24px;
  }

  .infoGrid {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .infoRow {
    display: flex;
    flex-wrap: wrap;
    gap: 24px;
  }

  .infoItem {
    min-width: 200px;
  }

  .infoLabel {
    color: #666;
    margin-right: 8px;
  }

  .infoValue {
    color: #333;
    font-weight: 500;
  }

  .afterAdjustRow {
    background-color: #fff7e6;
  }
}
.bright>td {
  background: #facd91;
  height:40px;
}
.bright>td:hover {
  background: #facd91;
}
