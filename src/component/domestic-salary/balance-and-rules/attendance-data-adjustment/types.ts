import {
  IFetchResponse, IPageInfo, IViewBaseProps,
} from '@src/typing/base';
import { FormRef } from 'shineout/lib/Form/Props';

export interface IGetListAPIRequest {
  /** 补贴标准 */
  allowanceStandard?:number;
  /** 考勤项编码 */
  attendanceItem?:string;
  /** 考勤项名称 */
  attendanceItemName?:string;
  /** 入库科目id */
  costItemId?:number;
  /** 入库科目名称 */
  costItemName?:string;
  /**  新增人 */
  createUser?:string;
  /** 生效时间 */
  effectiveYm?:string;
  /** 是否有效编码（0：无效，1：有效） */
  enable?:string;
  /** 是否有效名称 */
  enableName?:string;
  /** 失效时间 */
  expiryYm?:string;
  /** id */
  id?:number;
  /** 是否指定工号，如果是指定工号，前端需要显示查看工号按钮 */
  isSpecifyUname?:boolean;
  /** 其他条件编码 */
  otherCondition?:number;
  /** 其他条件名称 */
  otherConditionName?:string;
  /** 备注 */
  remark?:string;
  /** 子科目id */
  subCostItemId?:number;
  /** 子科目名称 */
  subCostItemName?:string;
  /** 更新时间 */
  updateTime?:string;
  /**  更新人 */
  updateUser?:string;
}
export interface IAttendanceAdjustItem {
  /** 实际出勤(天) */
  actualAttendDay:number;
  /** 实际出勤(小时) */
  actualAttendHour:number;
  /** 实际申请事假（小时） */
  actualSjsc:number;
  /** 病假（天） */
  bjsc:number;
  /** 漏卡登记（次） */
  bkcs:number;
  /** 哺乳假（小时） */
  brjsc:number;
  /** 护理假（天） */
  careLeave:number;
  /** 出差（天） */
  ccsc:number;
  /** 迟到(分钟) */
  cdsczMinute:number;
  /** 产检假（天） */
  cjjsc:number;
  /** 产假（天） */
  cjsc:number;
  /** 节假日加班时长（小时） */
  festivalOtHour:number;
  /** 工伤假（天） */
  gsjsc:number;
  /** 婚假（天） */
  hjsc:number;
  /** 旷工（天） */
  kgscDay:number;
  /** 迟到（次） */
  lateNum:number;
  /** 早退（次） */
  leaveEarlyNum:number;
  /** 夜班补贴标记 */
  nightBonusFlag:string;
  /** 年假（天） */
  njsc:number;
  /** 育儿假（天） */
  parentalLeave:number;
  /** 陪产假（天） */
  pcjsc:number;
  /** 计薪出勤(天) */
  salaryAttendDay:number;
  /** 计薪出勤(小时) */
  salaryAttendHour:number;
  /** 丧假（天） */
  sangjsc:number;
  /** 事假（天） */
  sjsc:number;
  /** 调休（天） */
  txsc:number;
  /** 平日加班时长（小时） */
  usualOtHour:number;
  /** 外出（天） */
  wcsc:number;
  /** 早退（分钟） */
  ztscMinute:number;
}
export interface IApplyInfoItem {
  /** 调整日期,yyyy-MM-dd */
  adjustDate:string;
  /** 日历类型 */
  calendarTypeName:string;
  /** 五级部门名称 */
  dept6Desc:string;
  /** 六级部门名称 */
  dept7Desc:string;
  /** 员工姓名 */
  fullName:string;
  /** 职级 */
  jobLevel:string;
  /** 岗位 */
  jobName:string;
  /** 入职日期,yyyy-MM-dd */
  lastHireDate:string;
  /** 调整原因 */
  reason:string;
  /** 离职日期,yyyy-MM-dd */
  terminalDate:string;
  /** 工号 */
  uname:string;
  /** 星期 */
  week:string;
  /** 工时制 */
  workHoursSystem:string;
}
export interface IAttendanceAdjustDetailItem {
  /** 实际出勤(天) */
  actualAttendDay:number;
  /** 实际出勤(小时) */
  actualAttendHour:number;
  /** 实际申请事假（小时） */
  actualSjsc:number;
  /** 调整日期,yyyy-MM-dd */
  adjustDate:string;
  /** 病假（天） */
  bjsc:number;
  /** 漏卡登记（次） */
  bkcs:number;
  /** 哺乳假（小时） */
  brjsc:number;
  /** 护理假（天） */
  careLeave:number;
  /** 出差（天） */
  ccsc:number;
  /** 迟到(分钟) */
  cdsczMinute:number;
  /** 产检假（天） */
  cjjsc:number;
  /** 产假（天） */
  cjsc:number;
  /** 下班卡时间 */
  endCard:string;
  /** 下班卡状态 */
  endStatusName:string;
  /** 节假日加班时长（小时） */
  festivalOtHour:number;
  /** 第一段休息时间 */
  firstRest:string;
  /** 工伤假（天） */
  gsjsc:number;
  /** 婚假（天） */
  hjsc:number;
  /** 旷工（天） */
  kgscDay:number;
  /** 迟到（次） */
  lateNum:number;
  /** 早退（次） */
  leaveEarlyNum:number;
  /** 夜班补贴标记 */
  nightBonusFlag:string;
  /** 年假（天） */
  njsc:number;
  /** 开始加班时间 */
  otBeginTime:string;
  /** 育儿假（天） */
  parentalLeave:number;
  /** 陪产假（天） */
  pcjsc:number;
  /** 休息时长 */
  restHour:string;
  /** 计薪出勤(天) */
  salaryAttendDay:number;
  /** 计薪出勤(小时) */
  salaryAttendHour:number;
  /** 丧假（天） */
  sangjsc:number;
  /** 第二段休息时间 */
  secondRest:string;
  /** 事假（天） */
  sjsc:number;
  /** 上班卡时间 */
  startCard:string;
  /** 上班卡状态 */
  startStatusName:string;
  /** 调休（天） */
  txsc:number;
  /** 工号 */
  uname:string;
  /** 平日加班时长（小时） */
  usualOtHour:number;
  /** 外出（天） */
  wcsc:number;
  /** 班次开始时间 */
  workBeginTime:string;
  /** 班次结束时间 */
  workEndTime:string;
  /** 班次时长 */
  workHour:string;
  /** 班次 */
  workTimeWay:string;
  /** 早退（分钟） */
  ztscMinute:number;
}
export interface IDataItem {
  /** 调整日期 */
  adjustDate?:string;
  /** 调整原因 */
  adjustReason?:string;
  /** 归档时间 */
  archiveTime?:string;
  /** 日历类型 */
  calendarTypeName?:string;
  /** 三级部门（片区名称） */
  dept3Desc?:string;
  /** 四级部门名称 */
  dept4Desc?:string;
  /** 五级部门名称 */
  dept5Desc?:string;
  /** 六级部门名称 */
  dept6Desc?:string;
  /** 流程编号 */
  flowNum?:string;
  /** 调整人员姓名 */
  fullname?:string;
  /** 明细id */
  id?:number;
  /** 入职日期 */
  lastHireDate?:string;
  /** 岗位 */
  posnJbName?:string;
  /** 提交时间 */
  submitTime?:string;
  /** 提交人工号 */
  submitUser?:string;
  /** 职级 */
  supvLevelName?:string;
  /** 离职日期 */
  terminationDate?:string;
  /** 调整人员工号 */
  uname?:string;
  /** 星期 */
  week?:string;
  workerSystemName?:string;
  /** 调整月份 */
  ym?:number;
  flowId?: string;
}

type IData = IDataItem[];

export interface IApproveLogItem {
  /** 审核意见 */
  approvalMsg?:string;
  /** 审核时间 */
  approvalTime?:string;
  /** 审批人 */
  assignee?:string;
  /** 审批节点 */
  taskName?:string;
}

interface IMeta {
  /** 数据记录数 */
  count?:number;
  /** 用户自定义扩展 */
  customObj?:unknown;
}

interface IResponseBodyInfo {
  /** 结果 */
  data:IData;
  /** 元数据 */
  meta:IMeta;
}

export type IGetListAPIResponse = IFetchResponse<IResponseBodyInfo>;

export interface ILimitType {
  uname: string,
  ym: string,
}

export interface IAddAndEditObj {
  allowanceStandard?: string | number;
  attendanceItem?: string| number;
  costItemId?: string | number;
  effectiveYm?: string;
  expiryYm?: string;
  otherCondition?: string | number;
  remark?: string;
  subCostItemId?: string | number;
  unameList?: string[];
}
export interface IApplyListItem {
  /** 调整日期,yyyy-MM-dd */
  adjustDate:string;
  /** 日历类型 */
  calendarTypeName:string;
  /** 五级部门名称 */
  dept6Desc:string;
  /** 六级部门名称 */
  dept7Desc:string;
  /** 员工姓名 */
  fullName:string;
  /** 职级 */
  jobLevel:string;
  /** 岗位 */
  jobName:string;
  /** 入职日期,yyyy-MM-dd */
  lastHireDate:string;
  /** 调整原因 */
  reason:string;
  /** 离职日期,yyyy-MM-dd */
  terminalDate:string;
  /** 工号 */
  uname:string;
  /** 星期 */
  week:string;
  /** 工时制 */
  workHoursSystem:string;
}

type IApplyList = IApplyListItem[];

export interface IAttendanceDetailFourListItem {
  /** 哺乳假（小时） */
  brjsc:number;
  /** 产检假（天） */
  cjjsc:number;
  /** 节假日加班时长（小时） */
  festivalOtHour:number;
  /** 婚假（天） */
  hjsc:number;
  /** 夜班补贴标记 */
  nightBonusFlag:string;
  /** 年假（天） */
  njsc:number;
  /** 陪产假（天） */
  pcjsc:number;
  /** 丧假（天） */
  sangjsc:number;
  /** 调休（天） */
  txsc:number;
  /** 平日加班时长（小时） */
  usualOtHour:number;
}

type IAttendanceDetailFourList = IAttendanceDetailFourListItem[];

export interface IAttendanceDetailOneListItem {
  /** 下班卡时间 */
  endCard:string;
  /** 下班卡状态 */
  endStatusName:string;
  /** 第一段休息时间 */
  firstRest:string;
  /** 开始加班时间 */
  otBeginTime:string;
  /** 休息时长 */
  restHour:string;
  /** 第二段休息时间 */
  secondRest:string;
  /** 上班卡时间 */
  startCard:string;
  /** 上班卡状态 */
  startStatusName:string;
  /** 班次开始时间 */
  workBeginTime:string;
  /** 班次结束时间 */
  workEndTime:string;
  /** 班次时长 */
  workHour:string;
  /** 班次 */
  workTimeWay:string;
}

type IAttendanceDetailOneList = IAttendanceDetailOneListItem[];

export interface IAttendanceDetailThreeListItem {
  /** 实际申请事假（小时） */
  actualSjsc:number;
  /** 病假（天） */
  bjsc:number;
  /** 护理假（天） */
  careLeave:number;
  /** 出差（天） */
  ccsc:number;
  /** 产假（天） */
  cjsc:number;
  /** 工伤假（天） */
  gsjsc:number;
  /** 旷工（天） */
  kgscDay:number;
  /** 育儿假（天） */
  parentalLeave:number;
  /** 事假（天） */
  sjsc:number;
  /** 外出（天） */
  wcsc:number;
}

type IAttendanceDetailThreeList = IAttendanceDetailThreeListItem[];

export interface IAttendanceDetailTwoListItem {
  /** 实际出勤(天) */
  actualAttendDay:number;
  /** 实际出勤(小时) */
  actualAttendHour:number;
  /** 漏卡登记（次） */
  bkcs:number;
  /** 迟到(分钟) */
  cdsczMinute:number;
  /** 迟到（次） */
  lateNum:number;
  /** 早退（次） */
  leaveEarlyNum:number;
  /** 计薪出勤(天) */
  salaryAttendDay:number;
  /** 计薪出勤(小时) */
  salaryAttendHour:number;
  /** 早退（分钟） */
  ztscMinute:number;
}

type IAttendanceDetailTwoList = IAttendanceDetailTwoListItem[];

// 查看调整信息接口
export interface ICheckAdjustmentInfo {
  /** 调整备注 */
  adjustRemark?:string;
  /** 申请信息 */
  applyList?:IApplyList;
  /** 考勤信息调整-调休（天）列信息 */
  attendanceDetailFourList?:IAttendanceDetailFourList;
  /** 考勤信息调整-班次列信息 */
  attendanceDetailOneList?:IAttendanceDetailOneList;
  /** 考勤信息调整-外出（天）列信息 */
  attendanceDetailThreeList?:IAttendanceDetailThreeList;
  /** 考勤信息调整-实际出勤(天)列信息 */
  attendanceDetailTwoList?:IAttendanceDetailTwoList;
  /** 三级部门名称 */
  dept4Desc?:string;
  /** 四级部门名称 */
  dept5Desc?:string;
  /** 流程编号 */
  flowNum?:string;
  /** 调整月份 */
  ym?:string;
}

export interface IStateType {
  loading: number;
  limit: ILimitType;
  list: IDataItem[];
  pageInfo: IPageInfo;
  formRef?: FormRef<ILimitType>;
  userList: Array<{
    uname: string;
    hrStatus: string;
    fullName: string;
  }>
  approveLogVisible: boolean; // 审批日志弹框
  approveLog: {
    list: IApproveLogItem[];
  }; // 审批日志弹框列表
  checkAdjustmentInfoVisible: boolean; // 查看调整弹框
  checkAdjustmentInfo: ICheckAdjustmentInfo; // 查看调整信息
  selectedRows: IDataItem[]
}

export type IPageProps = IViewBaseProps<IStateType>;
