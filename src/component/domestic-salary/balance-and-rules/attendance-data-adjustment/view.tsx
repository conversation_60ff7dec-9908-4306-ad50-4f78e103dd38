import React from 'react';
import { i18n } from '@shein-bbl/react';
import ContainerPage from '@public-component/search-queries/container';
import Header from './jsx/header';
import List from './jsx/list';
import Handle from './jsx/handle';
import CheckAdjustmentInfoModal from './jsx/check-adjustment-info-modal';
import { IPageProps } from './types';

function Container(props: IPageProps) {
  return (
    <ContainerPage>
      <Header {...props} />
      <Handle {...props} />
      <List {...props} />
      <CheckAdjustmentInfoModal
        checkAdjustmentInfoVisible={props.checkAdjustmentInfoVisible}
        checkAdjustmentInfo={props.checkAdjustmentInfo}
      />
    </ContainerPage>
  );
}

export default i18n(Container);
