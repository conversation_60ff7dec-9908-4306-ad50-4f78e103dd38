import React from 'react';
import { t } from '@shein-bbl/react';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import { handleTablePros } from '@src/lib/deal-func';
import styles from '@src/component/style.less';
import { Table, Popover } from 'shineout';
import Icon from '@shein-components/Icon';
import type { ColumnItem } from 'shineout/lib/Table/Props';
import store from '../reducers';
import { IDataItem, IPageProps } from '../types';

function List(props: IPageProps) {
  const { loading, list, pageInfo } = props;
  const columns: ColumnItem<IDataItem>[] = [
    {
      title: t('账单月'),
      render: 'ym',
      width: 130,
    },
    {
      title: t('计算周期'),
      render: 'cycleName',
      width: 130,
    },
    {
      title: t('工号'),
      render: 'uname',
      width: 130,
    },
    {
      title: t('姓名'),
      render: 'fullName',
      width: 130,
    },
    {
      title: t('结算状态'),
      render: 'settleStatusName',
      width: 130,
    },
    {
      title: (
        <span>
          <Popover style={{ width: 200, padding: 20 }}>
            <div>{t('需要按顺序满足条件：')}</div>
            <div>{t('1、是否在职=在职')}</div>
            <div>{t('2、出勤是否达标=是 ')}</div>
            <div>{t('3、满足排名前40%=是 ')}</div>
            <div>{t('4、异常激励单数=0')}</div>
          </Popover>
          {t('奖励是否有效')}
          <Icon
            name="pc-alert-shineout"
            style={{ color: '#39f', marginLeft: '5px', fontSize: 16 }}
          />
        </span>
      ),
      render: 'enabledName',
      width: 180,
      // @ts-expect-error keygen
      keygen: t('奖励是否有效'),
    },
    {
      title: t('OA岗位'),
      render: 'oaPosnJbName',
      width: 130,
    },
    {
      title: t('环节'),
      render: 'deptPartTypeName',
      width: 130,
    },
    {
      title: t('三级部门(片区)'),
      render: 'dept4Name',
      width: 130,
    },
    {
      title: t('四级部门'),
      render: 'dept5Name',
      width: 130,
    },
    {
      title: (
        <span>
          <Popover style={{ width: 200, padding: 20 }}>
            <div>
              <div>{t('1、使用五级部门查询【排名子仓配置】，存在子仓配置时，读取“子仓名称”，写入“仓库”字段，使用仓库维度进行排名')}</div>
              <div>{t('2、使用五级部门没有查寻到子仓配置时，“仓库”字段为空，使用四级部门维度进行排名')}</div>
            </div>
          </Popover>
          {t('五级部门')}
          <Icon
            name="pc-alert-shineout"
            style={{ color: '#39f', marginLeft: '5px', fontSize: 16 }}
          />
        </span>
      ),
      render: 'dept6Name',
      width: 180,
      // @ts-expect-error keygen
      keygen: t('五级部门'),
    },
    {
      title: t('仓库'),
      render: 'rankDeptName',
      width: 130,
    },
    {
      title: (
        <span>
          <Popover style={{ width: 200, padding: 20 }}>
            <div>{t('通过离职日期，結合计算时间判断')}</div>
            <div>{t('【上半月】离职=离职日期＜账单月16号：在职=离职日期≥账单月16号。【下半月】离职=离职日期＜账单月次月1号：在职=离职日期≥账单月次月1号。')}</div>
            <div>{t('【下半月】离职=离职日期＜账单月次月1号：在职=离职日期≥账单月次月1号。')}</div>
          </Popover>
          {t('离职日期')}
          <Icon
            name="pc-alert-shineout"
            style={{ color: '#39f', marginLeft: '5px', fontSize: 16 }}
          />
        </span>
      ),
      render: 'terminationDate',
      width: 180,
      // @ts-expect-error keygen
      keygen: t('离职日期'),
    },
    {
      title: (
        <span>
          <Popover style={{ width: 200, padding: 20 }}>
            <div>{t('上半月：实际出勤天:考勤日报中，该人员考核月1->15日（含15日）合计[实际出勤-天]应出勤天：考核月到1->15日应出勤天数（所有人一致）')}</div>
            <div>{t('下半月：实际出勤天:考勤日报中，该人员考核月16->月末最后天日，合计[实际出勤-天]应出勤天：考核月16->月末最后天日，应出勤天数（所有人一致）')}</div>
          </Popover>
          {t('是否在职')}
          <Icon
            name="pc-alert-shineout"
            style={{ color: '#39f', marginLeft: '5px', fontSize: 16 }}
          />
        </span>
      ),
      render: 'hrStatusName',
      width: 180,
      // @ts-expect-error keygen
      keygen: t('是否在职'),
    },
    {
      title: t('实际出勤天/应出勤天'),
      render: 'attendDay',
      width: 160,
    },
    {
      title: t('出勤是否达标'),
      render: 'attendReachStandardName',
      width: 130,
    },
    {
      title: t('产能'),
      render: 'production',
      width: 180,
    },
    {
      title: t('产能排名'),
      render: 'rankPosition',
      width: 180,
    },
    {
      title: t('排名前40%'),
      render: 'rankReachStandardName',
      width: 180,
    },
    {
      title: t('异常激励单数量'),
      render: 'exceptionRewardCount',
      width: 180,
    },
    {
      title: t('归档时间'),
      render: 'archiveTime',
      width: 180,
    },
    {
      title: t('计算时间'),
      render: 'calTime',
      width: 180,
    },
  ];

  return (
    <section className={styles.tableSection}>
      <SearchAreaTable>
        <Table
          {...handleTablePros(columns)}
          loading={!loading}
          data={list}
          keygen="id"
          pagination={{
            align: 'right',
            current: pageInfo.pageNum,
            pageSize: pageInfo.pageSize,
            layout: [
              ({ total }) => `${t('共')} ${String(total)} ${t('条')}`,
              'links',
              'list',
              () => t('跳至'),
              'jumper',
              () => t('页'),
            ],
            onChange: (page, size) => {
              store.handlePaginationChange({
                pageNum: page,
                pageSize: size,
              });
            },
            className: styles.pagination,
            pageSizeList: pageInfo.pageSizeList,
            total: pageInfo.count,
          }}
        />
      </SearchAreaTable>
    </section>
  );
}

export default List;
