import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { getSize } from '@src/middlewares/pagesize';
// 搜索区域：搜索导出条件，若有仓库则使用nav右上角的仓库
export const defaultLimit = {
  formName: '',
};
// 入库单价页面 20 盘点单价页面 18 集货单价页面 13 播种单价页面 1
// 打包单价页面 7 南沙上架页面 21 发货单价页面 8 南沙合包页面 12 装车单价 29
const formOptions = [
  {
    title: t('绩效排名'),
    subList: [
      {
        title: t('拣货异常率得分表'),
        href: '/domestic-salary/balance-and-rules/performance-rank?type=2',
      },
      {
        title: t('拣货排名系数表'),
        href: '/domestic-salary/balance-and-rules/performance-rank?type=3',
      },
      {
        title: t('上架异常率得分表'),
        href: '/domestic-salary/balance-and-rules/performance-rank?type=0',
      },
      {
        title: t('上架排名系数表'),
        href: '/domestic-salary/balance-and-rules/performance-rank?type=1',
      },
      {
        title: t('新人阶段达成系数'),
        href: '/domestic-salary/balance-and-rules/newcomer-achieve',
      },
      {
        title: t('通用排名系数表'),
        href: '/domestic-salary/balance-and-rules/performance-rank?type=4',
      },
    ],
  },
  {
    title: t('个人计件'),
    subList: [
      // {
      //   title: t('入库单价表'),
      //   href: '/domestic-salary/balance-and-rules/individual-piece-count?type=20',
      // },
      // {
      //   title: t('盘点单价表'),
      //   href: '/domestic-salary/balance-and-rules/individual-piece-count?type=18',
      // },
      // {
      //   title: t('集货单价表'),
      //   href: '/domestic-salary/balance-and-rules/individual-piece-count?type=13',
      // },
      // {
      //   title: t('播种单价表'),
      //   href: '/domestic-salary/balance-and-rules/individual-piece-count?type=1',
      // },
      // {
      //   title: t('打包单价表'),
      //   href: '/domestic-salary/balance-and-rules/individual-piece-count?type=7',
      // },
      // {
      //   title: t('发货单价表'),
      //   href: '/domestic-salary/balance-and-rules/individual-piece-count?type=8&searchWarehouseId=1', // searchWarehouseId: 1 为佛山仓
      // },
      {
        title: t('南沙合包单价'),
        href: '/domestic-salary/balance-and-rules/individual-piece-count?type=12&searchWarehouseId=98', // searchWarehouseId: 1 为南沙退货中心仓,
      },
      // {
      //   title: t('南沙上架单价'),
      //   href: '/domestic-salary/balance-and-rules/individual-piece-count?type=21',
      // },
      {
        title: t('南沙发货单价'),
        href: '/domestic-salary/balance-and-rules/individual-piece-count?type=8&searchWarehouseId=98', // searchWarehouseId: 1 为南沙退货中心仓,
      },
      {
        title: t('佛山计件单价表'),
        href: '/domestic-salary/balance-and-rules/settlement/foshan-unit-price',
      },
      {
        title: t('线下产能单价表'),
        href: '/domestic-salary/balance-and-rules/settlement/salary-count-piece-price-offline',
      },
      // {
      //   title: t('发货单价'),
      //   href: '/domestic-salary/balance-and-rules/individual-piece-count?type=29',
      // },

    ],
  },
  {
    title: t('平均绩效'),
    subList: [
      // {
      //   title: t('绩效取值路径配置表'),
      //   href: '/domestic-salary/balance-and-rules/performance-average',
      // },
      {
        title: t('佛山部门平均表'),
        href: '/domestic-salary/balance-and-rules/foshan-average',
      },
      {
        title: t('南沙部门平均表'),
        href: '/domestic-salary/balance-and-rules/nansha-average',
      },
    ],
  },
  // {
  //   title: t('小组计件'),
  //   subList: [
  //   ],
  // },
  {
    title: t('临时工'),
    subList: [
      {
        title: t('临时工单价'),
        href: '/domestic-salary/balance-and-rules/temporary-worker/temporary-worker-unitprice',
      },
      {
        title: t('劳务考核得分'),
        href: '/domestic-salary/balance-and-rules/temporary-worker/labor-assessment-score',
      },
      {
        title: t('撤场名单'),
        href: '/domestic-salary/balance-and-rules/temporary-worker/withdrawal-list',
      },
    ],
  },
  {
    title: t('福利补贴'),
    subList: [
      {
        title: t('餐补信息'),
        href: '/domestic-salary/balance-and-rules/meal-allowance-info',
      },
      {
        title: t('推荐奖'),
        href: '/domestic-salary/balance-and-rules/recommendation-award',
      },
      {
        title: t('住宿信息'),
        href: '/domestic-salary/balance-and-rules/accommodation-info',
      },
      {
        title: t('资产赔偿找回'),
        href: '/domestic-salary/balance-and-rules/asset-recovery',
      },
    ],
  },
  {
    title: t('其他'),
    subList: [
      {
        title: t('个人支援计件配置表'),
        href: '/domestic-salary/balance-and-rules/support-piece-count',
      },
      {
        title: t('算薪人事花名册'),
        href: '/domestic-salary/balance-and-rules/salary-calculation-personnel-roster',
      },
      // {
      //   title: t('岗位环节映射表'),
      //   href: '/domestic-salary/balance-and-rules/position-link-map-table',
      // },
      {
        title: t('考勤相关补贴'),
        href: '/domestic-salary/balance-and-rules/attendance-related-subsidies',
      },
      {
        title: t('劳务费负账'),
        href: '/domestic-salary/salary-laborfee-negative',
      },
      {
        title: t('WMS架构与子仓映射'),
        href: '/domestic-salary/basic-info/salary-wms-architecture',
      },
    ],
  },
  {
    title: t('结算数据调整'),
    subList: [
      {
        title: t('考勤数据调整'),
        href: '/domestic-salary/balance-and-rules/attendance-data-adjustment',
      },
    ],
  },
];
const defaultState = {
  searchWarehouseId: undefined, // 仓库
  formRef: {}, // 绑定 form 的引用, 可以调用某些 form 的方法  validate(校验表单) 具体可以查看 https://shineout.biz.sheincorp.cn/1.6.x/cn/components/Form
  // 搜索区域+下拉数据
  loading: 1, // 0加载中 1加载成功 2加载失败
  topSearch: true,
  limit: defaultLimit,
  // 列表区域+分页区域
  pageInfo: {
    pageNum: 1,
    pageSize: getSize(),
    count: 0, // 表格总条数
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  list: formOptions, // 表格数据
  selectedRows: [], // 选中行数据
};

export default {
  state: defaultState,
  // 改state属性值
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件即state.limit属性值
  changeLimitData(state, data) {
    Object.assign(state.limit, data);
  },
  // 改搜索条件新增/编辑对象属性值
  changeEdit: (state, data) => {
    Object.assign(state.editObj, data);
  },
  // 重置查询条件
  * clearLimitData() {
    const { formRef } = yield '';
    yield this.changeLimitData(defaultLimit);
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },
  // 页面初始化
  * init() {
    markStatus('loading');
    yield this.search();
  },
  /**
   * 搜索
   */
  * search() {
    const {
      limit: {
        formName,
      },
    } = yield '';
    markStatus('loading');
    // eslint-disable-next-line no-promise-executor-return
    yield new Promise((resolve) => setTimeout(resolve, 500)); // 增加查询反馈感
    const newList = formOptions.reduce((acc, { title, subList }) => {
      const matchedSubItem = subList.filter((subItem) => subItem.title.includes(formName));
      if (matchedSubItem.length > 0) {
        acc.push({ title, subList: matchedSubItem });
      } else if (title.includes(formName)) {
        acc.push({ title, subList });
      }
      return acc;
    }, []);
    yield this.changeData({
      list: newList,
    });
  },

  /**
   * 校验
   * @returns
   */
  * handleFormValidate() {
    const { formRef } = yield '';
    // 兼容跳转进来触发搜索时，dom还没渲染完，执行formRef.validate()报错
    if (!formRef || !formRef.validate) {
      return true;
    }
    let validateFlag = false;
    yield formRef.validate().then(() => {
      validateFlag = true;
    }).catch(() => {
      validateFlag = false;
    });
    return validateFlag;
  },

  /**
   * 分页条改变
   * @param {object} data 分页数组主要有pageNum pageSize
   * @returns
   */
  * handlePaginationChange() {
    // 校验
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    yield this.search();
  },
};
