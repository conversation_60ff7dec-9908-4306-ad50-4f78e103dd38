import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import {
  Button, Switch, Popover, Modal, Form, Select, Input, Rule, DatePicker,
} from 'shineout';
import { Link } from 'react-router-dom';
import moment from 'moment';
import DataExportReason from '@src/component/wbms-public-component/data-export-reason';
import style from '../style.less';
import store from '../reducers';

const rule = new Rule();

class Handle extends React.Component {
  render() {
    const {
      loading,
      showSensitiveData,
      showSensitiveDataBtn,
      editModalObj,
      editModalVisible,
      posnJbOptions,
      syncJobTransferDateModalVisible,
      dataExportReasonModalVisible,
    } = this.props;
    // 检查当前日期是否在1号到7号之间
    function isDateInFirstWeek() {
      // 获取当前日期
      const currentDate = new Date();
      const currentDay = currentDate.getDate();

      // 判断日期是否在1至7号之间
      return currentDay >= 1 && currentDay <= 7;
    }
    return (
      <section className={style.handleHandle}>
        <Button
          type="primary"
          loading={loading === 0}
          onClick={() => {
            store.changeData({
              dataExportReasonModalVisible: true,
            });
          }}
        >
          {t('导出')}
        </Button>
        <Link to="/domestic-salary/balance-and-rules/performance-config" style={{ marginLeft: '8px' }}>
          <Button
            type="warning"
            loading={loading === 0}
          >
            {t('返回')}
          </Button>
        </Link>
        <Button
          style={{ marginLeft: '8px' }}
          type="primary"
          loading={loading === 0}
          disabled={!isDateInFirstWeek()}
          onClick={() => {
            store.changeData({
              syncJobTransferDateModalVisible: true,
            });
          }}
        >
          {t('更新转岗时间')}
        </Button>
        {/* 查看加密信息 */}
        <div style={{ marginRight: 10, float: 'inline-end' }}>
          <span style={{ verticalAlign: 'middle', marginRight: 8 }}>{t('查看加密信息')}</span>
          <Switch
            value={Boolean(showSensitiveData)}
            disabled={!loading || !showSensitiveDataBtn}
            onChange={(val) => {
              store.changeData({
                showSensitiveData: val,
              });
              store.search();
            }}
          />
          {!showSensitiveDataBtn && (
          <Popover
            position="top-right"
            type="warning"
          >
            {t('暂无权限查看敏感信息')}
          </Popover>
          )}
        </div>

        {/* 编辑弹窗 */}
        <Modal
          maskCloseAble={null}
          visible={editModalVisible}
          width={750}
          title={`${editModalObj.uname} - ${editModalObj.userName}`}
          destroy
          onClose={() => {
            store.handleCloseEditModal();
          }}
          footer={(
            <div>
              <Button onClick={() => store.handleCloseEditModal()}>{t('取消')}</Button>
              <Modal.Submit disabled={!loading}>{t('确认')}</Modal.Submit>
            </div>
          )}
        >
          <Form
            labelWidth={100}
            labelAlign="right"
            style={{ maxWidth: 750 }}
            inline
            onSubmit={() => {
              store.handleEditModify();
            }}
            onChange={(value) => {
              store.changeData({
                editModalObj: value,
              });
            }}
            value={editModalObj}
            formRef={(f) => store.changeData({ editModalFormRef: f })}
          >
            <Form.Item required label={t('岗位')}>
              <Select
                autoAdapt
                name="posnJb"
                data={posnJbOptions}
                keygen="posnJb"
                format="posnJb"
                renderItem="posnJbName"
                width={200}
                onFilter={(text) => (d) => d.posnJbName.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                clearable
                placeholder={t('请选择')}
                rules={[rule.required(t('请选择岗位'))]}
              />
            </Form.Item>
            <Form.Item required label={t('转岗时间')}>
              <DatePicker
                absolute
                label={t('转岗时间')}
                name="jobTransferDate"
                type="datetime"
                format="yyyy-MM-DD"
                style={{ width: 200 }}
                rules={[rule.required(t('请选择转岗时间'))]}
              />
            </Form.Item>
            <Form.Item required label={t('最低工资')}>
              <Input.Number
                name="minWage"
                allowNull
                hideArrow
                digits={2}
                min={0}
                width={200}
                placeholder={t('请输入')}
                rules={[rule.required(t('请输入最低工资'))]}
                clearable
              />
            </Form.Item>
            <Form.Item required label={t('基本工资')}>
              <Input.Number
                name="baseWage"
                allowNull
                hideArrow
                digits={2}
                min={-100000}
                max={100000}
                width={200}
                placeholder={t('请输入')}
                rules={[rule.required(t('请输入基本工资'))]}
                clearable
              />
            </Form.Item>
            <Form.Item required label={t('岗位工资')}>
              <Input.Number
                name="jobAllowance"
                allowNull
                hideArrow
                digits={2}
                min={-100000}
                max={100000}
                width={200}
                placeholder={t('请输入')}
                rules={[rule.required(t('请输入岗位工资'))]}
                clearable
              />
            </Form.Item>
            <Form.Item required label={t('职级津贴')}>
              <Input.Number
                name="supvLevelAllowance"
                allowNull
                hideArrow
                digits={2}
                min={-100000}
                max={100000}
                width={200}
                placeholder={t('请输入')}
                rules={[rule.required(t('请输入职级津贴'))]}
                clearable
              />
            </Form.Item>
            {/* 非敏感词 */}
            <Form.Item required label={t('绩效基数')}>
              <Input.Number
                name="performanceBase"
                allowNull
                hideArrow
                digits={2}
                min={-100000}
                max={100000}
                width={200}
                placeholder={t('请输入')}
                // 非敏感词
                rules={[rule.required(t('请输入绩效基数'))]}
                clearable
              />
            </Form.Item>
            {/* 非敏感词 */}
            <Form.Item required label={t('病假最低工资')}>
              <Input.Number
                name="sickMinWage"
                allowNull
                hideArrow
                digits={2}
                min={-100000}
                max={100000}
                width={200}
                placeholder={t('请输入')}
              // 非敏感词
                rules={[rule.required(t('请输入病假最低工资'))]}
                clearable
              />
            </Form.Item>
            <Form.Item required label={t('备注')}>
              <Input
                label={t('备注')}
                name="remark"
                required
                placeholder={t('请输入')}
                width={526}
                maxLength={200}
                clearable
                rules={[rule.required(t('请输入备注'))]}
              />
            </Form.Item>
            <Form.Item label={t('结算标记')}>
              <Switch
                label={t('结算标记')}
                name="settlementTag"
                width={200}
              />
            </Form.Item>
          </Form>
        </Modal>
        <Modal
          maskCloseAble={null}
          visible={syncJobTransferDateModalVisible}
          width={450}
          title={t('转岗时间同步')}
          destroy
          onClose={() => {
            store.changeData({
              syncJobTransferDateModalVisible: false,
            });
          }}
          footer={(
            <div>
              <Button onClick={() => store.changeData({
                syncJobTransferDateModalVisible: false,
              })}
              >
                {t('取消')}
              </Button>
              <Modal.Submit disabled={!loading}>{t('确认')}</Modal.Submit>
            </div>
          )}
        >
          <Form
            labelWidth={40}
            labelAlign="right"
            style={{ maxWidth: 750 }}
            inline
            onSubmit={() => {
              store.syncJobTransferDate({
                ym: moment().subtract(1, 'months').format('YYYYMM'),
              });
            }}
          >
            <Form.Item label={t('账期')}>
              <Input value={moment().subtract(1, 'months').format('YYYYMM')} disabled />
            </Form.Item>
            <p style={{ color: 'gray' }}>{t('对指定期间花名册更新生效时间并重算考勤月报')}</p>
          </Form>
        </Modal>
        <DataExportReason
          onSubmit={(data) => {
            store.exportData(data);
          }}
          onClose={() => {
            store.changeData({
              dataExportReasonModalVisible: false,
            });
          }}
          visible={dataExportReasonModalVisible}
        />
      </section>
    );
  }
}

Handle.propTypes = {
  loading: PropTypes.number.isRequired,
  showSensitiveData: PropTypes.bool.isRequired,
  showSensitiveDataBtn: PropTypes.bool.isRequired,
  editModalObj: PropTypes.shape().isRequired,
  editModalVisible: PropTypes.bool.isRequired,
  posnJbOptions: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  syncJobTransferDateModalVisible: PropTypes.bool.isRequired,
  dataExportReasonModalVisible: PropTypes.bool,
};

export default Handle;
