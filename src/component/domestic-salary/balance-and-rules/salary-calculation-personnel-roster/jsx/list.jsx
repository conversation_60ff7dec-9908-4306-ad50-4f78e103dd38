import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Table, Button, Popover } from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import { handleTablePros } from '@src/lib/deal-func';
import globalStyles from '@src/component/style.less';
import OperationModal from '@public-component/modal/operation-modal';
import ShowMore from '@public-component/other/show-more';
import store from '../reducers';
// 表格行选中
// const rowSelectHandle = (rows) => {
//   store.changeData({ selectedRows: rows });
// };
class List extends React.Component {
  render() {
    const {
      loading,
      list,
      selectedRows,
      pageInfo,
      recordVisible,
      recordId,
      showSensitiveData,
    } = this.props;
    const columns = [
      {
        title: t('三级部门'),
        width: 140,
        render: 'dept3Desc',
      },
      {
        title: t('四级部门'),
        render: 'dept4Desc',
        width: 140,
      },
      {
        title: t('五级部门'),
        render: 'dept5Desc',
        width: 140,
      },
      {
        title: t('六级部门'),
        render: 'dept6Desc',
        width: 140,
      },
      {
        title: t('工号'),
        render: 'uname',
        width: 140,
      },
      {
        title: t('姓名'),
        render: 'userName',
        width: 120,
      },
      {
        title: t('是否临时工'),
        render: 'isCasualDesc',
        width: 100,
      },
      {
        title: t('公司'),
        render: 'companyDescr',
        width: 160,
      },
      {
        title: t('费用所属公司'),
        render: 'costCompanyDescr',
        width: 160,
      },
      {
        title: t('员工类型'),
        render: 'emplClassName',
        width: 120,
      },
      {
        title: t('入职时间'),
        render: 'lastHireDate',
        width: 180,
      },
      {
        title: t('离职时间'),
        render: 'terminationDate',
        width: 180,
      },
      {
        title: t('转岗时间'),
        render: 'jobTransferDate',
        width: 180,
      },
      {
        title: t('离职原因'),
        render: 'epLeaveReason',
        width: 180,
      },
      {
        title: t('离职子原因'),
        render: 'epLeaveReasonSub',
        width: 180,
      },
      {
        title: t('环节'),
        render: 'deptPartTypeName',
        width: 120,
      },
      {
        title: t('岗位'),
        render: 'posnJbName',
        width: 120,
      },
      {
        title: t('职级'),
        render: 'supvLevelName',
        width: 120,
      },
      {
        // 已处理敏感词
        title: t('底薪'),
        render: 'basicSalary',
        width: 120,
        needThousandsPoint: true,
      },
      {
        // 已处理敏感词
        title: t('基本工资'),
        render: 'baseWage',
        width: 120,
        needThousandsPoint: true,
      },
      {
        // 已处理敏感词
        title: t('调薪前基本工资'),
        render: 'oldBaseWage',
        width: 140,
        needThousandsPoint: true,
      },
      {
        // 已处理敏感词
        title: t('岗位工资'),
        render: 'jobAllowance',
        width: 120,
        needThousandsPoint: true,
      },
      {
        // 已处理敏感词
        title: t('调薪前岗位工资'),
        render: 'oldJobAllowance',
        width: 140,
        needThousandsPoint: true,
      },
      {
        // 已处理敏感词
        title: t('职级津贴'),
        render: 'supvLevelAllowance',
        width: 120,
        needThousandsPoint: true,
      },
      {
        // 已处理敏感词
        title: t('调薪前职级津贴'),
        render: 'oldSupvLevelAllowance',
        width: 140,
        needThousandsPoint: true,
      },
      {
        // 非敏感词
        title: t('绩效基数'),
        render: 'performanceBase',
        width: 120,
      },
      {
        // 已处理敏感词
        title: t('日薪'),
        render: 'daySalaryInfo',
        width: 120,
        needThousandsPoint: true,
      },
      {
        // 已处理敏感词
        title: t('最低工资'),
        render: 'minWage',
        width: 160,
        needThousandsPoint: true,
      },
      {
        title: t('病假最低工资'),
        render: 'sickMinWage',
        width: 160,
        needThousandsPoint: true,
      },
      {
        title: t('实际长期工作城市'),
        render: 'locationName',
        width: 160,
      },
      {
        title: t('工时制'),
        width: 140,
        render: 'workerSystemName',
      },
      {
        title: t('生效月份'),
        render: 'yearMonth',
        width: 160,
      },
      {
        title: t('临班信息'),
        render: 'tempPosnInfo',
        width: 160,
      },
      {
        title: t('当月应出勤天'),
        render: 'shouldAttendDay',
        width: 160,
      },
      {
        title: t('结算标记'),
        render: 'settlementTagName',
        width: 160,
      },
      {
        title: t('备注'),
        render: (r) => <ShowMore text={r.remark} isText />,
        width: 160,
      },
      {
        title: t('操作'),
        width: 160,
        fixed: 'right',
        render: (record) => (
          <div>
            <Button
              text
              type="primary"
              disabled={loading === 0 || !showSensitiveData}
              onClick={() => {
                store.handleEdit(record);
              }}
            >
              {!showSensitiveData && (
              <Popover>
                {t('请先点击查看加密信息')}
              </Popover>
              )}
              {t('编辑')}
            </Button>
            <Button
              text
              disabled={loading === 0}
              type="primary"
              onClick={() => store.changeData({ recordVisible: true, recordId: record.id })}
            >
              {t('操作日志')}
            </Button>
          </div>
        ),
      },
    ];

    return (
      <section className={globalStyles.tableSection}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen="id"
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              className: globalStyles.pagination,
              layout: [({ total }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
            value={selectedRows}
            // onRowSelect={rowSelectHandle}
          />
        </SearchAreaTable>

        {/* 操作记录 */}
        <OperationModal
          visible={recordVisible}
          param={{
            operateId: recordId,
            operateCode: 'SALARY_USER_ROSTER_LOG',
          }}
          onCancel={() => store.changeData({ recordVisible: false })}
        />
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number.isRequired,
  list: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  selectedRows: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  pageInfo: PropTypes.shape(),
  limit: PropTypes.shape(),
  recordVisible: PropTypes.bool.isRequired,
  recordId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  showSensitiveData: PropTypes.bool.isRequired,
};

export default List;
