import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { Modal, Message } from 'shineout';
import { getSize } from '@src/middlewares/pagesize';
import { clearEmpty } from '@src/lib/deal-func';
import { checkUrlPermissionAPI } from '@src/server/common/common';
import moment from 'moment';
import {
  getListAPI, exportListAPI, queryByFullNameAPI, getSensitiveDataListAPI, querySalaryPsonJbAPI, handleModifyAPI,
  syncJobTransferDateAPI,
} from './server';

// 搜索区域：搜索导出条件，若有仓库则使用nav右上角的仓库
export const defaultLimit = {
  ym: moment().format('YYYYMM'),
  uname: '', // 工号
  isCasual: '', // 是否临时工
};

const defaultEditObj = {
  posnJb: '', // 岗位
  jobTransferDate: '', // 转岗日期
  minWage: '', // 最低工资
  remark: '', // 备注
  baseWage: '', // 底薪
  // 非敏感词
  performanceBase: '', // 绩效基数
  settlementTag: 2, // 结算标记，1-结算；2-不结算
  jobAllowance: '',
  supvLevelAllowance: '',
  sickMinWage: '',
};

const defaultState = {
  formRef: {}, // 绑定 form 的引用, 可以调用某些 form 的方法  validate(校验表单) 具体可以查看 https://shineout.biz.sheincorp.cn/1.6.x/cn/components/Form
  // 搜索区域+下拉数据
  loading: 1, // 0加载中 1加载成功 2加载失败
  topSearch: true,
  limit: defaultLimit,
  // 列表区域+分页区域
  pageInfo: {
    pageNum: 1,
    pageSize: getSize(),
    count: 0, // 表格总条数
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  list: [], // 表格数据
  selectedRows: [], // 选中行数据
  showSensitiveData: false, // 是否显示加密信息
  showSensitiveDataBtn: false, // 是否有查看敏感品信息按钮
  recordVisible: false, // 查看记录弹窗
  recordId: '', // 查看记录id
  editModalObj: { ...defaultEditObj }, // 新增/编辑对象
  editModalVisible: false, // 新增/编辑弹窗
  posnJbOptions: [], // 岗位下拉
  editModalFormRef: {}, // 编辑form引用
  isCasualList: [ // 是否临时工
    {
      value: '1',
      label: t('是'),
    },
    {
      value: '0',
      label: t('否'),
    },
  ],
  syncJobTransferDateModalVisible: false, // 转岗时间同步弹框
  dataExportReasonModalVisible: false, // 导出弹框是否显示
};

export default {
  state: defaultState,
  // 改state属性值
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件即state.limit属性值
  changeLimitData(state, data) {
    Object.assign(state.limit, data);
  },
  // 改搜索条件新增/编辑对象属性值
  changeEdit: (state, data) => {
    Object.assign(state.editObj, data);
  },
  // 编辑导入
  changeImport: (state, data) => {
    Object.assign(state.importObj, data);
  },
  // 重置查询条件
  * clearLimitData() {
    const { formRef } = yield '';
    yield this.changeLimitData(defaultLimit);
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },
  // 页面初始化
  * init() {
    // 请求下拉框数据
    markStatus('loading');
    // 检查是否有查看敏感品信息权限
    yield this.checkPerssion();
    // 获取岗位下拉
    yield this.getPosnJbOptions();
    yield this.search();
  },
  /**
   * 搜索
   */
  * search() {
    const { limit, pageInfo, showSensitiveData } = yield '';
    const param = {
      ...limit,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
    };
    markStatus('loading');
    let res = {};
    if (showSensitiveData) {
      res = yield getSensitiveDataListAPI(clearEmpty(param, [0]));
    } else {
      res = yield getListAPI(clearEmpty(param, [0]));
    }
    const { code, info, msg } = res;
    if (code === '0') {
      yield this.changeData({
        // 有复选框的，要重置对应字段成空数组
        list: info.data,
        pageInfo: {
          ...pageInfo,
          count: info.meta.count,
        },
        selectedRows: [],
      });
    } else {
      Modal.error({ title: msg });
    }
  },

  /**
   * 校验
   * @returns
   */
  * handleFormValidate() {
    const { formRef } = yield '';
    // 兼容跳转进来触发搜索时，dom还没渲染完，执行formRef.validate()报错
    if (!formRef || !formRef.validate) {
      return true;
    }
    let validateFlag = false;
    yield formRef.validate().then(() => {
      validateFlag = true;
    }).catch(() => {
      validateFlag = false;
    });
    return validateFlag;
  },

  /**
   * 分页条改变
   * @param {object} data 分页数组主要有pageNum pageSize
   * @returns
   */
  * handlePaginationChange(data = {}) {
    // 校验
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    // 校验通过后执行
    const { pageInfo } = yield '';
    yield this.changeData({
      pageInfo: {
        ...pageInfo,
        ...data,
      },
    });
    yield this.search();
  },

  // 导出
  * exportData(params) {
    // 校验
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    // 校验通过后执行
    const { limit } = yield '';
    // 一般导出接口不用传页数页码，因为是要导出全部数据：部分旧页面有传就与旧页面一致；新页面跟后端统一按规范不用传页数页码
    // 实际开发时，根据情况决定是否加仓库id，页数页码等字段
    const param = {
      ...limit,
      pageNum: 1,
      pageSize: 100000,
      ...params,
    };
    markStatus('loading');
    const { code, msg } = yield exportListAPI(param);
    if (code === '0') {
      window.open('#/statistical/download');
    } else {
      Modal.error({ title: msg });
    }
  },

  // 输入人名进行搜索
  * handleUnameFilter(v) {
    if (!v) { return; }
    const { code, info, msg } = /^\d/.test(v) // 判断当前输入的是否是数字开头
      ? yield queryByFullNameAPI({
        uname: v, // 工号
        pageNum: 1,
        pageSize: 50,
      }) : yield queryByFullNameAPI({
        fullName: v, // 中文名
        pageNum: 1,
        pageSize: 50,
      });
    if (code === '0') {
      yield this.changeData({ userList: info });
    } else {
      Modal.error({ title: msg });
    }
  },

  /**
   * 检查是否有查看敏感品信息权限
   */
  * checkPerssion() {
    const res = yield checkUrlPermissionAPI({ url: `${process.env.WBMS_FRONT}/salary_user_roster/query_list_v2` });
    yield this.changeData({
      showSensitiveDataBtn: res.code === '0',
    });
  },
  /**
   * 编辑
   */
  * handleEdit(row) {
    yield this.changeData({
      editModalVisible: true,
      editModalObj: {
        ...row,
        settlementTag: Number(row.settlementTag) === 1,
      },
    });
  },

  /**
   * 关闭编辑弹窗
   */
  * handleCloseEditModal() {
    const { editModalFormRef } = yield '';
    if (editModalFormRef && editModalFormRef.clearValidate) editModalFormRef.clearValidate();

    yield this.changeData({
      editModalVisible: false,
      editModalObj: { ...defaultEditObj },
    });
  },

  /**
   * 获取岗位下拉
   */
  * getPosnJbOptions() {
    const { code, info, msg } = yield querySalaryPsonJbAPI({});
    if (code === '0') {
      yield this.changeData({ posnJbOptions: info.data });
    } else {
      Modal.error({ title: msg });
    }
  },

  /**
   * 算薪人事花名册 - 编辑
   */
  * handleEditModify() {
    const { editModalObj } = yield '';
    const params = {
      id: editModalObj.id,
      posnJb: editModalObj.posnJb,
      jobTransferDate: editModalObj.jobTransferDate,
      minWage: editModalObj.minWage,
      remark: editModalObj.remark,
      baseWage: editModalObj.baseWage,
      performanceBase: editModalObj.performanceBase,
      settlementTag: editModalObj.settlementTag ? 1 : 2,
      jobAllowance: editModalObj.jobAllowance,
      supvLevelAllowance: editModalObj.supvLevelAllowance,
      sickMinWage: editModalObj?.sickMinWage,
    };

    const { code, msg } = yield handleModifyAPI(params);

    if (code === '0') {
      yield this.handleCloseEditModal();
      yield this.handlePaginationChange({ pageNum: 1 });
      Message.success(t('保存成功'));
    } else {
      Modal.error({ title: msg });
    }
  },
  /**
   * 算薪人事花名册-更新转岗时间
   */
  * syncJobTransferDate(params) {
    const { code, msg } = yield syncJobTransferDateAPI(params);
    if (code === '0') {
      yield this.changeData({ syncJobTransferDateModalVisible: false });
      yield this.handlePaginationChange({ pageNum: 1 });
      Message.success(t('操作成功'));
    } else {
      Modal.error({ title: msg });
    }
  },
};
