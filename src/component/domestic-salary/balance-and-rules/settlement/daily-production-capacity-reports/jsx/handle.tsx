import React from 'react';
import { t } from '@shein-bbl/react';
import {
  Button, Modal,
} from 'shineout';
import { Link } from 'react-router-dom';
import Icon from '@shein-components/Icon';
import FileDelayUpload from '@public-component/upload/fileDelayUpload';
import store from '../reducers';

interface IPageProps {
  loading: number;
  file: string | File | null;
  importModalVisible: boolean;
}

function Handle(props: IPageProps) {
  const {
    loading,
    file,
    importModalVisible,
  } = props;

  return (
    <section style={{ padding: '10px 0', display: 'flex', gap: '8px' }}>
      <Link to="/domestic-salary/balance-and-rules/settlement/settlement-data" style={{ marginLeft: '8px' }}>
        <Button
          type="warning"
          loading={loading === 0}
        >
          <Icon name="pc-return" style={{ marginRight: 6 }} />
          {t('返回')}
        </Button>
      </Link>
      <Button
        type="primary"
        loading={loading === 0}
        onClick={() => {
          store.exportData();
        }}
      >
        {t('导出')}
      </Button>
      <Button
        type="primary"
        onClick={() => {
          store.changeData({
            importModalVisible: true,
            file: '',
          });
        }}
      >
        {t('产能调整')}
      </Button>
      <Modal
        maskCloseAble={null}
        visible={importModalVisible}
        title={t('产能调整导入')}
        onClose={() => {
          store.changeData({
            importModalVisible: false,
          });
        }}
        footer={[
          <Button
            key="cancel"
            onClick={() => {
              store.changeData({
                importModalVisible: false,
              });
            }}
          >
            {t('取消')}
          </Button>,
          <Button
            type="primary"
            disabled={!file}
            loading={!loading}
            onClick={() => {
              const formData = new FormData();
              formData.append('file', file as File);
              store.uploadFile(formData);
              return false;
            }}
          >
            {t('确认上传')}
          </Button>,
        ]}
      >
        <div style={{ padding: '20px 0', display: 'flex' }}>
          <span style={{ display: 'inline-block', marginRight: 20 }}>
            {t('选择文件')}
            :
          </span>
          <div>
            <FileDelayUpload
              value={file}
              loading={!loading}
              onChange={(f: string | File) => {
                store.changeData({
                  file: f,
                });
              }}
              accept=".xls,.xlsx"
              maxSize={50}
            />
            <div style={{ color: '#999', fontSize: '12px', marginTop: '12px' }}>
              {t('支持格式：.xls, .xlsx')}
            </div>
            <div>
              <Button
                type="primary"
                text
                onClick={() => {
                  store.downloadTemplate();
                }}
              >
                {t('下载模板')}
              </Button>
            </div>
          </div>
        </div>
      </Modal>
    </section>
  );
}

export default Handle;
