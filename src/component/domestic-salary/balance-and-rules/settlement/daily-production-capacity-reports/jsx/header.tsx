import React from 'react';
import { t } from '@shein-bbl/react';
import { Rule, Select } from 'shineout';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import { formatSearchData } from '@public-component/search-queries/utils';
import DateRangePicker from '@shein-components/dateRangePicker2';
import store, { defaultLimit } from '../reducers';
import { IPageProps } from '../types';

const rule = Rule();

function Header(props: IPageProps) {
  const {
    limit,
    loading,
    userList,
    posnJbList,
    actualPosnJbList,
    settleStatusList,
  } = props;

  return (
    <section>
      <SearchAreaContainer
        clearUndefined={false}
        // labelStyle={{ width: 60 }} // 统一修改label宽度，默认60
        searching={!loading}
        value={limit}
        // 保留用户所选项，并补全搜索条件
        onChange={(val) => store.changeLimitData(formatSearchData(defaultLimit, val))}
        onClear={() => store.clearLimitData()}
        onSearch={() => {
          // 点搜索按钮，将页码重置为1
          store.handlePaginationChange({ pageNum: 1 });
        }}
        formRef={(f) => store.changeData({ formRef: f })}
        alwaysVisible={[t('日期')]}
      >
        <Select
          autoAdapt
          label={t('产能岗位')}
          keygen="dictNameEn"
          format="dictNameEn"
          data={posnJbList}
          clearable
          multiple
          compressed
          onFilter={(text: string) => (d) => d.dictNameZh.indexOf(text) >= 0}
          renderItem="dictNameZh"
          name="posnJb"
          placeholder={t('请选择')}
          style={{ width: 240 }}
        />
        <Select
          autoAdapt
          label={t('OA岗位')}
          keygen="posnJb"
          format="posnJb"
          data={actualPosnJbList}
          clearable
          multiple
          compressed
          onFilter={(text: string) => (d) => d.posnJbName.indexOf(text) >= 0}
          renderItem="posnJbName"
          name="actualPosnJb"
          placeholder={t('请选择')}
          style={{ width: 240 }}
        />
        <Select
          autoAdapt
          label={t('工号')}
          keygen="uname"
          format="uname"
          data={userList}
          style={{ width: 240 }}
          clearable
          onFilter={(v: string) => { store.handleUnameFilter(v); }}
          renderItem="fullName"
          name="uid"
          placeholder={t('请输入')}
        />
        <Select
          autoAdapt
          label={t('结算状态')}
          keygen="dictCode"
          format="dictCode"
          data={settleStatusList}
          clearable
          multiple
          compressed
          onFilter={(text: string) => (d) => d.dictNameZh.indexOf(text) >= 0}
          renderItem="dictNameZh"
          name="settleStatus"
          placeholder={t('请选择')}
          style={{ width: 240 }}
        />
        <DateRangePicker
          required
          placeholder={[t('开始时间'), t('结束时间')]}
          type="datetime"
          inputable
          format="yyyy-MM-dd"
          name={['beginDay', 'endDay']}
          defaultTime={['00:00:00', '23:59:59']}
          label={t('日期')}
          span={2}
          rules={[rule.required]}
        />
      </SearchAreaContainer>
    </section>
  );
}

export default Header;
