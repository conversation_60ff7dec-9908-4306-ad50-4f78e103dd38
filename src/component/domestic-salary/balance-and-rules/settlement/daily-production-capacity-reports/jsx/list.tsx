import React from 'react';
import { t } from '@shein-bbl/react';
import { Table } from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import { handleTablePros } from '@src/lib/deal-func';
import styles from '@src/component/style.less';
import store from '../reducers';
import { IPageProps, IListItem } from '../types';

function List(props: IPageProps) {
  const {
    loading,
    list,
    selectedRows,
    pageInfo,
  } = props;

  const columns = [
    {
      title: t('ID'),
      render: 'id',
      width: 140,
    },
    {
      title: t('工号'),
      render: 'uname',
      width: 140,
    },
    {
      title: t('姓名'),
      render: 'userName',
      width: 180,
      fixed: 'left',
    },
    {
      title: t('仓库'),
      render: 'warehouseName',
      width: 140,
    },
    {
      title: t('子仓'),
      render: 'subWarehouseName',
      width: 140,
    },
    {
      title: t('月份'),
      render: 'yearMonth',
      width: 140,
    },
    {
      title: t('操作日期'),
      render: 'operateDate',
      width: 180,
    },
    {
      title: t('OA岗位'),
      width: 140,
      render: 'actualPosnJbName',
    },
    {
      title: t('业务大类'),
      render: 'businessBigCategoryName',
      width: 140,
    },
    {
      title: t('产能岗位'),
      render: 'posnJbName',
      width: 120,
    },
    {
      title: t('业务子类'),
      render: 'businessSubCategoryName',
      width: 140,
    },
    {
      title: t('类型'),
      render: 'businessTypeName',
      width: 140,
    },
    {
      title: t('品类'),
      render: 'shppChnlName',
      width: 140,
    },
    {
      title: t('维度'),
      render: 'operateUnitName',
      width: 140,
    },
    {
      title: t('产能'),
      render: 'productionOriginal',
      width: 140,
    },
    {
      title: t('调整产能'),
      render: 'adjustProduction',
      width: 140,
    },
    {
      title: t('结算产能'),
      render: 'production',
      width: 140,
    },
    {
      title: t('排班耗时(小时)'),
      render: 'normalTimeHour',
      width: 140,
    },
    {
      title: t('加班耗时(小时)'),
      render: 'otTimeHour',
      width: 140,
    },
    {
      title: t('本岗标记'),
      render: (r: IListItem) => (Number(r.originFlag) === 1 ? t('是') : t('否')),
      width: 120,
    },
    {
      title: t('支援计件标记'),
      render: (r: IListItem) => (Number(r.supportFlag) === 1 ? t('是') : t('否')),
      width: 120,
    },
    {
      title: t('备注'),
      render: 'remark',
      width: 160,
    },
    {
      title: t('更新时间'),
      render: 'lastUpdateTime',
      width: 180,
    },
    {
      title: t('调度任务号'),
      render: 'taskCode',
      width: 180,
    },
    {
      title: t('调度任务类型'),
      render: 'planTaskTpName',
      width: 180,
    },
    {
      title: t('结算状态'),
      render: 'statusName',
      width: 180,
    },
    {
      title: t('调整说明'),
      render: 'adjustRemark',
      width: 180,
    },
  ];

  const isEmpty = (value) => {
    let showValue = value;
    if ([null, undefined, ''].includes(value)) {
      showValue = '--';
    }
    return showValue;
  };

  const getTotalInfo = () => {
    let totalInfo = '';
    if (list && list.length > 0) {
      const customObj = pageInfo.customObj || {};
      totalInfo = <div className={styles.paginationLeft}>{t('总产能：{}', isEmpty(customObj?.production))}</div>;
    }
    return totalInfo;
  };

  return (
    <section className={[styles.tableSection, styles.listArea].join(' ')}>
      <SearchAreaTable>
        <Table
          {...handleTablePros(columns)}
          loading={!loading}
          data={list}
          keygen="id"
          pagination={{
            align: 'right',
            current: pageInfo.pageNum,
            pageSize: pageInfo.pageSize,
            className: styles.pagination,
            layout: [() => getTotalInfo(), ({ total }: { total: number }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
            onChange: (page: number, size: number) => {
              store.handlePaginationChange({
                pageNum: page,
                pageSize: size,
              });
            },
            pageSizeList: pageInfo.pageSizeList,
            total: pageInfo.count,
          }}
          value={selectedRows}
        />
      </SearchAreaTable>
    </section>
  );
}

export default List;
