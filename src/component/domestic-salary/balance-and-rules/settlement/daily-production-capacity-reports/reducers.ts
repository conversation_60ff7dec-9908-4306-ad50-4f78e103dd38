import { markStatus } from 'rrc-loader-helper';
import { Modal } from 'shineout';
import { getSize } from '@src/middlewares/pagesize';
import { clearEmpty } from '@src/lib/deal-func';
import { dictSelect } from '@src/server/basic/dictionary';
import { handleListMsg } from '@src/lib/dealFunc';
import moment from 'moment';
import fileSaver from 'file-saver';
import { t } from '@shein-bbl/react';
import { formdataPost } from '@src/server/common/fileFetch';
import { STATISTICAL_IMPORT_CENTER } from '@src/lib/jump-url';
import { queryByFullNameAPI, querySalaryPsonJbAPI } from '@src/server/wbms/server';
import {
  getListAPI, exportListAPI, downloadTemplateAPI, uploadImportURL,
} from './server';
import { ILimitType, IStateType } from './types';

// 搜索区域：搜索导出条件，若有仓库则使用nav右上角的仓库
export const defaultLimit: ILimitType = {
  uid: '', // 工号（即uid）
  beginDay: moment().subtract(6, 'days').format('YYYY-MM-DD 00:00:00'),
  endDay: moment().format('YYYY-MM-DD 23:59:59'),
  posnJb: [], // 产能岗位 (string[])
  actualPosnJb: [], // OA岗位 (string[])
  settleStatus: [], // 结算状态 (number[])
};

const defaultState: IStateType = {
  warehouseId: undefined, // 仓库
  formRef: undefined, // 绑定 form 的引用, 可以调用某些 form 的方法  validate(校验表单) 具体可以查看 https://shineout.biz.sheincorp.cn/1.6.x/cn/components/Form
  // 搜索区域+下拉数据
  loading: 1, // 0加载中 1加载成功 2加载失败
  topSearch: true,
  limit: defaultLimit,
  // 列表区域+分页区域
  pageInfo: {
    pageNum: 1,
    pageSize: getSize(),
    count: 0, // 表格总条数
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  list: [], // 表格数据
  count: 0, // 表格总条数
  selectedRows: [], // 选中行数据
  userList: [], // 用户下拉数据
  // 新增下拉数据
  posnJbList: [], // 产能岗位下拉
  actualPosnJbList: [], // OA岗位下拉
  settleStatusList: [], // 结算状态下拉
  // 导入导出相关
  importModalVisible: false, // 导入模态框显示状态
  file: '', // 上传文件
};

export default {
  state: defaultState,
  // 改state属性值
  changeData(state: IStateType, data: Partial<IStateType>) {
    Object.assign(state, data);
  },
  // 改搜索条件即state.limit属性值
  changeLimitData(state: IStateType, data: Partial<ILimitType>) {
    Object.assign(state.limit, data);
  },

  // 重置查询条件
  * clearLimitData() {
    const { formRef } = yield '';
    yield this.changeLimitData(defaultLimit);
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },
  // 页面初始化
  * init() {
    const [selectData, posnData] = yield Promise.all([
      dictSelect({
        catCode: ['WBMS_SALARY_DICT_BUSINESS_JOB', 'WBMS_SALARY_PRODUCTION_SETTLE_STATUS'],
      }),
      querySalaryPsonJbAPI({}),
    ]);
    if (selectData.code === '0') {
      yield this.changeData({
        settleStatusList: selectData.info.data.find((x) => x.catCode === 'WBMS_SALARY_PRODUCTION_SETTLE_STATUS')?.dictListRsps || [],
        posnJbList: selectData.info.data.find((x) => x.catCode === 'WBMS_SALARY_DICT_BUSINESS_JOB')?.dictListRsps || [],
        actualPosnJbList: posnData.info?.data || [],
      });
    } else {
      handleListMsg([selectData], false);
    }
  },
  /**
   * 搜索
   */
  * search() {
    const { limit, pageInfo } = yield '';
    const param = {
      ...limit,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
    };
    markStatus('loading');
    const { code, info, msg } = yield getListAPI(clearEmpty(param, [0]));
    if (code === '0') {
      yield this.changeData({
        // 有复选框的，要重置对应字段成空数组
        list: info.data,
        pageInfo: {
          ...pageInfo,
          count: info.meta.count,
          customObj: info.meta?.customObj || {},
        },
        selectedRows: [],
      });
    } else {
      Modal.error({ title: msg });
    }
  },

  /**
   * 校验
   * @returns
   */
  * handleFormValidate() {
    const { formRef } = yield '';
    // 兼容跳转进来触发搜索时，dom还没渲染完，执行formRef.validate()报错
    if (!formRef || !formRef.validate) {
      return true;
    }
    let validateFlag = false;
    yield formRef.validate().then(() => {
      validateFlag = true;
    }).catch(() => {
      validateFlag = false;
    });
    return validateFlag;
  },

  /**
   * 分页条改变
   * @param {object} data 分页数组主要有pageNum pageSize
   * @returns
   */
  * handlePaginationChange(data) {
    // 校验
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    // 校验通过后执行
    const { pageInfo } = yield '';
    yield this.changeData({
      pageInfo: {
        ...pageInfo,
        ...data,
      },
    });
    yield this.search();
  },
  // 输入人名进行搜索
  * handleUnameFilter(v: string) {
    if (!v) { return; }
    const { code, info, msg } = /^\d/.test(v) // 判断当前输入的是否是数字开头
      ? yield queryByFullNameAPI({
        uname: v, // 工号
        pageNum: 1,
        pageSize: 50,
      }) : yield queryByFullNameAPI({
        fullName: v, // 中文名
        pageNum: 1,
        pageSize: 50,
      });
    if (code === '0') {
      yield this.changeData({ userList: info });
    } else {
      Modal.error({ title: msg });
    }
  },
  // 导出
  * exportData() {
    // 校验
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    // 校验通过后执行
    const { limit, pageInfo } = yield '';
    const param = {
      ...limit,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
    };
    markStatus('loading');
    const { code, msg } = yield exportListAPI(param);
    if (code === '0') {
      window.open('#/statistical/download');
    } else {
      Modal.error({ title: msg });
    }
  },
  // 下载模板
  * downloadTemplate() {
    try {
      const res = yield downloadTemplateAPI({});
      const b = yield res.blob();
      const blob = new Blob([b], { type: 'application/octet-stream' });
      fileSaver.saveAs(blob, `${t('导入模板')}.xlsx`);
    } catch (e) {
      Modal.error({ title: e.reason.message });
    }
  },
  // 导入文件
  * uploadFile(form: FormData) {
    markStatus('loading');
    form.append('function_node', '247'); // 产能调整导入功能节点
    const res = yield formdataPost(uploadImportURL, form);
    if (res.code === '0') {
      yield this.changeData({
        importModalVisible: false,
        file: '',
      });
      window.open(STATISTICAL_IMPORT_CENTER);
    } else {
      Modal.error({ title: res.msg });
    }
  },

};
