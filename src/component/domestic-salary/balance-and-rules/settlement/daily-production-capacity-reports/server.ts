import { sendPostRequest } from '@src/server/common/public';

import fileFetch from '@src/server/common/fileFetch';
import { camel2Under } from '@src/lib/camal-case-convertor';

// 列表查询接口
export const getListAPI = (param) => sendPostRequest({
  url: '/salary_production/day/query',
  param,
}, process.env.WBMS_FRONT);

// 导出
export const exportListAPI = (param) => sendPostRequest({
  url: '/salary_production/day/export',
  param,
}, process.env.WBMS_FRONT);

// 模板下载
export const downloadTemplateAPI = (params) => {
  const uri = `${process.env.WBMS_FRONT}/salary_production/day/download_template`;
  return fileFetch(uri, {
    method: 'POST',
    credentials: 'include',
    body: JSON.stringify(camel2Under(params)),
  });
};

// 导入接口
export const uploadImportURL = `${process.env.WGS_FRONT}/file_import/record/wbms/salary_production_day_adjust`;
