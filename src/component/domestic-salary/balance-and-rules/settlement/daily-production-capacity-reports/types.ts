import {
  IFetchResponse, IPageInfo, IViewBaseProps,
} from '@src/typing/base';
import { FormRef } from 'shineout/lib/Form/Props';

export interface IGetListAPIRequest {
  /** 工号 */
  uid?: string;
  /** 开始日期 */
  beginDay?: string;
  /** 结束日期 */
  endDay?: string;
  /** 产能岗位 */
  posnJb?: string[];
  /** OA岗位 */
  actualPosnJb?: string[];
  /** 结算状态 */
  settleStatus?: number[];
  /** 页码 */
  pageNum?: number;
  /** 页大小 */
  pageSize?: number;
}

export interface IDataItem {
  /** id */
  id?: string | number;
  /** 工号 */
  uname?: string;
  /** 用户名 */
  userName?: string;
  /** 仓库名称 */
  warehouseName?: string;
  /** 子仓库名称 */
  subWarehouseName?: string;
  /** 年月 */
  yearMonth?: string;
  /** 操作日期 */
  operateDate?: string;
  /** 实际岗位名称 */
  actualPosnJbName?: string;
  /** 业务大类名称 */
  businessBigCategoryName?: string;
  /** 岗位名称 */
  posnJbName?: string;
  /** 业务子类名称 */
  businessSubCategoryName?: string;
  /** 业务类型名称 */
  businessTypeName?: string;
  /** 渠道名称 */
  shppChnlName?: string;
  /** 操作单位名称 */
  operateUnitName?: string;
  /** 产能 */
  production?: string | number;
  /** 正常工时 */
  normalTimeHour?: string | number;
  /** 加班工时 */
  otTimeHour?: string | number;
  /** 原始标识 */
  originFlag?: string | number;
  /** 支持标识 */
  supportFlag?: string | number;
  /** 备注 */
  remark?: string;
  /** 最后更新时间 */
  lastUpdateTime?: string;
  /** 任务编码 */
  taskCode?: string;
  /** 计划任务类型名称 */
  planTaskTpName?: string;
}

type IData = IDataItem[];

interface IMeta {
  /** 数据记录数 */
  count?: number;
  /** 用户自定义扩展 */
  customObj?: unknown;
}

interface IResponseBodyInfo {
  /** 结果 */
  data: IData;
  /** 元数据 */
  meta: IMeta;
}

export type IGetListAPIResponse = IFetchResponse<IResponseBodyInfo>;

export interface ILimitType {
  uid: string; // 工号
  beginDay: string; // 开始日期
  endDay: string; // 结束日期
  posnJb: string[]; // 产能岗位
  actualPosnJb: string[]; // OA岗位
  settleStatus: number[]; // 结算状态
}

type IDictItem = {
  dictNameZh: string;
  dictCode: number | string;
}

export interface IUserItem {
  uname: string;
  fullName: string;
}

export interface IPosnJbItem {
  posnJb: string;
  posnJbName: string;
}

export interface IStateType {
  loading: number;
  limit: ILimitType;
  list: IDataItem[];
  selectedRows: IDataItem[];
  pageInfo: IPageInfo;
  formRef?: FormRef<ILimitType>;
  importModalVisible: boolean;
  file: Blob | string;
  userList: IUserItem[];
  posnJbList: IDictItem[];
  actualPosnJbList: IPosnJbItem[];
  settleStatusList: IDictItem[];
  warehouseId?: string | number;
  topSearch: boolean;
  count: number;
}

export type IPageProps = IViewBaseProps<IStateType>;
