import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { getSize } from '@src/middlewares/pagesize';
import { checkUrlPermissionAPI } from '@src/server/common/common';
/**
 * 个人计件-入库产能入库员20
 * 个人计件-盘点产能盘点员18
 * 个人计件-播种产能播种员1
 * 个人计件-打包产能打包员7
 * 个人计件-发货产能发货员8
 * 个人计件-集货产能集货员13
 * 个人计件-分拣产能分拣员11
 * 个人计件-合包产能合包员12
 * @type {[{subList: [{href: string, title: string}, {href: string, title: string}, {href: string, title: string}, {href: string, title: string}, {href: string, title: string}, null, null, null], title: string}, {subList: [{href: string, title: string}, {href: string, title: string}, {href: string, title: string}, {href: string, title: string}, {href: string, title: string}, null, null, null], title: string}, {subList: [{href: string, title: string}, {href: string, title: string}], title: string}, {subList: [{href: string, title: string}, {href: string, title: string}], title: string}, {subList: [{href: string, title: string}, {href: string, title: string}], title: string}, null]}
 */
const formOptions = [
  {
    title: t('绩效排名'),
    subList: [
      {
        name: 'dailyPickingCapacity',
        title: t('拣货产能报表-日报'),
        href: '/domestic-salary/balance-and-rules/settlement/performance-rank/daily-picking-capacity',
      },
      {
        name: 'dailyShelvingCapacity',
        title: t('上架产能报表-日报'),
        href: '/domestic-salary/balance-and-rules/settlement/performance-rank/daily-shelving-capacity',
      },
      {
        name: 'monthlyPickingCapacity',
        title: t('拣货产能报表月报'),
        href: '/domestic-salary/balance-and-rules/settlement/performance-rank/monthly-picking-capacity',
      },
      {
        name: 'monthlyShelvingCapacity',
        title: t('上架产能报表月报'),
        href: '/domestic-salary/balance-and-rules/settlement/performance-rank/monthly-shelving-capacity',
      },
      // {
      //   name: 'pickingScoreHourlyWage',
      //   title: t('拣货得分与时薪'),
      //   href: '/domestic-salary/balance-and-rules/settlement/performance-rank/picking-score-hourly-wage',
      // },
      // {
      //   name: 'shelvingScoreHourlyWage',
      //   title: t('上架得分与时薪'),
      //   href: '/domestic-salary/balance-and-rules/settlement/performance-rank/shelving-score-hourly-wage',
      // },
      {
        name: 'rewardsPunishments',
        title: t('激励单'),
        href: '/domestic-salary/balance-and-rules/settlement/performance-rank/rewards-punishments',
      },
      {
        name: 'ownershipScore',
        title: t('质量主人翁得分表'),
        href: '/domestic-salary/balance-and-rules/settlement/performance-rank/ownership-score',
      },
      {
        name: 'salaryCommonRankDailyReport',
        title: t('通用排名日报'),
        href: '/domestic-salary/balance-and-rules/settlement/salary-common-rank-daily-report',
      },
      {
        name: 'salaryCommonRankMonthReport',
        title: t('通用排名月报'),
        href: '/domestic-salary/balance-and-rules/settlement/salary-common-rank-month-report',
      },
      {
        name: 'capacityIncentiveList',
        title: t('产能激励名单'),
        href: '/domestic-salary/balance-and-rules/capacity-incentive-list',
      },
    ],
  },
  {
    title: t('小组计件'),
    subList: [
      {
        name: 'groupProductionCapacity',
        title: t('分拣产能工时报表'),
        href: '/domestic-salary/balance-and-rules/settlement/group-production-capacity',
      },
      // {
      //   title: t('分拣产能工时登记'),
      //   href: '/domestic-salary/balance-and-rules/settlement/sorting-capacity-hours',
      // },
      // {
      //   title: t('打包分拣时薪'),
      //   href: '/domestic-salary/balance-and-rules/settlement/salary_pick_dept_detail',
      // },
      {
        name: 'pickPerformance',
        title: t('分包分拣绩效'),
        href: '/domestic-salary/balance-and-rules/settlement/pick_performance',
      },
    ],
  },
  {
    title: t('调度支援'),
    subList: [
      {
        name: 'dispatchingSupport',
        title: t('调度支援报表'),
        href: '/domestic-salary/balance-and-rules/settlement/dispatching-support',
      },
      {
        name: 'overtimeWorkingHours',
        title: t('加班明细表'),
        href: '/domestic-salary/balance-and-rules/settlement/overtime-working-hours',
      },
    ],
  },
  {
    title: t('平均绩效'),
    subList: [
      {
        name: 'nanshaAverage',
        title: t('南沙平均绩效表'),
        href: '/domestic-salary/balance-and-rules/settlement/nansha-average',
      },
      {
        name: 'foshanAverage',
        title: t('佛山平均绩效表'),
        href: '/domestic-salary/balance-and-rules/settlement/foshan-average',
      },
    ],
  },
  {
    // 非敏感词
    title: t('绩效基数'),
    subList: [
      {
        name: 'performanceCoefficientScore',
        title: t('绩效系数与得分'),
        href: '/domestic-salary/balance-and-rules/performance-coefficient-score',
      },
      {
        name: 'salaryAgentManager',
        title: t('代理主管清单'),
        href: '/domestic-salary/balance-and-rules/salary-agent-manager',
      },
    ],
  },
  {
    title: t('其他'),
    subList: [
      {
        name: 'temporaryMonitor',
        title: t('临时班长任命与撤销'),
        href: '/domestic-salary/balance-and-rules/temporary-leader-assignment',
      },
      {
        name: 'temporaryMonitor',
        title: t('临时班长'),
        href: '/domestic-salary/balance-and-rules/settlement/temporary-monitor',
      },
      {
        name: 'reissueDifference',
        title: t('外接费用'),
        href: '/domestic-salary/balance-and-rules/reissue-difference',
      },
      // {
      //   name: 'salaryCompanyOverDay',
      //   title: t('因公超休'),
      //   href: '/domestic-salary/balance-and-rules/settlement/salary-company-over-day',
      // },
      {
        name: 'salarySubWarehouseTemperature',
        title: t('子仓温度登记表'),
        href: '/domestic-salary/balance-and-rules/settlement/salary-sub-warehouse-temperature',
      },
      {
        name: 'welfareSubsidiesImportCenter',
        title: t('福利补贴导入中心'),
        href: '/domestic-salary/balance-and-rules/welfare-subsidies-import-center',
      },
    ],
  },
  {
    title: t('佛山计件'),
    subList: [
      {
        name: 'individualPieceCount20',
        title: t('入库产能报表'),
        href: '/domestic-salary/balance-and-rules/settlement/individual-piece-count/20?warehouseId=1',
      },
      {
        name: 'individualPieceCount18',
        title: t('盘点产能报表'),
        href: '/domestic-salary/balance-and-rules/settlement/individual-piece-count/18?warehouseId=1',
      },
      {
        name: 'individualPieceCount13',
        title: t('集货产能报表'),
        href: '/domestic-salary/balance-and-rules/settlement/individual-piece-count/13?warehouseId=1',
      },
      {
        name: 'individualPieceCount1',
        title: t('播种产能报表'),
        href: '/domestic-salary/balance-and-rules/settlement/individual-piece-count/1?warehouseId=1',
      },
      {
        name: 'individualPieceCount7',
        title: t('打包产能报表'),
        href: '/domestic-salary/balance-and-rules/settlement/individual-piece-count/7?warehouseId=1',
      },
      {
        name: 'individualPieceCount32',
        title: t('预发货扫描产能报表'),
        href: '/domestic-salary/balance-and-rules/settlement/individual-piece-count/32?warehouseId=1',
      },
      {
        name: 'individualPieceCount8',
        title: t('发货产能报表'),
        href: '/domestic-salary/balance-and-rules/settlement/individual-piece-count/8?warehouseId=1',
      },
      {
        name: 'individualPieceCountOther1',
        title: t('其他计件产能'),
        href: '/domestic-salary/balance-and-rules/settlement/individual-piece-count/-1?warehouseId=1',
      },
    ],
  },
  {
    title: t('南沙计件'),
    subList: [
      {
        name: 'individualPieceCount12',
        title: t('南沙合包产能报表'),
        href: '/domestic-salary/balance-and-rules/settlement/individual-piece-count/12?warehouseId=98',
      },
      {
        name: 'individualPieceCount3298',
        title: t('南沙发货扫描产能'),
        href: '/domestic-salary/balance-and-rules/settlement/individual-piece-count/32?warehouseId=98',
      },
      // {
      //   title: t('分拣产能报表'),
      //   href: '/domestic-salary/balance-and-rules/settlement/individual-piece-count/11',
      // },
      {
        name: 'individualPieceCount198',
        title: t('南沙计件产能报表'),
        href: '/domestic-salary/balance-and-rules/settlement/individual-piece-count/-1?warehouseId=98',
      },
    ],
  },
  {
    title: t('线下产能'),
    subList: [
      {
        name: 'offlineCapacityRegistration',
        title: t('线下产能登记表'),
        href: '/domestic-salary/balance-and-rules/settlement/offline-capacity-registration',
      },
    ],
  },
  {
    title: t('实时产能'),
    subList: [
      {
        name: 'foshanSalaryProductionDaySummary',
        title: t('佛山计件实时产能'),
        href: '/domestic-salary/balance-and-rules/settlement/foshan-salary-production-day-summary',
      },
    ],
  },
  {
    title: t('夜班和超休'),
    subList: [
      {
        name: 'settleDataShiftScheduleRecord',
        title: t('排班记录'),
        href: '/domestic-salary/balance-and-rules/settlement/shift-schedule-record',
      },
      {
        name: 'officiallyExceedingNightShift',
        title: t('因公超休与夜班'),
        href: '/domestic-salary/balance-and-rules/settlement/officially-exceeding-night-shift',
      },
    ],
  },
  {
    title: t('结算数据调整申请'),
    subList: [
      {
        name: 'attendanceDataAdjustmentApply',
        title: t('考勤数据调整申请'),
        href: '/domestic-salary/balance-and-rules/attendance-data-adjustment-apply',
      },
    ],
  },
];
const defaultState = {
  warehouseId: undefined, // 仓库
  // 搜索区域+下拉数据
  loading: 1, // 0加载中 1加载成功 2加载失败
  topSearch: true,
  // 列表区域+分页区域
  pageInfo: {
    pageNum: 1,
    pageSize: getSize(),
    count: 0, // 表格总条数
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  list: [], // 表格数据
  selectedRows: [], // 选中行数据
  isShowDailyProduction: false, // 是否显示产能日报
  isShowMonthlyProduction: false, // 是否显示产能月报
  permissionCheckResult: {}, // 权限校验结果
  ready: false, // 权限校验是否完成
};

/**
   * 校验表单区域各表单的权限
   */
const checkPermission = async ({ name = '', checkUrl }) => {
  // 校验权限
  const res = await checkUrlPermissionAPI({
    url: checkUrl,
  });

  const resObj = {
    name,
    checkResult: false,
  };
  if (res.code === '0') {
    resObj.checkResult = true;
  } else {
    resObj.checkResult = false;
  }

  return resObj;
};

export default {
  state: defaultState,
  // 改state属性值
  changeData(state, data) {
    Object.assign(state, data);
  },

  /**
   * 页面初始化
   */
  * init() {
    markStatus('loading');

    // 校验【产能日报/月报】权限
    yield this.handleCheckPermission();

    // 校验表单权限
    yield this.checkAllPermissions();
  },

  /**
   * 校验【产能日报/月报】权限
   */
  * handleCheckPermission() {
    const dailyRes = yield checkUrlPermissionAPI({
      url: '/wbms/front/salary_production/day/query',
    });
    if (dailyRes.code === '0') {
      yield this.changeData({
        isShowDailyProduction: true,
      });
    } else {
      yield this.changeData({
        isShowDailyProduction: false,
      });
    }
    const monthlyRes = yield checkUrlPermissionAPI({
      url: '/wbms/front/salary_production/month/query',
    });
    if (monthlyRes.code === '0') {
      yield this.changeData({
        isShowMonthlyProduction: true,
      });
    } else {
      yield this.changeData({
        isShowMonthlyProduction: false,
      });
    }
  },

  /**
   * 异步校验所有表单权限
   * @returns
   */
  * checkAllPermissions() {
    // 待校验权限数据
    let toCheckPermissions = [];
    formOptions.forEach((item) => {
      const subList = item.subList || [];
      const currSubCheckPermissions = subList.map((subItem) => ({
        name: subItem.name,
        checkUrl: subItem.href,
      }));
      toCheckPermissions = [...toCheckPermissions, ...currSubCheckPermissions];
    });

    // 组装校验权限 Promise list
    const promises = toCheckPermissions.map((checkItem) => checkPermission(checkItem));
    const checkResults = yield Promise.allSettled(promises);

    // 校验结果
    const permissionCheckResult = {};

    checkResults.forEach((checkItem) => {
      const result = checkItem?.value || { name: '', checkResult: false };
      permissionCheckResult[result.name] = result.checkResult;
    });

    const newList = [];

    formOptions.forEach((item) => {
      const currArea = {
        ...item,
        subList: [],
      };
      const subList = item.subList || [];
      subList.forEach((subItem) => {
        if (permissionCheckResult[subItem.name]) {
          return currArea.subList.push(subItem);
        }
      });
      newList.push(currArea);
    });

    yield this.changeData({
      permissionCheckResult,
      list: newList,
      ready: true,
    });
  },
};
