import React from 'react';
import { t } from '@shein-bbl/react';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import OperationModal from '@public-component/modal/operation-modal';
import style from '@src/component/style.less';
import {
  Table, Tabs,
} from 'shineout';
import type { ColumnItem } from 'shineout/lib/Table/Props';
import classnames from 'classnames';
import store from '../reducers';
import { IDataItem, IPageProps, IRankDataItem } from '../types';
import styles from '../style.less';

function List(props: IPageProps) {
  const {
    loading,
    list,
    pageInfo,
    operationModalVisible,
    recordId,
    tabKey,
  } = props;

  const columns: ColumnItem<IDataItem>[] = [
    {
      title: t('月份'),
      render: 'ym',
      width: 160,
    },
    {
      title: t('工号'),
      render: 'uname',
      width: 160,
    },
    {
      title: t('姓名'),
      render: 'fullName',
      width: 160,
    },
    {
      title: t('公司'),
      render: 'companyName',
      width: 160,
    },
    {
      title: t('岗位'),
      render: 'posnJbName',
      width: 160,
    },
    {
      title: t('职级'),
      render: 'supvLevelName',
      width: 120,
    },
    {
      title: t('工时制'),
      render: 'workerSystemName',
      width: 120,
    },
    {
      title: t('临班任命期间'),
      render: 'tempLeaderPeriod',
      width: 120,
    },
    {
      title: t('三级部门（片区）'),
      render: 'dept4Desc',
      width: 180,
    },
    {
      title: t('四级部门'),
      render: 'dept5Desc',
      width: 180,
    },
    {
      title: t('五级部门'),
      render: 'dept6Desc',
      width: 180,
    },
    {
      title: t('六级部门'),
      render: 'dept7Desc',
      width: 180,
    },
    {
      title: t('绩效类型'),
      render: 'performanceTypeName',
      width: 120,
    },
    {
      title: t('入职日期'),
      render: 'lastHireDate',
      width: 180,
    },
    {
      title: t('转正日期'),
      render: 'probationDate',
      width: 120,
    },
    {
      title: t('离职日期'),
      render: 'terminationDate',
      width: 180,
    },
    {
      title: t('员工可计周末开始日期'),
      render: 'weekendStartDate',
      width: 180,
    },
    {
      title: t('正常产能-南沙'),
      render: 'workdayProductionNs',
      width: 180,
    },
    {
      title: t('周末产能-南沙'),
      render: 'weekendProductionNs',
      width: 180,
    },
    {
      title: t('非法定产能-佛山'),
      render: 'unHolidayProductionFs',
      width: 180,
    },
    {
      title: t('法定产能'),
      render: 'holidayProduction',
      width: 180,
    },
    {
      title: t('总产能'),
      render: 'totalProduction',
      width: 180,
    },
    {
      title: t('正常工时-南沙'),
      render: 'workdayWorkHours',
      width: 180,
    },
    {
      title: t('周末工时-南沙'),
      render: 'weekendWorkHours',
      width: 180,
    },
    {
      title: t('非法定工时-佛山'),
      render: 'unHolidayWorkHoursFs',
      width: 180,
    },
    {
      title: t('法定工时'),
      render: 'holidayWorkHours',
      width: 180,
    },
    {
      title: t('总工时'),
      render: 'totalWorkHours',
      width: 180,
    },
    {
      title: t('应出勤天'),
      render: 'shouldAttendDay',
      width: 180,
    },
    {
      title: t('是否满勤'),
      render: 'isFullAttendDesc',
      width: 180,
    },
    {
      title: t('平日出勤小时数'),
      render: 'workdayAttendHours',
      width: 180,
    },
    {
      title: t('平日加班小时数'),
      render: 'workdayOtHours',
      width: 180,
    },
    {
      title: t('周末加班小时数'),
      render: 'weekendOtHours',
      width: 180,
    },
    {
      title: t('法定加班小时数'),
      render: 'holidayOtHours',
      width: 180,
    },
    {
      title: t('总绩效出勤天'),
      render: 'performanceAttendDay',
      width: 180,
    },
    {
      title: t('正常绩效工资'),
      render: 'performanceBase',
      width: 180,
    },
    {
      title: t('周末绩效工资'),
      render: 'performanceWeekend',
      width: 180,
    },
    {
      title: t('非法定绩效'),
      render: 'performanceUnHoliday',
      width: 180,
    },
    {
      title: t('法定绩效工资'),
      render: 'performanceHoliday',
      width: 180,
    },
    {
      title: t('月度绩效总额'),
      render: 'performanceMonthlyTotal',
      width: 180,
    },
    {
      title: t('片区-浮动绩效'),
      render: 'performanceAreaFloat',
      width: 180,
    },
    {
      title: t('片区-绩效（拆分后）'),
      render: 'performanceAreaSplit',
      width: 180,
    },
    {
      title: t('片区-加班费（拆分后）'),
      render: 'overtimePayAreaSplit',
      width: 180,
    },
    {
      title: t('奖惩金额'),
      render: 'penaltyTotal',
      width: 120,
    },
    {
      title: t('上月补发补扣'),
      render: 'lastReissue',
      width: 120,
    },
    {
      title: t('浮动工资(月度总绩效+奖惩金额+上月补发)'),
      render: 'floatSalary',
      width: 120,
    },
    {
      title: t('工资条-绩效'),
      render: 'performanceSalary',
      width: 120,
    },
    {
      title: t('工资条-加班费'),
      render: 'overtimePay',
      width: 180,
    },
    {
      title: t('工资条-月度奖金'),
      render: 'reword',
      width: 120,
    },
    {
      title: t('KPI分数'),
      render: 'kpiScore',
      width: 120,
    },
    {
      title: t('KPI系数'),
      render: 'kpiRate',
      width: 120,
    },
    {
      title: t('月均绩效'),
      render: 'performanceMonthlyAvg',
      width: 180,
    },
    {
      title: t('导入人'),
      render: 'importUser',
      width: 180,
    },
    {
      title: t('导入时间'),
      render: 'importDate',
      width: 180,
    },
  ];
  // 拣货排名、上架排名、其他排名 三个tab 共用这个表格
  const rankColumns: ColumnItem<IRankDataItem>[] = [
    {
      title: t('月份'),
      render: 'ym',
      width: 120,
    },
    {
      title: t('园区'),
      render: 'park',
      width: 120,
    },
    {
      title: t('工号'),
      render: 'uname',
      width: 120,
    },
    {
      title: t('姓名'),
      render: 'fullName',
      width: 120,
    },
    {
      title: t('子仓'),
      render: 'label',
      width: 120,
    },
    {
      title: t('业务子类'),
      render: 'businessSubCategoryName',
      width: 120,
    },
    {
      title: t('转换后产能'),
      render: 'transformProduction',
      width: 120,
    },
    {
      title: t('耗时-(H)'),
      render: 'totalTime',
      width: 120,
    },
    {
      title: t('主力仓标签'),
      render: 'mainWarehouseFlagName',
      width: 120,
    },
    {
      title: t('排名标签'),
      render: 'rankLabel',
      width: 120,
    },
    {
      title: t('效率'),
      render: 'efficRate',
      width: 120,
    },
    {
      title: t('效率排名'),
      render: 'efficRealRank',
      width: 120,
    },
    {
      title: t('效率排名档次'),
      render: (record) => `${record.efficRank}${record?.joinRank ? t('档') : ''}`,
      width: 140,
    },
    {
      title: t('效率排名百分比'),
      render: 'efficRank',
      width: 120,
    },
    {
      title: t('效率得分'),
      render: 'efficScore',
      width: 120,
    },
    {
      title: t('质量得分'),
      render: 'qualityScore',
      width: 120,
    },
    {
      title: t('主人翁得分'),
      render: 'scoreOwner',
      width: 120,
    },
    {
      title: t('月度得分'),
      render: 'monthScore',
      width: 120,
    },
    {
      title: t('月度得分排名'),
      render: 'monthRealRank',
      width: 120,
    },
    {
      title: t('月度得分档次'),
      render: 'monthRank',
      width: 120,
    },
    {
      title: t('月度排名系数'),
      render: 'monthRate',
      width: 120,
    },
    {
      title: t('入职日期'),
      render: 'lastHireDate',
      width: 120,
    },
    {
      title: t('转岗日期'),
      render: 'jobTransferDate',
      width: 120,
    },
    {
      title: t('老员工属性'),
      render: 'oldStatusName',
      width: 120,
    },
    {
      title: t('新员工入职周期'),
      render: 'onBoardingCycle',
      width: 120,
    },
    {
      title: t('主力仓老员工平均效率'),
      render: 'mainOldAvgEffic',
      width: 120,
    },
    {
      title: t('新员工/非主力仓达成率'),
      render: 'otherAvgEffic',
      width: 120,
    },
    {
      title: t('时薪标准'),
      render: 'hourSalaryStandard',
      width: 120,
    },
    {
      title: t('非法定工时'),
      render: 'notHoTime',
      width: 120,
    },
    {
      title: t('法定加班工时'),
      render: 'hoTime',
      width: 140,
    },
    {
      title: t('拣货环节整体效率系数'),
      render: 'totalRate',
      width: 160,
    },
    {
      title: t('非法定绩效'),
      render: 'notHoPerformanceSalary',
      width: 120,
    },
    {
      title: t('法定绩效'),
      render: 'hoPerformanceSalary',
      width: 120,
    },
    {
      title: t('非法定新人津贴'),
      render: 'notHoNewEmployeeAllowance',
      width: 140,
    },
    {
      title: t('法定新人津贴'),
      render: 'hoNewEmployeeAllowance',
      width: 140,
    },
    {
      title: t('合计'),
      render: 'total',
      width: 120,
    },
    {
      title: t('更新时间'),
      render: 'lastUpdateTime',
      width: 140,
    },
  ];
  const RankTable = (
    <Table<IDataItem, IDataItem[]>
      style={{ height: '100%' }}
      rowClassName={() => style.borderInner}
      bordered
      fixed="both"
      loading={!loading}
      data={list}
      columns={rankColumns}
      keygen="id"
      empty={t('暂无数据')}
      size="small"
      width={columns.reduce((pre, current) => pre + (current.width || 100), 50)}
      pagination={{
        align: 'right',
        current: pageInfo.pageNum,
        pageSize: pageInfo.pageSize,
        className: style.pagination,
        layout: [({ total }) => `${t('共')} ${total} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
        onChange: (page, size) => {
          store.handlePaginationChange({
            pageNum: page,
            pageSize: size,
          });
        },
        pageSizeList: pageInfo.pageSizeList,
        total: pageInfo.count,
      }}
    />
  );

  return (
    <section className={style.tableSection}>
      <Tabs
        shape="card"
        defaultActive={tabKey}
        onChange={(key) => {
          store.changeLimitData({
            companyName: '',
          });
          store.changeData({ tabKey: key });
          store.search();
        }}
        className={classnames(styles.tabsSection, styles.tabStyle)}
      >
        <Tabs.Panel className={styles.tabsPanel} tab={t('计件绩效')}>
          <SearchAreaTable>
            <Table<IDataItem, IDataItem[]>
              style={{ height: '100%' }}
              rowClassName={() => style.borderInner}
              bordered
              fixed="both"
              loading={!loading}
              data={list}
              columns={columns}
              keygen="id"
              empty={t('暂无数据')}
              size="small"
              width={columns.reduce((pre, current) => pre + (current.width || 100), 50)}
              pagination={{
                align: 'right',
                current: pageInfo.pageNum,
                pageSize: pageInfo.pageSize,
                className: style.pagination,
                layout: [({ total }) => `${t('共')} ${total} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
                onChange: (page, size) => {
                  store.handlePaginationChange({
                    pageNum: page,
                    pageSize: size,
                  });
                },
                pageSizeList: pageInfo.pageSizeList,
                total: pageInfo.count,
              }}
            />
          </SearchAreaTable>
        </Tabs.Panel>
        <Tabs.Panel className={styles.tabsPanel} tab={t('拣货排名')}>
          {RankTable}
        </Tabs.Panel>
        <Tabs.Panel className={styles.tabsPanel} tab={t('上架排名')}>
          {RankTable}
        </Tabs.Panel>
        <Tabs.Panel className={styles.tabsPanel} tab={t('其他排名')}>
          {RankTable}
        </Tabs.Panel>
      </Tabs>
      <OperationModal
        visible={operationModalVisible}
        param={{
          operateId: recordId,
          operateCode: 'MULTI_AREA_PATH',
        }}
        onCancel={() => store.changeData({ operationModalVisible: false })}
      />
    </section>
  );
}

export default List;
