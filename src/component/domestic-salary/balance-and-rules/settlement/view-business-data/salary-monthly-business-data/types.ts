import {
  IFetchResponse, IPageInfo, ISubwarehouseItem, IViewBaseProps, IWarehouseItem,
} from '@src/typing/base';
import { FormRef } from 'shineout/lib/Form/Props';

export interface IGetListAPIRequest {
  /** 当前页码 */
  pageNum?:number;
  /** 页宽度 */
  pageSize?:number;
  /** 绩效类型 */
  performanceType?:number[];
  /** 工号 */
  uname?:string;
  /** 月份 */
  ym?:number;
}

interface IGroupTaskPriorityListItem {
  /** 优先级 */
  priority:string;
  /** 任务子类ID */
  taskId:number;
  /** 子类任务名称 */
  taskName:string;
}

export interface IDataItem {
  businessKey?:string;
  companyCode?:string;
  /** 公司 */
  companyName?:string;
  /** 三级部门 */
  dept4Desc?:string;
  /** 四级部门 */
  dept5Desc?:string;
  /** 五级部门 */
  dept6Desc?:string;
  /** 六级部门 */
  dept7Desc?:string;
  deptId?:string;
  /** 浮动工资 */
  floatSalary?:string;
  /** 姓名 */
  fullName?:string;
  /** 法定加班小时数 */
  holidayOtHours?:string;
  /** 法定产能 */
  holidayProduction?:string;
  /** 法定工时 */
  holidayWorkHours?:string;
  /** 主键 */
  id?:number;
  /** 操作时间 */
  importDate?:string;
  /** 操作人 */
  importUser?:string;
  isFullAttend?:boolean;
  /** 是否满勤 */
  isFullAttendDesc?:string;
  /** KPI系数 */
  kpiRate?:string;
  /** KPI分数 */
  kpiScore?:string;
  /** 入职日期 */
  lastHireDate?:string;
  /** 上月补发 */
  lastReissue?:string;
  /** 工资条-加班工资 */
  overtimePay?:string;
  /** 片区-加班费（拆分后） */
  overtimePayAreaSplit?:string;
  /** 奖惩金额 */
  penaltyTotal?:string;
  /** 片区-浮动绩效 */
  performanceAreaFloat?:string;
  /** 片区-绩效（拆分后） */
  performanceAreaSplit?:string;
  /** 总绩效出勤天 */
  performanceAttendDay?:string;
  /** 正常绩效工资 */
  performanceBase?:string;
  /** 法定绩效工资 */
  performanceHoliday?:string;
  /** 月平均绩效工资 */
  performanceMonthlyAvg?:string;
  /** 月度绩效总额 */
  performanceMonthlyTotal?:string;
  /** 工资条-绩效 */
  performanceSalary?:string;
  performanceType?:string;
  /** 绩效类型 */
  performanceTypeName?:string;
  /** 非法定绩效 */
  performanceUnHoliday?:string;
  /** 周末绩效工资 */
  performanceWeekend?:string;
  posnJb?:string;
  /** 岗位 */
  posnJbName?:string;
  /** 转正日期 */
  probationDate?:string;
  /** 工资条-月度奖金 */
  reword?:string;
  /** 应出勤天 */
  shouldAttendDay?:number;
  supvLevelId?:string;
  /** 职级 */
  supvLevelName?:string;
  /** 临班任命期间 */
  tempLeaderPeriod?:string;
  /** 离职日期 */
  terminationDate?:string;
  /** 总产能 */
  totalProduction?:string;
  /** 总工时 */
  totalWorkHours?:string;
  /** 非法定产能-佛山 */
  unHolidayProductionFs?:string;
  /** 非法定工时-佛山 */
  unHolidayWorkHoursFs?:string;
  /** 工号 */
  uname?:string;
  warehouseId?:number;
  /** 周末加班小时数 */
  weekendOtHours?:string;
  /** 周末产能-南沙 */
  weekendProductionNs?:string;
  /** 员工可计周末开始日期 */
  weekendStartDate?:string;
  /** 周末工时-南沙 */
  weekendWorkHours?:string;
  /** 平日出勤小时数 */
  workdayAttendHours?:string;
  /** 平日加班小时数 */
  workdayOtHours?:string;
  /** 正常产能-南沙 */
  workdayProductionNs?:string;
  /** 正常工时-南沙 */
  workdayWorkHours?:string;
  workerSystem?:string;
  /** 工时制 */
  workerSystemName?:string;
  /** 月份 */
  ym?:number;
}

type IData = IDataItem[];

interface IMeta {
  /** 数据记录数 */
  count?:number;
  /** 用户自定义扩展 */
  customObj?:unknown;
}

interface IResponseBodyInfo {
  /** 结果 */
  data:IData;
  /** 元数据 */
  meta:IMeta;
}

export type IGetListAPIResponse = IFetchResponse<IResponseBodyInfo>;

export interface ILimitType {
  /** 绩效类型 */
  performanceType?:number[];
  /** 工号 */
  uname?:string;
  /** 月份 */
  ym?:number | string;
  deptPermissionIdList: Array<string>
  companyName?: string;
}

export interface IUserListItem {
  uname: string;
  fullName: string;
}
export interface ISyncData {
  ym: string;
  syncType: string;
}

export interface IStateType {
  loading: number; // 0 loading, 1 load success, 2 load fail
  limit: ILimitType;
  ready?: boolean;
  subWarehouseList: ISubwarehouseItem[];
  currentWarehouseList?: IWarehouseItem[];
  list: IDataItem[];
  showPriorityList?: IGroupTaskPriorityListItem[];
  importModalVisible: boolean;
  operationModalVisible: boolean;
  selectedGroups?: IDataItem[];
  pageInfo: IPageInfo;
  formRef?: FormRef<ILimitType>;
  recordId?: number;
  recordVisible?: boolean;
  file?: string;
  headerFormAreaList?: {id: number; area: string}[];
  warehouseIds?: string | number | undefined;
  exportVisible: boolean;
  file: string;
  userList: IUserListItem [];
  showSensitiveData: boolean;
  showSensitiveDataBtn: boolean;
  tabKey: number;
  syncDataModalVisible: boolean;
  syncData: ISyncData;
  deptPermissionIdListOptions: Array<{
    id: string;
    name: string;
  }>;
  syncTypeOptions: Array<{
    dictCode: string;
    dictNameZh: string;
  }>;
}

export interface IRankDataItem {
  /** 业务子类 */
  businessSubCategoryName?:string;
  /** 六级部门 */
  dept?:string;
  /** 三级部门 */
  dept3?:string;
  /** 四级部门 */
  dept4?:string;
  /** 五级部门 */
  dept5?:string;
  /** 效率排名档次 */
  efficRank?:number;
  /** 效率 */
  efficRate?:number;
  /** 效率排名 */
  efficRealRank?:number;
  /** 效率得分 */
  efficScore?:number;
  /** 姓名 */
  fullName?:string;
  /** 法定新人津贴 */
  hoNewEmployeeAllowance?:number;
  /** 法定绩效 */
  hoPerformanceSalary?:number;
  /** 法定工时 */
  hoTime?:number;
  /** 时薪标准 */
  hourSalaryStandard?:number;
  /** 主键 */
  id?:number;
  /** 转岗时间 */
  jobTransferDate?:string;
  /** 子仓 */
  label?:string;
  /** 入职日期 */
  lastHireDate?:string;
  /** 更新时间 */
  lastUpdateTime?:string;
  /** 主力仓老员工平均效率 */
  mainOldAvgEffic?:number;
  /** 主力仓/非主力仓 */
  mainWarehouseFlagName?:string;
  /** 排名档次 */
  monthRank?:number;
  /** 月度排名系数 */
  monthRate?:number;
  /** 月度总得分排名 */
  monthRealRank?:number;
  /** 月度总得分 */
  monthScore?:number;
  /** 非法定新人津贴 */
  notHoNewEmployeeAllowance?:number;
  /** 非法定绩效 */
  notHoPerformanceSalary?:number;
  /** 非法定工时 */
  notHoTime?:number;
  /** 老员工/新员工 */
  oldStatusName?:string;
  /** 入职周期 */
  onBoardingCycle?:number;
  /** 新员工/非主力仓达成率 */
  otherAvgEffic?:number;
  /** 园区 */
  park?:string;
  /** 岗位 */
  posnJbName?:string;
  /** 质量得分 */
  qualityScore?:number;
  /** 排名标签 */
  rankLabel?:string;
  /** 主人翁得分 */
  scoreOwner?:number;
  /** 合计 */
  total?:number;
  /** 整体效率系数 */
  totalRate?:number;
  /** 耗时 */
  totalTime?:number;
  /** 转换后产能 */
  transformProduction?:number;
  /** 工号 */
  uname?:string;
  /** 月份 */
  ym?:number;
  joinRank?: string;
}

type IRankData = IDataItem[];

interface IRankResponseBodyInfo {
  /** 结果 */
  data:IRankData;
  /** 元数据 */
  meta:IMeta;
}

export type IGetRankListAPIResponse = IFetchResponse<IRankResponseBodyInfo>;

export type IGetAPIResponse = IGetListAPIResponse | IGetRankListAPIResponse

export type IPageProps = IViewBaseProps<IStateType>;
