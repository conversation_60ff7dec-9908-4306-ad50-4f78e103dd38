import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import {
  Rule, TreeSelect, Select, Switch,
} from 'shineout';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import { formatSearchData } from '@public-component/search-queries/utils';
import DateRangePicker from '@shein-components/dateRangePicker2';
import moment from 'moment';
import store, { defaultLimit } from '../reducers';

const rule = Rule({
  timeRange: {
    func: (val, formData, callback) => {
      // 时间范围不能超过一个月
      if (moment(formData.endDay)
        .diff(moment(formData.beginDay), 'months', true) > 1) {
        callback(new Error(t('时间范围不能超过{}个月', 1)));
      }
      callback(true);
    },
  },
});

class Header extends Component {
  render() {
    const {
      limit,
      loading,
      allDeptOptions,
      attendStartStatusList,
      attendEndStatusList,
      workTypeList,
      userList,
      thirdDeptList,
      fourthDeptList,
      fifthDeptList,
      posnJbList,
      calendarTypeList,
      deptsPermission,
    } = this.props;
    return (
      <section>
        <SearchAreaContainer
          clearUndefined={false}
          labelStyle={{ width: 70 }} // 统一修改label宽度，默认60
          searching={!loading}
          value={limit}
          // 保留用户所选项，并补全搜索条件
          onChange={(val) => store.changeLimitData(formatSearchData(defaultLimit, val))}
          onClear={() => store.clearLimitData()}
          onSearch={() => {
            // 点搜索按钮，将页码重置为1
            store.handlePaginationChange({ pageNum: 1 });
          }}
          formRef={(f) => store.changeData({ formRef: f })}
          alwaysVisible={[t('日期')]}
        >
          <Select
            autoAdapt
            label={t('三级部门(片区)')}
            name="permissionIds"
            data={thirdDeptList}
            keygen="id"
            format="id"
            renderItem="name"
            placeholder={t('权限域自动带出')}
            disabled
            multiple
            compressed
          />
          <Select
            autoAdapt
            label={t('四级部门')}
            name="deptFourIds"
            data={fourthDeptList}
            keygen="deptId"
            format="deptId"
            renderItem="pdDescrLong"
            placeholder={t('全部')}
            multiple
            compressed
            clearable
            onFilter={(text) => (d) => d.pdDescrLong.indexOf(text) >= 0}
            onChange={(val) => store.handleFourthDeptChange(val)}
          />
          <Select
            autoAdapt
            label={t('五级部门')}
            name="deptFiveIds"
            data={fifthDeptList}
            keygen="deptId"
            format="deptId"
            renderItem="pdDescrLong"
            placeholder={t('全部')}
            multiple
            compressed
            clearable
            onFilter={(text) => (d) => d.pdDescrLong.indexOf(text) >= 0}
          />
          <Select
            autoAdapt
            label={t('姓名')}
            keygen="uname"
            format="uname"
            data={userList}
            clearable
            onFilter={(v) => { store.handleUnameFilter(v); }}
            renderItem="fullName"
            name="uid"
            placeholder={t('请输入')}
          />
          <Select
            autoAdapt
            label={t('上班卡状态')}
            name="attendStartStatus"
            data={attendStartStatusList}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('全部')}
            renderItem="dictNameZh"
            multiple
            compressed
            clearable
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
          <Select
            autoAdapt
            label={t('下班卡状态')}
            name="attendEndStatus"
            data={attendEndStatusList}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('全部')}
            renderItem="dictNameZh"
            multiple
            compressed
            clearable
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
          <Select
            autoAdapt
            label={t('班次类型')}
            name="workTimeType"
            data={workTypeList}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            placeholder={t('全部')}
            clearable
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
          <Select
            autoAdapt
            label={t('岗位')}
            name="posnJbs"
            data={posnJbList}
            keygen="dictCode"
            format="dictNameEn"
            renderItem="dictNameZh"
            placeholder={t('全部')}
            multiple
            compressed
            clearable
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
          <Select
            autoAdapt
            label={t('日历类型')}
            name="calendarTypes"
            data={calendarTypeList}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            placeholder={t('全部')}
            multiple
            compressed
            clearable
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
          <DateRangePicker
            required
            placeholder={[t('开始时间'), t('结束时间')]}
            type="datetime"
            inputable
            format="yyyy-MM-dd"
            name={['beginDay', 'endDay']}
            defaultTime={['00:00:00', '23:59:59']}
            label={t('日期')}
            span={2}
            rules={[rule.required, rule.timeRange()]}
          />
          <DateRangePicker
            required
            placeholder={[t('开始时间'), t('结束时间')]}
            type="datetime"
            inputable
            format="yyyy-MM-dd"
            name={['beginTerminationDate', 'endTerminationDate']}
            defaultTime={['00:00:00', '23:59:59']}
            label={t('最后工作日')}
            span={2}
          />
          {
            deptsPermission && (
              <TreeSelect
                label={t('部门')}
                compressed
                name="depts"
                span={2}
                mode={2}
                clearable
                keygen={(d) => d.deptId}
                renderItem="pdDescrLong"
                data={allDeptOptions}
                checkboxFalse
                multiple
                childrenKey="childList"
                onFilter={(text) => (d) => d.pdDescrLong.indexOf(text) >= 0}
              />
            )
          }
          <Switch
            label={t('作业子仓识别失败')}
            name="subWarehouseNullFlag"
            checkedChildren={t('开启')}
            uncheckedChildren={t('关闭')}
            style={{ width: 40 }}
          />
        </SearchAreaContainer>
      </section>
    );
  }
}

Header.propTypes = {
  limit: PropTypes.shape().isRequired,
  loading: PropTypes.number.isRequired,
  allDeptOptions: PropTypes.arrayOf(PropTypes.shape()),
  attendStartStatusList: PropTypes.arrayOf(PropTypes.shape()),
  attendEndStatusList: PropTypes.arrayOf(PropTypes.shape()),
  workTypeList: PropTypes.arrayOf(PropTypes.shape()),
  userList: PropTypes.arrayOf(PropTypes.shape()),
  thirdDeptList: PropTypes.arrayOf(PropTypes.shape()),
  fourthDeptList: PropTypes.arrayOf(PropTypes.shape()),
  fifthDeptList: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  posnJbList: PropTypes.arrayOf(PropTypes.shape()),
  calendarTypeList: PropTypes.arrayOf(PropTypes.shape()),
  deptsPermission: PropTypes.bool,
};
export default Header;
