import { markStatus } from 'rrc-loader-helper';
import { Modal } from 'shineout';
import { handleListMsg } from '@src/lib/dealFunc';
import { getSize } from '@src/middlewares/pagesize';
import { clearEmpty } from '@src/lib/deal-func';
import { dictSelect } from '@src/server/basic/dictionary';
import moment from 'moment';
import fileSaver from 'file-saver';
import { formdataPost } from '@src/server/common/fileFetch';
import { STATISTICAL_IMPORT_CENTER } from '@src/lib/jump-url';
import { t } from '@shein-bbl/react';
import { queryByFullNameAPI } from '@src/server/wbms/server';
import {
  getListAPI,
  exportListAPI,
  downloadAPI,
  importUrl,
  downloadAdjustSubwarehouseTemplateAPI,
  imporAdjustSubwarehousetUrl,
  imporSalaryDayAttendanceModifyUrl,
  downloadSalaryDayAttendanceModifyTemplateAPI,
  getPermissionBizListAPI,
  getDeptByParentAPI,
  getDeptByStartEndAPI,
} from './server';

// 搜索区域：搜索导出条件，若有仓库则使用nav右上角的仓库
export const defaultLimit = {
  uid: '', // 工号（即uid）
  attendStartStatus: [], // 考勤上班卡状态
  attendEndStatus: [], // 考勤下班卡状态
  workTimeType: '', // 班次类型
  nightBonus: '', // 夜班补贴
  oldStatus: '', // 老员工属性
  beginDay: moment().subtract(6, 'days').format('YYYY-MM-DD'),
  endDay: moment().subtract(0, 'days').format('YYYY-MM-DD'),
  beginTerminationDate: '', // 最后工作日开始时间
  endTerminationDate: '', // 最后工作日结束时间
  permissionIds: [], // 三级部门(片区)
  deptFourIds: [], // 四级部门
  deptFiveIds: [], // 五级部门名称
  depts: [], // 部门
  posnJbs: [], // 岗位
  calendarTypes: [], // 日历类型
  subWarehouseNullFlag: false, // 作业子仓识别失败
};

const defaultState = {
  warehouseId: undefined, // 仓库
  formRef: {}, // 绑定 form 的引用, 可以调用某些 form 的方法  validate(校验表单) 具体可以查看 https://shineout.biz.sheincorp.cn/1.6.x/cn/components/Form
  // 搜索区域+下拉数据
  loading: 1, // 0加载中 1加载成功 2加载失败
  topSearch: true,
  limit: defaultLimit,
  // 列表区域+分页区域
  pageInfo: {
    pageNum: 1,
    pageSize: getSize(),
    count: 0, // 表格总条数
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  list: [], // 表格数据
  count: 0, // 表格总条数
  selectedRows: [], // 选中行数据
  attendStartStatusList: [], // 考勤上班卡状态下拉
  attendEndStatusList: [], // 考勤下班卡状态下拉
  workTypeList: [], // 班次类型下拉
  nightBonusList: [], // 夜班补贴下拉
  oldStatusList: [], // 老员工属性下拉
  userList: [], // 用户下拉数据
  thirdDeptList: [], // 三级部门下拉数据
  fourthDeptList: [], // 四级部门下拉数据
  fifthDeptList: [], // 五级部门下拉数据
  allDeptOptions: [], // 部门下拉
  deptsPermission: false,
  posnJbList: [], // 岗位下拉数据
  calendarTypeList: [], // 日历类型下拉数据
  importModalVisible: false, // 高温补贴调整弹窗显示 默认不显示
  multiChangePropFile: '', // 高温补贴调整弹窗导入文件
  adjustSubwarehouseModalVisible: false, // 作业子仓调整弹窗
  dailyReportModifyModalVisible: false, // 考勤日报调整弹窗
  customObj: {}, // 汇总数据
};

export default {
  state: defaultState,
  // 改state属性值
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件即state.limit属性值
  changeLimitData(state, data) {
    Object.assign(state.limit, data);
  },
  // 改搜索条件新增/编辑对象属性值
  changeEdit: (state, data) => {
    Object.assign(state.editObj, data);
  },
  // 重置查询条件
  * clearLimitData() {
    const { formRef, limit } = yield '';
    yield this.changeLimitData({
      ...defaultLimit,
      permissionIds: limit.permissionIds,
    });
    yield this.getFourthDeptList(limit.permissionIds);
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },
  // 页面初始化
  * init() {
    // 请求下拉框数据
    markStatus('loading');
    const selectData = yield dictSelect({
      catCode: [
        'WBMS_SALARY_ATTEND_START_STATUS',
        'WBMS_SALARY_ATTEND_END_STATUS',
        'WBMS_SALARY_WORK_TIME_TYPE',
        'WBMS_SALARY_NIGHT_BONUS',
        'WBMS_SALARY_USER_IS_OLD',
        'WBMS_SALARY_POSN_JB',
        'WBMS_SALARY_ATTEND_DAY_CALENDAR_TYPE'],
    });
    if (selectData.code === '0') {
      yield this.changeData({
        attendStartStatusList: selectData.info.data.find((item) => item.catCode === 'WBMS_SALARY_ATTEND_START_STATUS').dictListRsps,
        attendEndStatusList: selectData.info.data.find((item) => item.catCode === 'WBMS_SALARY_ATTEND_END_STATUS').dictListRsps,
        workTypeList: selectData.info.data.find((item) => item.catCode === 'WBMS_SALARY_WORK_TIME_TYPE').dictListRsps,
        nightBonusList: selectData.info.data.find((item) => item.catCode === 'WBMS_SALARY_NIGHT_BONUS').dictListRsps,
        oldStatusList: selectData.info.data.find((item) => item.catCode === 'WBMS_SALARY_USER_IS_OLD').dictListRsps,
        posnJbList: selectData.info.data.find((item) => item.catCode === 'WBMS_SALARY_POSN_JB').dictListRsps,
        calendarTypeList: selectData.info.data.find((item) => item.catCode === 'WBMS_SALARY_ATTEND_DAY_CALENDAR_TYPE').dictListRsps,
      });
    } else {
      handleListMsg([selectData]);
    }
    // 获取三级部门权限数据
    yield this.getThirdDeptList();
    // 获取部门下拉
    yield this.getDeptList();
  },
  /**
   * 搜索
   */
  * search() {
    const { limit, pageInfo } = yield '';
    const param = {
      ...limit,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
    };
    markStatus('loading');
    const { code, info, msg } = yield getListAPI(clearEmpty(param, [0]));
    if (code === '0') {
      yield this.changeData({
        // 有复选框的，要重置对应字段成空数组
        list: info.data,
        pageInfo: {
          ...pageInfo,
          count: info.meta.count,
        },
        customObj: info.meta.customObj,
        selectedRows: [],
      });
    } else {
      Modal.error({ title: msg });
    }
  },

  /**
   * 校验
   * @returns
   */
  * handleFormValidate() {
    const { formRef } = yield '';
    // 兼容跳转进来触发搜索时，dom还没渲染完，执行formRef.validate()报错
    if (!formRef || !formRef.validate) {
      return true;
    }
    let validateFlag = false;
    yield formRef.validate().then(() => {
      validateFlag = true;
    }).catch(() => {
      validateFlag = false;
    });
    return validateFlag;
  },

  /**
   * 分页条改变
   * @param {object} data 分页数组主要有pageNum pageSize
   * @returns
   */
  * handlePaginationChange(data = {}) {
    // 校验
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    // 校验通过后执行
    const { pageInfo } = yield '';
    yield this.changeData({
      pageInfo: {
        ...pageInfo,
        ...data,
      },
    });
    yield this.search();
  },
  // 导出
  * exportData() {
    // 校验
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    // 校验通过后执行
    const { limit } = yield '';
    // 一般导出接口不用传页数页码，因为是要导出全部数据：部分旧页面有传就与旧页面一致；新页面跟后端统一按规范不用传页数页码
    // 实际开发时，根据情况决定是否加仓库id，页数页码等字段
    const param = {
      ...limit,
      pageNum: 1,
      pageSize: 100000,
    };
    markStatus('loading');
    const { code, msg } = yield exportListAPI(param);
    if (code === '0') {
      window.open('#/statistical/download');
    } else {
      Modal.error({ title: msg });
    }
  },
  // 输入人名进行搜索
  * handleUnameFilter(v) {
    if (!v) { return; }
    const { code, info, msg } = /^\d/.test(v) // 判断当前输入的是否是数字开头
      ? yield queryByFullNameAPI({
        uname: v, // 工号
        pageNum: 1,
        pageSize: 50,
      }) : yield queryByFullNameAPI({
        fullName: v, // 中文名
        pageNum: 1,
        pageSize: 50,
      });
    if (code === '0') {
      yield this.changeData({ userList: info });
    } else {
      Modal.error({ title: msg });
    }
  },
  /**
   * 下载
   */
  * downloadTemplate() {
    yield this.changeData({
      dataLoading: true,
    });
    const res = yield downloadAPI();
    yield this.changeData({
      dataLoading: false,
    });
    if (res.ok) {
      res.blob().then((b) => {
        const blob = new Blob([b], { type: 'application/octet-stream' });
        fileSaver.saveAs(blob, t('导入模板.xls'));
      });
    } else {
      res.json().then((err) => {
        Modal.error({ title: err.msg });
      });
    }
  },
  * uploadFile(formData) {
    yield this.changeData({
      dataLoading: true,
    });
    formData.append('function_node', '149');
    const res = yield formdataPost(importUrl, formData);
    yield this.changeData({
      dataLoading: false,
    });
    if (res.code === '0') {
      yield this.changeData({
        importModalVisible: false,
        multiChangePropFile: '',
      });
      window.open(STATISTICAL_IMPORT_CENTER);
    } else {
      Modal.error({ content: res.msg });
    }
  },
  /**
   * 下载
   */
  * downloadAdjustSubwarehouseTemplate() {
    yield this.changeData({
      dataLoading: true,
    });
    const res = yield downloadAdjustSubwarehouseTemplateAPI();
    yield this.changeData({
      dataLoading: false,
    });
    if (res.ok) {
      res.blob().then((b) => {
        const blob = new Blob([b], { type: 'application/octet-stream' });
        fileSaver.saveAs(blob, t('导入模板.xls'));
      });
    } else {
      res.json().then((err) => {
        Modal.error({ title: err.msg });
      });
    }
  },
  * uploadAdjustSubwarehouseFile(formData) {
    yield this.changeData({
      dataLoading: true,
    });
    formData.append('function_node', '164');
    const res = yield formdataPost(imporAdjustSubwarehousetUrl, formData);
    yield this.changeData({
      dataLoading: false,
    });
    if (res.code === '0') {
      yield this.changeData({
        adjustSubwarehouseModalVisible: false,
      });
      window.open(STATISTICAL_IMPORT_CENTER);
    } else {
      Modal.error({ content: res.msg });
    }
  },
  /**
   * 考勤日报数据修正导入
   * @param {*} formData
   */
  * uploadDailyReportModifyFile(formData) {
    yield this.changeData({
      dataLoading: true,
    });
    formData.append('function_node', '168');
    const res = yield formdataPost(imporSalaryDayAttendanceModifyUrl, formData);
    yield this.changeData({
      dataLoading: false,
    });
    if (res.code === '0') {
      window.open(STATISTICAL_IMPORT_CENTER);
      yield this.changeData({
        dailyReportModifyModalVisible: false,
      });
    } else {
      Modal.error({ content: res.msg });
    }
  },
  /**
   * 下载考勤日报数据修正导入模板
   */
  * downloadDailyReportModifyTemplate() {
    yield this.changeData({
      dataLoading: true,
    });
    const res = yield downloadSalaryDayAttendanceModifyTemplateAPI();
    yield this.changeData({
      dataLoading: false,
    });
    if (res.ok) {
      res.blob().then((b) => {
        const blob = new Blob([b], { type: 'application/octet-stream' });
        fileSaver.saveAs(blob, t('考勤日报数据修正导入模板.xls'));
      });
    } else {
      res.json().then((err) => {
        Modal.error({ title: err.msg });
      });
    }
  },
  // 获取三级部门数据
  * getThirdDeptList() {
    const { code, info, msg } = yield getPermissionBizListAPI({ type: '1' });
    if (code === '0') {
      yield this.changeData({
        thirdDeptList: info.data,
      });
      // 自动设置默认值
      yield this.changeLimitData({
        permissionIds: info.data.map((item) => item.id),
      });
      // 获取四级部门数据
      yield this.getFourthDeptList(info.data.map((item) => item.id));
    } else {
      Modal.error({ title: msg });
    }
  },
  // 获取四级部门数据
  * getFourthDeptList(parentPermissionIds) {
    if (!parentPermissionIds || parentPermissionIds.length === 0) {
      yield this.changeData({ fourthDeptList: [] });
      return;
    }
    const { code, msg, info } = yield getDeptByParentAPI({
      dept1: '800000',
      parentPermissionIdList: parentPermissionIds,
    });

    if (code === '0') {
      yield this.changeData({
        fourthDeptList: info.data,
      });
      // 获取五级部门数据
      yield this.getFifthDeptList({
        parentIdList: info.data.map((item) => item.deptId),
      });
    } else {
      Modal.error({ title: msg });
    }
  },
  // 获取五级部门数据
  * getFifthDeptList({ parentPermissionIds = [], parentIdList = [] }) {
    const params = {
      dept1: '800000',
    };
    if (parentPermissionIds.length) {
      params.parentPermissionIdList = parentPermissionIds;
    }
    if (parentIdList.length) {
      params.parentIdList = parentIdList;
    }

    const { code, msg, info } = yield getDeptByParentAPI(params);

    if (code === '0') {
      yield this.changeData({
        fifthDeptList: info.data,
      });
    } else {
      Modal.error({ title: msg });
    }
  },
  // 获取部门数据
  * getDeptList() {
    const { code, msg, info } = yield getDeptByStartEndAPI({
      dept1: '800000',
      rootDeptId: '800000',
      startLevel: 1,
      endLevel: 7,
    });

    if (code === '410101') {
      yield this.changeData({
        deptsPermission: false,
      });
      return;
    }
    if (code === '0') {
      yield this.changeData({
        allDeptOptions: info.data,
        deptsPermission: true,
      });
    } else {
      Modal.error({ title: msg });
    }
  },
  * handleFourthDeptChange(value) {
    const { fourthDeptList } = yield '';

    yield this.changeLimitData({
      deptFourIds: value,
      deptFiveIds: [],
    });
    // 重新获取五级部门数据
    yield this.getFifthDeptList({
      parentIdList: value?.length ? value : fourthDeptList.map((item) => item.deptId),
    });
  },
};
