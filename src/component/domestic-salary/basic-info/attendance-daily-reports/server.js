import { sendPostRequest } from '@src/server/common/public';
import fileFetch from '@src/server/common/fileFetch';

// interface IRequestBody {
//   /** 考勤下班卡状态，数据字典：WBMS_SALARY_ATTEND_END_STATUS */
//   attendEndStatus?:number[];
//   /** 考勤上班卡状态，数据字典：WBMS_SALARY_ATTEND_START_STATUS */
//   attendStartStatus?:number[];
//   /** 开始日期，格式：YYYY-MM-DD */
//   beginDay?:string;
//   /** 最后工作日，开始日期，格式：YYYY-MM-DD */
//   beginTerminationDate?:string;
//   /** 日历类型 */
//   calendarTypes?:any;
//   /** 三级部门(片区) */
//   permissionIds?:number[];
//   dept3IdsReal?:string[];
//   /** 四级部门 */
//   deptFourIds?:string[];
//   /** 部门id列表 */
//   depts?:string[];
//   /** 结束日期，格式：YYYY-MM-DD */
//   endDay?:string;
//   /** 最后工作日，结束日期，格式：YYYY-MM-DD */
//   endTerminationDate?:string;
//   /** 五级部门名称 */
//   deptFiveIds?:string;
//   /** id列表 */
//   ids?:number[];
//   /** 夜班补贴，数据字典：WBMS_SALARY_NIGHT_BONUS */
//   nightBonus?:number;
//   /** 老员工属性，数据字典：WBMS_SALARY_USER_IS_OLD */
//   oldStatus?:number;
//   /** 当前页码 */
//   pageNum?:number;
//   /** 页宽度 */
//   pageSize?:number;
//   /** 岗位 */
//   posnJbs?:string[];
//   /** 作业子仓识别失败 ture-开启， false-关闭 */
//   subWarehouseNullFlag?:boolean;
//   /** 工号 */
//   uid?:string;
//   /** 班次类型，数据字典：WBMS_SALARY_WORK_TIME_TYPE */
//   workTimeType?:number;
// }

/**
 * 列表查询接口
 * @see https://soapi.sheincorp.cn/application/4361/routes/post_wbms_front_salary_attendance_day_query_list/doc
 */
export const getListAPI = (param) => sendPostRequest({
  url: '/salary_attendance/day/query_list',
  param,
}, process.env.WBMS_FRONT);

/**
 * 导出
 * @see https://soapi.sheincorp.cn/application/4361/routes/post_wbms_front_salary_attendance_day_query_list_export/doc
 */
export const exportListAPI = (param) => sendPostRequest({
  url: '/salary_attendance/day/query_list/export',
  param,
}, process.env.WBMS_FRONT);

// 获取部门下拉
export const queryAllDeptAPI = (param) => sendPostRequest({
  url: '/salary_dept/query_tree',
  param,
}, process.env.WBMS_FRONT);

// 下载模板
export const downloadAPI = () => {
  const uri = `${process.env.WBMS_FRONT}/salary_attendance/day/megathermal_day/download`;
  return fileFetch(uri, {
    method: 'POST',
    credentials: 'include',
  });
};

// 批量修改存储属性导入接口
export const importUrl = `${process.env.WGS_FRONT}/file_import/record/wbms/salary_megathermal_day_adjust_actuator`;

// 下载模板
export const downloadAdjustSubwarehouseTemplateAPI = () => {
  const uri = `${process.env.WBMS_FRONT}/salary_attendance/sub_warehouse/megathermal_day/download`;
  return fileFetch(uri, {
    method: 'POST',
    credentials: 'include',
  });
};

// 导入接口
export const imporAdjustSubwarehousetUrl = `${process.env.WGS_FRONT}/file_import/record/wbms/megathermal_day_sub_warehouse_actuator`;

// 日报调整模板下载
export const downloadSalaryDayAttendanceModifyTemplateAPI = () => {
  const uri = `${process.env.WBMS_FRONT}/salary_attendance/day/modify/download`;
  return fileFetch(uri, {
    method: 'POST',
    credentials: 'include',
  });
};
// 日报调整导入接口
export const imporSalaryDayAttendanceModifyUrl = `${process.env.WGS_FRONT}/file_import/record/wbms/salary_day_attendance_modify`;

/**
 * 获取三级部门(片区)权限数据
 * @see https://soapi.sheincorp.cn/application/4361/routes/post_wbms_front_permission_biz_query_list/doc
 */
export const getPermissionBizListAPI = (param) => sendPostRequest({
  url: '/permission/biz/query_list',
  param,
}, process.env.WBMS_FRONT);

/**
 * 获取四级/五级部门数据
 * @see https://soapi.sheincorp.cn/application/4361/routes/post_wbms_front_std_dept_get_dept_by_parent/doc
 */
export const getDeptByParentAPI = (param) => sendPostRequest({
  url: '/std_dept/get_dept_by_parent',
  param,
}, process.env.WBMS_FRONT);

/**
 * 获取部门数据
 * @see https://soapi.sheincorp.cn/application/4361/routes/post_wbms_front_std_dept_get_dept_start_to_end/doc
 */
export const getDeptByStartEndAPI = (param) => sendPostRequest({
  url: '/salary_dept/get_dept_start_to_end',
  param,
  not403Modal: true,
}, process.env.WBMS_FRONT);
