import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import {
  TreeSelect, DatePicker, Rule, Select,
} from 'shineout';
import moment from 'moment';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import { formatSearchData } from '@public-component/search-queries/utils';
import DateRangePicker from '@shein-components/dateRangePicker2';
import store, { defaultLimit } from '../reducers';

const rule = Rule({
  timeRange: {
    func: (val, formData, callback) => {
      if (moment(formData.endTerminationDate)
        .diff(moment(formData.beginTerminationDate), 'days', true) > 32) {
        callback(new Error(t('开始时间和结束时间不能超过{}天', 32)));
      }
      callback(true);
    },
  },
  firstHireTimeRange: {
    func: (val, formData, callback) => {
      if (moment(formData.endFirstHireDate)
        .diff(moment(formData.beginFirstHireDate), 'days', true) > 32) {
        callback(new Error(t('开始时间和结束时间不能超过{}天', 32)));
      }
      callback(true);
    },
  },
});

class Header extends Component {
  render() {
    const {
      limit,
      loading,
      allDeptOptions,
      userList,
      posnJbListOptions,
      workTypeOptions,
      thirdDeptList,
      fourthDeptList,
      fifthDeptList,
      partOptions,
      isFullAttendanceOptions,
      deptsPermission,
    } = this.props;
    return (
      <section>
        <SearchAreaContainer
          clearUndefined={false}
          // labelStyle={{ width: 60 }} // 统一修改label宽度，默认60
          searching={!loading}
          value={limit}
          // 保留用户所选项，并补全搜索条件
          onChange={(val) => store.changeLimitData(formatSearchData(defaultLimit, val))}
          onClear={() => store.clearLimitData()}
          onSearch={() => {
            // 点搜索按钮，将页码重置为1
            store.handlePaginationChange({ pageNum: 1 });
          }}
          formRef={(f) => store.changeData({ formRef: f })}
          alwaysVisible={[t('日期')]}
        >
          <Select
            autoAdapt
            label={t('三级部门(片区)')}
            name="permissionIds"
            data={thirdDeptList}
            keygen="id"
            format="id"
            renderItem="name"
            placeholder={t('权限域自动带出')}
            disabled
            multiple
            compressed
          />
          <Select
            autoAdapt
            label={t('四级部门')}
            name="deptFourIds"
            data={fourthDeptList}
            keygen="deptId"
            format="deptId"
            renderItem="pdDescrLong"
            placeholder={t('全部')}
            multiple
            compressed
            clearable
            onFilter={(text) => (d) => d.pdDescrLong.indexOf(text) >= 0}
            onChange={(val) => store.handleFourthDeptChange(val)}
          />
          <Select
            autoAdapt
            label={t('五级部门')}
            name="deptFiveIds"
            data={fifthDeptList}
            keygen="deptId"
            format="deptId"
            renderItem="pdDescrLong"
            placeholder={t('全部')}
            multiple
            compressed
            clearable
            onFilter={(text) => (d) => d.pdDescrLong.indexOf(text) >= 0}
          />
          <Select
            autoAdapt
            label={t('姓名')}
            keygen="uname"
            format="uname"
            data={userList}
            style={{ width: 240 }}
            clearable
            onFilter={(v) => { store.handleUnameFilter(v); }}
            renderItem="fullName"
            name="uid"
            placeholder={t('请输入')}
          />
          <DatePicker
            label={t('考勤月份')}
            name="yearMonth"
            type="month"
            rules={[rule.required]}
          />
          <Select
            autoAdapt
            label={t('是否临时工')}
            name="workType"
            data={workTypeOptions}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
            placeholder={t('全部')}
          />
          <Select
            autoAdapt
            label={t('岗位')}
            name="posnJbList"
            data={posnJbListOptions}
            keygen="posnJb"
            format="posnJb"
            renderItem="posnJbName"
            onFilter={(text) => (d) => d.posnJbName.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            multiple
            compressed
            clearable
            placeholder={t('全部')}
          />
          <Select
            autoAdapt
            label={t('环节')}
            name="deptPartTypes"
            data={partOptions}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            placeholder={t('全部')}
            multiple
            compressed
            clearable
          />
          <DateRangePicker
            required
            placeholder={[t('开始时间'), t('结束时间')]}
            type="datetime"
            inputable
            format="yyyy-MM-dd"
            name={['beginTerminationDate', 'endTerminationDate']}
            defaultTime={['00:00:00', '23:59:59']}
            label={t('最后工作日')}
            rules={[rule.timeRange()]}
            span={2}
          />
          <DateRangePicker
            required
            placeholder={[t('开始时间'), t('结束时间')]}
            type="datetime"
            inputable
            format="yyyy-MM-dd"
            name={['beginFirstHireDate', 'endFirstHireDate']}
            defaultTime={['00:00:00', '23:59:59']}
            label={t('入职日期')}
            span={2}
            rules={[rule.firstHireTimeRange()]}
          />
          <Select
            autoAdapt
            label={t('是否满勤')}
            name="fullAttendanceTags"
            data={isFullAttendanceOptions}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            placeholder={t('全部')}
            multiple
            compressed
            clearable
          />
          {
            deptsPermission && (
              <TreeSelect
                label={t('部门')}
                compressed
                name="depts"
                span={2}
                mode={2}
                clearable
                keygen={(d) => d.deptId}
                renderItem="pdDescrLong"
                data={allDeptOptions}
                checkboxFalse
                multiple
                childrenKey="childList"
                onFilter={(text) => (d) => d.pdDescrLong.indexOf(text) >= 0}
              />
            )
          }
        </SearchAreaContainer>
      </section>
    );
  }
}

Header.propTypes = {
  limit: PropTypes.shape().isRequired,
  loading: PropTypes.number.isRequired,
  allDeptOptions: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  userList: PropTypes.arrayOf(PropTypes.shape()),
  posnJbListOptions: PropTypes.arrayOf(PropTypes.shape()),
  workTypeOptions: PropTypes.arrayOf(PropTypes.shape()),
  thirdDeptList: PropTypes.arrayOf(PropTypes.shape()),
  fourthDeptList: PropTypes.arrayOf(PropTypes.shape()),
  fifthDeptList: PropTypes.arrayOf(PropTypes.shape()),
  partOptions: PropTypes.arrayOf(PropTypes.shape()),
  isFullAttendanceOptions: PropTypes.arrayOf(PropTypes.shape()),
  deptsPermission: PropTypes.bool,
};
export default Header;
