import { markStatus } from 'rrc-loader-helper';
import { Modal } from 'shineout';
import { t } from '@shein-bbl/react';
import { handleListMsg } from '@src/lib/dealFunc';
import { getSize } from '@src/middlewares/pagesize';
import { clearEmpty } from '@src/lib/deal-func';
import { formdataPost } from '@src/server/common/fileFetch';
import { dictSelect } from '@src/server/basic/dictionary';
import fileSaver from 'file-saver';

import moment from 'moment';
import { STATISTICAL_IMPORT_CENTER } from '@src/lib/jump-url';
import { queryByFullNameAPI, querySalaryPsonJbAPI } from '@src/server/wbms/server';
import {
  getListAPI,
  exportListAPI,
  downloadTemplateAPI,
  importSalaryMonthAttendanceModifyUrl,
  downloadSalaryMonthAttendanceModifyTemplateAPI,
  downloadShortMonitorModifyTemplateAPI,
  importShortMonitorModifyUrl,
  getUserFullDeptAPI,
  getPermissionBizListAPI,
  getDeptByParentAPI,
  getDeptByStartEndAPI,
} from './server';

// 搜索区域：搜索导出条件，若有仓库则使用nav右上角的仓库
export const defaultLimit = {
  uid: '', // 工号（即uid）
  yearMonth: moment().format('YYYY-MM'),
  workType: '', // 员工类型
  posnJbList: [], // 岗位
  beginTerminationDate: '', // 最后工作日开始时间
  endTerminationDate: '', // 最后工作日结束时间
  beginFirstHireDate: '', // 入职日期开始
  endFirstHireDate: '', // 入职日期结束
  permissionIds: [], // 三级部门(片区)
  deptFourIds: [], // 四级部门
  deptFiveIds: [], // 五级部门
  depts: [], // 部门
  deptPartTypes: [], // 环节
  fullAttendanceTags: [], // 是否满勤
};

const defaultState = {
  warehouseId: undefined, // 仓库
  formRef: {}, // 绑定 form 的引用, 可以调用某些 form 的方法  validate(校验表单) 具体可以查看 https://shineout.biz.sheincorp.cn/1.6.x/cn/components/Form
  // 搜索区域+下拉数据
  loading: 1, // 0加载中 1加载成功 2加载失败
  topSearch: true,
  limit: defaultLimit,
  // 列表区域+分页区域
  pageInfo: {
    pageNum: 1,
    pageSize: getSize(),
    count: 0, // 表格总条数
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  list: [], // 表格数据
  selectedRows: [], // 选中行数据
  userList: [], // 用户下拉数据
  posnJbListOptions: [],
  workTypeOptions: [
    {
      dictCode: 1,
      dictNameZh: t('正式工'),
    },
    {
      dictCode: 2,
      dictNameZh: t('临时工'),
    },
  ],
  thirdDeptList: [], // 三级部门下拉数据
  fourthDeptList: [], // 四级部门下拉数据
  fifthDeptList: [], // 五级部门下拉数据
  allDeptOptions: [], // 部门下拉
  deptsPermission: false,
  partOptions: [], // 环节选项
  isFullAttendanceOptions: [ // 是否满勤选项
    { dictCode: '1', dictNameZh: t('是') },
    { dictCode: '0', dictNameZh: t('否') },
  ],
  monthReportModifyModalVisible: false, // 月报修正弹窗
  dataLoading: false,
  recordVisible: false, // 操作记录弹窗
  recordId: '', // 操作记录id
};

export default {
  state: defaultState,
  // 改state属性值
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件即state.limit属性值
  changeLimitData(state, data) {
    Object.assign(state.limit, data);
  },
  // 改搜索条件新增/编辑对象属性值
  changeEdit: (state, data) => {
    Object.assign(state.editObj, data);
  },
  // 重置查询条件
  * clearLimitData() {
    const { formRef, limit } = yield '';
    yield this.changeLimitData({
      ...defaultLimit,
      permissionIds: limit.permissionIds,
    });
    yield this.getFourthDeptList(limit.permissionIds);
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },
  // 页面初始化
  * init() {
    // 请求下拉框数据
    markStatus('loading');
    const [posnRes, dictRes] = yield Promise.all([
      querySalaryPsonJbAPI({}),
      dictSelect({ catCode: ['WBMS_SALARY_OA_USER_DEPT_PART'] }),
    ]);
    if (posnRes.code === '0' && dictRes.code === '0') {
      yield this.changeData({
        posnJbListOptions: posnRes.info.data, // 岗位下拉
        partOptions: dictRes.info.data.find((item) => item.catCode === 'WBMS_SALARY_OA_USER_DEPT_PART').dictListRsps || [],
      });
      // 获取三级部门数据
      yield this.getThirdDeptList();
      // 获取部门下拉
      yield this.getDeptList();
    } else {
      handleListMsg([posnRes, dictRes], false);
    }
  },
  /**
   * 搜索
   */
  * search() {
    const { limit, pageInfo } = yield '';
    const param = {
      ...limit,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
    };
    markStatus('loading');
    const { code, info, msg } = yield getListAPI(clearEmpty(param, [0]));
    if (code === '0') {
      yield this.changeData({
        // 有复选框的，要重置对应字段成空数组
        list: info.data,
        pageInfo: {
          ...pageInfo,
          count: info.meta.count,
        },
        selectedRows: [],
      });
    } else {
      Modal.error({ title: msg });
    }
  },

  /**
   * 校验
   * @returns
   */
  * handleFormValidate() {
    const { formRef } = yield '';
    // 兼容跳转进来触发搜索时，dom还没渲染完，执行formRef.validate()报错
    if (!formRef || !formRef.validate) {
      return true;
    }
    let validateFlag = false;
    yield formRef.validate().then(() => {
      validateFlag = true;
    }).catch(() => {
      validateFlag = false;
    });
    return validateFlag;
  },

  /**
   * 分页条改变
   * @param {object} data 分页数组主要有pageNum pageSize
   * @returns
   */
  * handlePaginationChange(data = {}) {
    // 校验
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    // 校验通过后执行
    const { pageInfo } = yield '';
    yield this.changeData({
      pageInfo: {
        ...pageInfo,
        ...data,

      },
    });
    yield this.search();
  },
  // 导出
  * exportData() {
    // 校验
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    // 校验通过后执行
    const { limit } = yield '';
    // 一般导出接口不用传页数页码，因为是要导出全部数据：部分旧页面有传就与旧页面一致；新页面跟后端统一按规范不用传页数页码
    // 实际开发时，根据情况决定是否加仓库id，页数页码等字段
    const param = {
      ...limit,
      pageNum: 1,
      pageSize: 100000,
    };
    markStatus('loading');
    const { code, msg } = yield exportListAPI(param);
    if (code === '0') {
      window.open('#/statistical/download');
    } else {
      Modal.error({ title: msg });
    }
  },
  // 下载模板
  * downloadTemplate() {
    try {
      markStatus('loading');
      const res = yield downloadTemplateAPI();
      const b = yield res.blob();
      const blob = new Blob([b], { type: 'application/octet-stream' });
      fileSaver.saveAs(blob, `${t('模版下载')}.xlsx`);
    } catch (e) {
      Modal.error({ title: e.reason.message });
    }
  },
  // 导入
  // * import() {
  //   const { file } = yield '';
  //   if (file?.length === 0) {
  //     Message.error(t('请先选择需要导入的文件'));
  //     return;
  //   }
  //   const formData = new FormData();
  //   formData.append('file', file[0]);
  //   formData.append('function_node', '98');
  //   const res = yield formdataPost(importUrl, formData);
  //   if (res.code === '0') {
  //     window.open(STATISTICAL_IMPORT_CENTER);
  //     yield this.changeData({
  //       importVisible: false,
  //       file: [],
  //     });
  //     yield this.handlePaginationChange({ pageNum: 1 });
  //   } else {
  //     Modal.error({ content: res.msg });
  //   }
  // },
  // 输入人名进行搜索
  * handleUnameFilter(v) {
    if (!v) { return; }
    const { code, info, msg } = /^\d/.test(v) // 判断当前输入的是否是数字开头
      ? yield queryByFullNameAPI({
        uname: v, // 工号
        pageNum: 1,
        pageSize: 50,
      }) : yield queryByFullNameAPI({
        fullName: v, // 中文名
        pageNum: 1,
        pageSize: 50,
      });
    if (code === '0') {
      yield this.changeData({ userList: info });
    } else {
      Modal.error({ title: msg });
    }
  },

  /**
   * 考勤月报数据修正导入
   * @param {*} formData
   */
  * uploadMonthReportModifyFile(formData) {
    yield this.changeData({
      dataLoading: true,
    });
    formData.append('function_node', '169');
    const res = yield formdataPost(importSalaryMonthAttendanceModifyUrl, formData);
    yield this.changeData({
      dataLoading: false,
    });
    if (res.code === '0') {
      window.open(STATISTICAL_IMPORT_CENTER);
      yield this.changeData({
        monthReportModifyModalVisible: false,
      });
    } else {
      Modal.error({ content: res.msg });
    }
  },
  /**
 * 下载考勤月报数据修正导入模板
 */
  * downloadMonthReportModifyTemplate() {
    yield this.changeData({
      dataLoading: true,
    });
    const res = yield downloadSalaryMonthAttendanceModifyTemplateAPI();
    yield this.changeData({
      dataLoading: false,
    });
    if (res.ok) {
      res.blob().then((b) => {
        const blob = new Blob([b], { type: 'application/octet-stream' });
        fileSaver.saveAs(blob, t('考勤月报数据修正导入模板.xls'));
      });
    } else {
      res.json().then((err) => {
        Modal.error({ title: err.msg });
      });
    }
  },

  /**
   * 考勤临时班长数据修正导入
   * @param {*} formData
   */
  * uploadShortMonitorModifyFile(formData) {
    yield this.changeData({
      dataLoading: true,
    });
    formData.append('function_node', '170');
    const res = yield formdataPost(importShortMonitorModifyUrl, formData);
    yield this.changeData({
      dataLoading: false,
    });
    if (res.code === '0') {
      window.open(STATISTICAL_IMPORT_CENTER);
      yield this.changeData({
        shortMonitorModifyModalVisible: false,
      });
    } else {
      Modal.error({ content: res.msg });
    }
  },
  /**
* 下载临时班长数据修正导入模板
*/
  * downloadShortMonitorModifyTemplate() {
    yield this.changeData({
      dataLoading: true,
    });
    const res = yield downloadShortMonitorModifyTemplateAPI();
    yield this.changeData({
      dataLoading: false,
    });
    if (res.ok) {
      res.blob().then((b) => {
        const blob = new Blob([b], { type: 'application/octet-stream' });
        fileSaver.saveAs(blob, t('临时班长数据修正导入模板.xls'));
      });
    } else {
      res.json().then((err) => {
        Modal.error({ title: err.msg });
      });
    }
  },
  /**
       * 获取部门
       */
  * getUserFullDept(record) {
    markStatus('loading');
    const { code, info, msg } = yield getUserFullDeptAPI({ uname: record.workCode });
    if (code === '0') {
      const { list } = yield '';
      yield this.changeData({
        list: list.map((item) => {
          if (item.workCode === record.workCode) {
            return {
              ...item,
              dept: info.fullDept,
            };
          }
          return item;
        }),
      });
    } else {
      Modal.error({ title: msg });
    }
  },
  // 获取三级部门数据
  * getThirdDeptList() {
    const { code, info, msg } = yield getPermissionBizListAPI({ type: '1' });
    if (code === '0') {
      yield this.changeData({
        thirdDeptList: info.data,
      });
      // 自动设置默认值
      yield this.changeLimitData({
        permissionIds: info.data.map((item) => item.id),
      });
      // 获取四级部门数据
      yield this.getFourthDeptList(info.data.map((item) => item.id));
    } else {
      Modal.error({ title: msg });
    }
  },
  // 获取四级部门数据
  * getFourthDeptList(parentPermissionIds) {
    if (!parentPermissionIds || parentPermissionIds.length === 0) {
      yield this.changeData({ fourthDeptList: [] });
      return;
    }
    const { code, msg, info } = yield getDeptByParentAPI({
      dept1: '800000',
      parentPermissionIdList: parentPermissionIds,
    });

    if (code === '0') {
      yield this.changeData({
        fourthDeptList: info.data,
      });
      // 获取五级部门数据
      yield this.getFifthDeptList({
        parentIdList: info.data.map((item) => item.deptId),
      });
    } else {
      Modal.error({ title: msg });
    }
  },
  // 获取五级部门数据
  * getFifthDeptList({ parentPermissionIds = [], parentIdList = [] }) {
    const params = {
      dept1: '800000',
    };
    if (parentPermissionIds.length) {
      params.parentPermissionIdList = parentPermissionIds;
    }
    if (parentIdList.length) {
      params.parentIdList = parentIdList;
    }
    const { code, msg, info } = yield getDeptByParentAPI(params);

    if (code === '0') {
      yield this.changeData({
        fifthDeptList: info.data,
      });
    } else {
      Modal.error({ title: msg });
    }
  },
  // 获取部门数据
  * getDeptList() {
    const { code, msg, info } = yield getDeptByStartEndAPI({
      dept1: '800000',
      rootDeptId: '800000',
      startLevel: 1,
      endLevel: 7,
    });

    if (code === '410101') {
      yield this.changeData({
        deptsPermission: false,
      });
      return;
    }

    if (code === '0') {
      yield this.changeData({
        allDeptOptions: info.data,
        deptsPermission: true,
      });
    } else {
      Modal.error({ title: msg });
    }
  },
  * handleFourthDeptChange(value) {
    const { fourthDeptList } = yield '';

    yield this.changeLimitData({
      deptFourIds: value,
      deptFiveIds: [],
    });
    // 重新获取五级部门数据
    yield this.getFifthDeptList({
      parentIdList: value?.length ? value : fourthDeptList.map((item) => item.deptId),
    });
  },
};
