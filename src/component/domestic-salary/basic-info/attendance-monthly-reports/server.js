import { sendPostRequest } from '@src/server/common/public';
import fileFetch from '@src/server/common/fileFetch';
import { camel2Under } from '@src/lib/camal-case-convertor';

// interface IRequestBody {
//   /** 入职时间，开始日期，格式：YYYY-MM-DD */
//   beginFirstHireDate?:string;
//   /** 最后工作日，开始日期，格式：YYYY-MM-DD */
//   beginTerminationDate?:string;
//   /** 三级部门(片区) */
//   permissionIds?:number[];
//   dept3IdsReal?:string[];
//   /** 四级部门 */
//   deptFourIds?:string[];
//   /** 五级部门 */
//   deptFiveIds?:string[];
//   /** 环节 */
//   deptPartTypes?:any;
//   /** 部门id列表 */
//   depts?:string[];
//   /** 入职时间，结束日期，格式：YYYY-MM-DD */
//   endFirstHireDate?:string;
//   /** 最后工作日，结束日期，格式：YYYY-MM-DD */
//   endTerminationDate?:string;
//   /** 是否满勤 (1: 是, 0: 否) */
//   fullAttendanceTags?:any;
//   /** id列表 */
//   ids?:number[];
//   /** 当前页码 */
//   pageNum?:number;
//   /** 页宽度 */
//   pageSize?:number;
//   /** 岗位 */
//   posnJbList?:string[];
//   /** 工号 */
//   uid?:string;
//   /** 员工类型 */
//   workType?:string;
//   /** 年月 */
//   yearMonth?:string;
// }

/**
 * 列表查询接口
 * @see https://soapi.sheincorp.cn/application/4361/routes/post_wbms_front_salary_attendance_month_query_list/doc
 */
export const getListAPI = (param) => sendPostRequest({
  url: '/salary_attendance/month/query_list',
  param,
}, process.env.WBMS_FRONT);

/**
 * 导出
 * @see https://soapi.sheincorp.cn/application/4361/routes/post_wbms_front_salary_attendance_month_query_list_export/doc
 */
export const exportListAPI = (param) => sendPostRequest({
  url: '/salary_attendance/month/query_list/export',
  param,
}, process.env.WBMS_FRONT);

// 下载模版
export const downloadTemplateAPI = () => {
  const uri = `${process.env.WBMS_FRONT}/salary_attendance/month/spring_holiday_import/download`;
  return fileFetch(uri, {
    method: 'POST',
    credentials: 'include',
    body: JSON.stringify(camel2Under()),
  });
};

// 导入
export const importUrl = `${process.env.WGS_FRONT}/file_import/record/wbms/spring_festival_holiday_actuator`;

// 月报调整模板下载
export const downloadSalaryMonthAttendanceModifyTemplateAPI = () => {
  const uri = `${process.env.WBMS_FRONT}/salary_attendance/month/modify/download`;
  return fileFetch(uri, {
    method: 'POST',
    credentials: 'include',
  });
};
// 月报调整导入接口
export const importSalaryMonthAttendanceModifyUrl = `${process.env.WGS_FRONT}/file_import/record/wbms/salary_month_attendance_modify`;

// 临时班长调整模板下载
export const downloadShortMonitorModifyTemplateAPI = () => {
  const uri = `${process.env.WBMS_FRONT}/salary_attendance/month/short_monitor/download`;
  return fileFetch(uri, {
    method: 'POST',
    credentials: 'include',
  });
};
// 临时班长调整导入接口
export const importShortMonitorModifyUrl = `${process.env.WGS_FRONT}/file_import/record/wbms/salary_month_segment_import`;

// 查询用户部门全路径
export const getUserFullDeptAPI = (param) => sendPostRequest({
  url: '/salary_user/get_user_full_dept',
  param,
}, process.env.WBMS_FRONT);

/**
 * 获取三级部门(片区)权限数据
 * @see https://soapi.sheincorp.cn/application/4361/routes/post_wbms_front_permission_biz_query_list/doc
 */
export const getPermissionBizListAPI = (param) => sendPostRequest({
  url: '/permission/biz/query_list',
  param,
}, process.env.WBMS_FRONT);

/**
 * 获取四级/五级部门数据
 * @see https://soapi.sheincorp.cn/application/4361/routes/post_wbms_front_std_dept_get_dept_by_parent/doc
 */
export const getDeptByParentAPI = (param) => sendPostRequest({
  url: '/std_dept/get_dept_by_parent',
  param,
}, process.env.WBMS_FRONT);

/**
 * 获取部门数据
 * @see https://soapi.sheincorp.cn/application/4361/routes/post_wbms_front_std_dept_get_dept_start_to_end/doc
 */
export const getDeptByStartEndAPI = (param) => sendPostRequest({
  url: '/salary_dept/get_dept_start_to_end',
  param,
  not403Modal: true,
}, process.env.WBMS_FRONT);
