import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import SensitiveTable from '@public-component/search-queries/new-sensitive-table';
import {
  Button, Modal, Form, DatePicker, Rule,
} from 'shineout';
import DateRangePicker from '@shein-components/dateRangePicker2';
import InputMore from '@shein-components/inputMore';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import { handleTablePros } from '@src/lib/deal-func';
import styles from '@src/component/style.less';
import store from '../reducers';
import style from '../style.less';

const rule = Rule();

class List extends React.Component {
  render() {
    const {
      list,
      configModalVisible,
      loading,
      configModalObj,
      currConfigJob,
    } = this.props;

    const columns = [
      {
        title: t('ID'),
        render: 'dictCode',
        width: 200,
      },
      {
        title: t('描述'),
        render: 'dictNameZh',
        width: 400,
      },
      {
        title: t('操作'),
        render: (record) => (
          <Button
            size="small"
            text
            type="primary"
            onClick={() => {
              store.hanldeConfigjob(record);
            }}
          >
            {t('配置任务')}
          </Button>
        ),
        width: 200,
      },
    ];

    return (
      <section className={styles.tableSection}>
        <div className={style.jobTitleStyle}>{t('JOB 任务清单 —— 计薪')}</div>
        <SearchAreaTable>
          <SensitiveTable
            {...handleTablePros(columns)}
            style={{ height: 'calc(100% - 55px)' }}
            data={list}
            keygen="id"
          />
        </SearchAreaTable>

        <Modal
          maskCloseAble={null}
          visible={configModalVisible}
          width={750}
          destroy
          title={t('{}-任务配置', currConfigJob.dictNameZh)}
          onClose={() => {
            store.handleCloseConfigModal();
          }}
          footer={(
            <div>
              <Button onClick={() => store.handleCloseConfigModal()}>{t('取消')}</Button>
              <Modal.Submit loading={!loading} disabled={!loading}>{t('确认')}</Modal.Submit>
            </div>
          )}
          formRef={(f) => store.changeData({ modalFormRef: f })}
        >
          <div className={style.enTitleStyle}>{currConfigJob.dictNameEn}</div>
          <Form
            labelWidth={100}
            labelAlign="right"
            style={{ maxWidth: 800 }}
            onSubmit={() => {
              store.handleCommitRerunSalaryJobJob();
            }}
            onChange={(value) => {
              store.changeData({
                configModalObj: value,
              });
            }}
            value={configModalObj}
            inline
          >
            {/* 月份 */}
            {[1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 16, 18].includes(currConfigJob.dictCode) && (
              <Form.Item
                required={[1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 16, 18].includes(currConfigJob.dictCode)}
                label={t('月份')}
              >
                <DatePicker
                  width={200}
                  name="ym"
                  type="month"
                  format="yyyyMM"
                  rules={[1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 16, 18].includes(currConfigJob.dictCode) ? [rule.required(t('月份必填'))] : []}
                />
              </Form.Item>
            )}

            {/* 工号 */}
            {[1, 2, 4, 5, 8, 9, 11, 13, 15, 16, 17].includes(currConfigJob.dictCode) && (
              <Form.Item
                label={t('工号')}
              >
                <InputMore
                  value={configModalObj.unameList}
                  title={t('添加多个工号,以回车键隔开')}
                  placeholder={t('请输入')}
                  modalplaceholder={t('支持输入多个工号')}
                  text={{ cancle: t('取消'), reset: t('重置'), ok: t('确定') }}
                  name="unameList"
                  maskCloseAble={false}
                  width={200}
                  overDisabled
                  clearable
                  onChange={(val) => {
                    store.changeData({
                      configModalObj: {
                        ...configModalObj,
                        unameList: val,
                      },
                    });
                  }}
                />
              </Form.Item>
            )}

            {/* ABC推送日期 */}
            {[5, 9].includes(currConfigJob.dictCode) && (
              <Form.Item
                required={[5, 9].includes(currConfigJob.dictCode)}
                label={t('ABC推送日期')}
              >
                <DatePicker
                  width={200}
                  name="biDataCreateDate"
                  type="date"
                  format="YYYY-MM-DD"
                  rules={[5, 9].includes(currConfigJob.dictCode) ? [rule.required(t('月份必填'))] : []}
                />
              </Form.Item>
            )}

            {/* 时间 */}
            {[2, 5, 6, 8, 9, 10, 15, 17].includes(currConfigJob.dictCode) && (
              <Form.Item
                required={[2, 15, 17].includes(currConfigJob.dictCode)}
                label={t('时间')}
              >
                <Form.Field
                  name={['startDate', 'endDate']}
                  rules={[2, 15, 17].includes(currConfigJob.dictCode) ? [rule.required(t('时间必填'))] : []}
                >
                  {({ value, onChange }) => (
                    <DateRangePicker
                      name={['startDate', 'endDate']}
                      required
                      placeholder={[t('开始时间'), t('结束时间')]}
                      value={[value[0], value[1]]}
                      width={538}
                      type="date"
                      format="yyyy-MM-dd"
                      onChange={(dates) => onChange(
                        [
                          dates[0],
                          dates[1],
                        ],
                      )}
                    />
                  )}
                </Form.Field>
              </Form.Item>
            )}
          </Form>
        </Modal>
      </section>
    );
  }
}

List.propTypes = {
  list: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  configModalVisible: PropTypes.bool.isRequired,
  loading: PropTypes.number.isRequired,
  configModalObj: PropTypes.shape(),
  currConfigJob: PropTypes.shape(),
};

export default List;
