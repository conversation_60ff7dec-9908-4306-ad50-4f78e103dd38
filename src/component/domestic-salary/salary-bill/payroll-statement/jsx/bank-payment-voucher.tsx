import React from 'react';
import {
  Modal, Form, Message,
} from 'shineout';
import { t } from '@shein-bbl/react';
import UploadPlus from '@shein-components/upload_plus';
import store from '../reducers';
import { IPageProps } from '../types';

function BankPaymentVoucher(props: IPageProps) {
  const {
    proofList,
    bankPaymentVoucherModalVisible,
  } = props;
  return (
    <Modal
      width={850}
      visible={bankPaymentVoucherModalVisible}
      title={t('银行付款凭证')}
      onClose={() => store.changeData(({ bankPaymentVoucherModalVisible: false }))}
      footer={undefined}
    >
      <Form labelAlign="right" inline>
        <div>
          <UploadPlus
            accept=".xls,.xlsx,.pdf,.docx,.doc,.zip,.jpg,.png,.txt"
            disabled
            fileList={proofList || []}
            autoUploadKeyName="file"
            filePathKeyName="attachmentUrl"
            onFailUpload={async (_, info) => Message.error(info)}
            renderResult={(f) => (
              <a
                key={f.attachmentUrl}
                target="_blank"
                rel="noopener noreferrer"
                href={f.attachmentUrl}
                download
              >
                {f?.attachmentName || t('附件')}
              </a>
            )}
          />
        </div>
      </Form>
    </Modal>
  );
}

export default BankPaymentVoucher;
