import React from 'react';
import {
  Button, Modal, Form, Input, Divider,
} from 'shineout';
import { t } from '@shein-bbl/react';
import store from '../reducers';
import { IPageProps } from '../types';
import style from '../style.less';

function ContractInfo(props: IPageProps) {
  const {
    contractInfo,
    contractModalVisible,
  } = props;
  return (
    <Modal
      width={1024}
      visible={contractModalVisible}
      title={t('合同信息')}
      onClose={() => store.changeData(({ contractModalVisible: false }))}
      footer={(
        <div>
          <Button onClick={() => store.changeData(({ contractModalVisible: false }))}>{t('取消')}</Button>
        </div>
      )}
    >
      <Form labelWidth={140} labelAlign="right" inline value={contractInfo}>
        {/* 表单项 */}
        <Form.Item label={t('合同编码')}>
          <Input name="contractNo" disabled width={300} />
        </Form.Item>
        <Form.Item label={t('合同状态')}>
          <Input name="contractStatusName" disabled width={300} />
        </Form.Item>
        <Form.Item label={t('合同开始日期')}>
          <Input name="contractEffectStartTime" disabled width={300} />
        </Form.Item>
        <Form.Item label={t('合同结束日期')}>
          <Input name="contractEffectEndTime" disabled width={300} />
        </Form.Item>
        <Form.Item label={t('合同币种')}>
          <Input name="mainContractCurrency" disabled width={300} />
        </Form.Item>
        <Form.Item label={t('合同签署公司')}>
          <Input name="mainContractCompany" disabled width={300} />
        </Form.Item>
        <Form.Item label={t('是否框架合同')}>
          <Input name="isFrameworkContractName" disabled width={300} />
        </Form.Item>
        <Divider>{t('合同文件')}</Divider>
        <div>
          {contractInfo?.attachmentList && (contractInfo?.attachmentList || []).map((file) => (
            <div className={style.fileTag}>
              <a target="_blank" rel="noopener noreferrer" href={file.link} download>{file.name}</a>
            </div>
          ))}
        </div>
      </Form>
    </Modal>
  );
}

export default ContractInfo;
