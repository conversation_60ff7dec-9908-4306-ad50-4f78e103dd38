import React from 'react';
import {
  Button, Modal, Form, Input, Select,
} from 'shineout';
import { t } from '@shein-bbl/react';
import store from '../reducers';
import { IPageProps } from '../types';

function CostInfo(props: IPageProps) {
  const {
    costInfo,
    costInfoModalVisible,
    servicePersonalList,
  } = props;
  return (
    <Modal
      width={1024}
      visible={costInfoModalVisible}
      title={t('结算信息')}
      onClose={() => store.changeData(({ costInfoModalVisible: false }))}
      footer={(
        <div>
          <Button onClick={() => store.changeData(({ costInfoModalVisible: false }))}>{t('取消')}</Button>
        </div>
      )}
    >
      <Form labelWidth={140} labelAlign="right" inline value={costInfo}>
        {/* 表单项 */}
        <div style={{ marginBottom: 0 }}>
          <Form.Item label={t('结算编码')}>
            <Input name="serviceProviderCostCode" disabled width={300} />
          </Form.Item>
        </div>
        <Form.Item label={t('账户性质')}>
          <Select
            absolute
            name="servicePersonal"
            width={300}
            disabled
            data={servicePersonalList}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            renderUnmatched={() => ''}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
          />
        </Form.Item>
        <Form.Item label={t('支付方式')}>
          <Input name="payWayName" disabled width={300} />
        </Form.Item>
        <Form.Item label={t('收款账户开户名')}>
          <Input name="collectionBankAccountName" disabled width={300} />
        </Form.Item>
        <Form.Item label={t('收款账号')}>
          <Input name="fareBankAccount" disabled width={300} />
        </Form.Item>
        <Form.Item label={t('开户行所在省')}>
          <Input name="provinceName" disabled width={300} />
        </Form.Item>
        <Form.Item label={t('开户行所在市')}>
          <Input name="cityName" disabled width={300} />
        </Form.Item>
        <Form.Item label={t('开户行所在市代码')}>
          <Input name="cityCode" disabled width={300} />
        </Form.Item>
        <Form.Item label={t('收款银行名称')}>
          <Input name="fareBankType" disabled width={300} />
        </Form.Item>
        <Form.Item label={t('收款银行代码')}>
          <Input name="openBankCode" disabled width={300} />
        </Form.Item>
        <Form.Item label={t('收款账号银行开户行')}>
          <Input name="bankName" disabled width={300} />
        </Form.Item>
        <Form.Item label={t('收款人身份证号')}>
          <Input name="payeeNumber" disabled width={300} />
        </Form.Item>
        <Form.Item label={t('收款人手机号')}>
          <Input name="payeeMobile" disabled width={300} />
        </Form.Item>
      </Form>
    </Modal>
  );
}

export default CostInfo;
