import React from 'react';
import style from '@src/component/style.less';
import {
  Button, Modal, Form, Table, Popover,
} from 'shineout';
import { t } from '@shein-bbl/react';
import { TableColumnItem } from '@src/typing/base';
import store from '../reducers';
import SalaryModal from './salary-modal';
import IncomeTaxAuditModal from './income-tax-audit-modal';
import BankPaymentVoucher from './bank-payment-voucher';
import { IPageProps, IApproveLogItem } from '../types';

function Handle(props: IPageProps) {
  const {
    loading,
    selectedRows,
    recalculateVerifyDataModalVisible,
    verifyOrders,
    approveLogVisible,
    approveLog,
  } = props;
  const approveLogColumns: TableColumnItem<IApproveLogItem>[] = [
    {
      title: t('审批节点'),
      render: 'taskName',
      width: 150,
    },
    {
      title: t('审批人'),
      render: 'assignee',
      width: 150,
    },
    {
      title: t('审核时间'),
      render: 'approvalTime',
      width: 150,
    },
    {
      title: t('审核备注'),
      render: 'approvalRemark',
      width: 150,
    },
    {
      title: t('审核平台'),
      render: 'platform',
      width: 150,
    },
  ];
  return (
    <section className={[style.handle, 'handleSection'].join(' ')}>
      <Button
        type="primary"
        disabled={!loading}
      >
        <Popover.Confirm
          type="warning"
          okType="primary"
          position="top"
          text={{ ok: t('确认'), cancel: t('取消') }}
          onOk={() => {
            store.exportData();
          }}
        >
          {t('是否确认导出?')}
        </Popover.Confirm>
        {t('导出')}
      </Button>
      <Button
        style={{ marginLeft: '8px' }}
        type="primary"
        disabled={
          !selectedRows.length
          || new Set(selectedRows.map((si) => si.approvalNo)).size > 1
          // || selectedRows.some((si) => (![1, 9].includes(Number(si.verifyOrderStatus))))
        }
        onClick={() => {
          store.changeData({
            salaryModalVisible: true,
            currentApprovalNo: selectedRows[0]?.approvalNo,
            isEditMode: true,
          });
          store.getApproveInfo();
        }}
      >
        {t('提交付薪报账')}
      </Button>
      <Button
        style={{ marginLeft: '8px' }}
        type="primary"
        disabled={!selectedRows.length || selectedRows.some((si) => (![2, 6].includes(Number(si.verifyOrderStatus))))}
        onClick={() => {
          store.queryVerifyList();
        }}
      >
        {t('对账单重算')}
      </Button>
      <Button
        type="primary"
        style={{ marginLeft: '8px' }}
        disabled={!selectedRows.length
            || selectedRows.some((si) => (!si.canPushStdPayOrder))}
      >
        <Popover.Confirm
          type="warning"
          okType="primary"
          position="top"
          text={{ ok: t('确认'), cancel: t('取消') }}
          onOk={() => {
            store.rePush();
          }}
        >
          {t('是否确认重推报账单中台?')}
        </Popover.Confirm>
        {t('重推报账单中台')}
      </Button>
      <Button
        style={{ marginLeft: '8px' }}
        type="primary"
        disabled={!selectedRows.length
            || selectedRows.some((si) => (![2].includes(Number(si.taxStatus))))
            || new Set(selectedRows.map((item) => item.verifyOrder)).size > 1}
        onClick={() => {
          store.personalIncomeTaxAudit();
        }}
      >
        {t('个税审核')}
      </Button>
      <Modal
        destroy
        maskCloseAble={null}
        visible={recalculateVerifyDataModalVisible}
        title={t('对账单重算更新')}
        bodyStyle={{ maxHeight: '550px', overflow: 'auto' }}
        onClose={() => {
          store.changeData({
            recalculateVerifyDataModalVisible: false,
          });
        }}
        footer={(
          <div>
            <Button onClick={() => store.changeData({
              recalculateVerifyDataModalVisible: false,
            })}
            >
              {t('取消')}
            </Button>
            <Modal.Submit disabled={!loading}>{t('确认')}</Modal.Submit>
          </div>
        )}
      >
        <Form
          labelWidth={110}
          labelAlign="right"
          onSubmit={() => {
            Modal.confirm({
              title: t('您确认对账单重算更新吗?'),
              onOk: () => {
                store.recalculateVerifyData();
              },
              text: { ok: t('确认'), cancel: t('取消') },
            });
          }}
          inline
        />
        {t('对账单')}
        :
        {verifyOrders.join(',')}
      </Modal>
      <SalaryModal {...props} />
      <Modal
        destroy
        width={1024}
        maskCloseAble={null}
        visible={approveLogVisible}
        title={t('审批日志')}
        bodyStyle={{ maxHeight: '550px', overflow: 'auto' }}
        onClose={() => {
          store.changeData({
            approveLogVisible: false,
          });
        }}
        footer={undefined}
      >
        <h4>
          {t('审批单号')}
          ：
          {approveLog?.approvalCode}
        </h4>
        <Table<IApproveLogItem, IApproveLogItem[]>
          style={{ maxHeight: '350px' }}
          rowClassName={() => style.borderInner}
          bordered
          fixed="both"
          loading={!loading}
          data={approveLog.list}
          columns={approveLogColumns}
          keygen="approvalTime"
          empty={t('暂无数据')}
          size="small"
          width={approveLogColumns.reduce((pre, current) => pre + (current.width || 100), 50)}
          pagination={undefined}
        />
      </Modal>
      <IncomeTaxAuditModal {...props} />
      <BankPaymentVoucher {...props} />
    </section>
  );
}

export default Handle;
