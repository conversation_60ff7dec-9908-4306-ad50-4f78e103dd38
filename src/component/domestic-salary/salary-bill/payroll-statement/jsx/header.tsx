import React from 'react';
import { t } from '@shein-bbl/react';
import {
  Input, Select, DatePicker,
} from 'shineout';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import { formatSearchData } from '@public-component/search-queries/utils';
import type { FormRef } from 'shineout/lib/Form/Props';
import store, { defaultLimit } from '../reducers';
import { ILimitType, IPageProps } from '../types';

function Header(props: IPageProps) {
  const {
    loading,
    limit,
    verifyOrderStatusOptions,
    payOrderStatusOptions,
    subBusinessCategoryOptions,
    taxStatusOptions,
  } = props;

  return (
    <section>
      {/* 高级搜索 */}
      <SearchAreaContainer
        value={limit}
        labelStyle={{ width: 110 }}
        searching={!loading}
        collapseOnSearch={false}
        clearUndefined={false}
        onSearch={() => {
          store.handlePaginationChange({ pageNum: 1 });
        }}
        onChange={(val: ILimitType) => {
          // 业务需求隐藏表单就设置默认值
          store.changeLimitData(formatSearchData(defaultLimit, val));
        }}
        onClear={() => store.clearLimitData()}
        formRef={(f: FormRef<ILimitType>) => {
          store.changeData({
            formRef: f,
          });
        }}
      >
        <Input
          label={t('对账单号')}
          name="verifyOrder"
          placeholder={t('请输入')}
          clearable
        />
        <Input
          label={t('报账单号')}
          name="payOrder"
          placeholder={t('请输入')}
          clearable
        />
        <DatePicker
          label={t('期间')}
          type="month"
          name="ym"
          format="yyyyMM"
        />
        <Input
          label={t('公司')}
          name="company"
          placeholder={t('请输入')}
          clearable
        />
        <Input
          label={t('费用所属公司')}
          name="costCompany"
          placeholder={t('请输入')}
          clearable
        />
        <Select
          autoAdapt
          label={t('对账单状态')}
          name="verifyOrderStatus"
          data={verifyOrderStatusOptions}
          keygen="dictCode"
          format="dictCode"
          renderItem="dictNameZh"
          onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          clearable
          placeholder={t('全部')}
        />
        <Select
          autoAdapt
          label={t('报账单状态')}
          name="payOrderStatus"
          data={payOrderStatusOptions}
          keygen="dictCode"
          format="dictCode"
          renderItem="dictNameZh"
          onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          clearable
          placeholder={t('全部')}
        />
        <Select
          autoAdapt
          label={t('业务子类')}
          name="subBusinessCategory"
          data={subBusinessCategoryOptions}
          keygen="dictCode"
          format="dictCode"
          renderItem="dictNameZh"
          onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          clearable
          placeholder={t('全部')}
        />
        <Input
          label={t('审批单号')}
          name="approvalNo"
          placeholder={t('请输入')}
          clearable
        />
        <Select
          autoAdapt
          label={t('个税状态')}
          name="taxStatus"
          data={taxStatusOptions}
          keygen="dictCode"
          format="dictCode"
          renderItem="dictNameZh"
          onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          clearable
          placeholder={t('全部')}
        />
      </SearchAreaContainer>
    </section>
  );
}

export default Header;
