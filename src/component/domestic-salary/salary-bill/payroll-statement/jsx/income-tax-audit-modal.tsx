import React from 'react';
import {
  Button, Form, Message, Modal, Rule, Textarea, Radio,
} from 'shineout';
import { t } from '@shein-bbl/react';
import { uploadFileURL } from '@src/server/basic/upload';
import UploadPlus from '@shein-components/upload_plus';
import style from '../style.less';
import store from '../reducers';
import { IPageProps } from '../types';

const rules = Rule();

function IncomeTaxAudit(props: IPageProps) {
  const {
    loading,
    personalIncomeTaxInfo,
    personalIncomeTaxModalVisible,
  } = props;
  return (
    <Modal
      maskCloseAble={null}
      key={personalIncomeTaxModalVisible}
      visible={personalIncomeTaxModalVisible}
      width={450}
      title={<div style={{ textAlign: 'center' }}>{t('供应商个税确认')}</div>}
      onClose={() => {
        store.changeData({
          personalIncomeTaxModalVisible: false,
        });
      }}
      footer={(
        <div>
          <Button onClick={() => store.changeData({
            personalIncomeTaxModalVisible: false,
          })}
          >
            {t('取消')}
          </Button>
          <Modal.Submit disabled={!loading}>{t('确认')}</Modal.Submit>
        </div>
      )}
    >
      <Form
        labelWidth={90}
        labelAlign="right"
        style={{ maxWidth: 450 }}
        onSubmit={() => {
          store.taxCheck();
        }}
        onChange={(value) => {
          store.changeData({
            personalIncomeTaxInfo: value,
          });
        }}
        value={personalIncomeTaxInfo}
        formRef={(f) => store.changeData({ formRef: f })}
      >
        <Form.Item label={t('供应商:')} className={style.formItem}>
          {personalIncomeTaxInfo?.companyDesc}
        </Form.Item>
        <Form.Item label={t('对账单:')} className={style.formItem}>
          {personalIncomeTaxInfo?.verifyOrder}
        </Form.Item>
        <Form.Item label="">
          <Radio.Group
            name="checkStatus"
            data={[
              {
                dictCode: 1,
                dictNameZh: t('通过'),
              },
              {
                dictCode: 2,
                dictNameZh: t('驳回'),
              },
            ]}
            renderItem="dictNameZh"
            format="dictCode"
            keygen="dictNameZh"
            onChange={(val) => {
              store.changeData({
                personalIncomeTaxInfo: {
                  ...personalIncomeTaxInfo,
                  checkStatus: val,
                  rejectReason: '',
                },
              });
            }}
          />
        </Form.Item>
        {personalIncomeTaxInfo?.checkStatus === 2 && (
        <Form.Item required label={t('驳回原因')}>
          <Textarea
            label={t('驳回原因')}
            name="rejectReason"
            rows={4}
            maxLength={200}
            rules={[rules.required(t('请输入驳回原因,最多200字'))]}
            clearable
          />
        </Form.Item>
        )}
        <Form.Item label={t('文件上传')}>
          <UploadPlus
            accept=".xls,.xlsx,.pdf,.docx,.doc,.zip,.jpg,.png,.txt"
            limit={1}
            autoUpload
            action={uploadFileURL}
            data={{
              is_use_origin_name: true,
            }}
            maxSize={50}
            fileList={personalIncomeTaxInfo?.fileList || []}
            autoUploadKeyName="file"
            filePathKeyName="fileUrl"
            onFailUpload={async (_, info) => Message.error(info)}
            onDelete={async (removeItem) => {
              const newFiles = (personalIncomeTaxInfo?.fileList || []).filter((file) => file.fileUrl !== removeItem.fileUrl);
              store.changeData({
                personalIncomeTaxInfo: {
                  ...personalIncomeTaxInfo,
                  fileList: newFiles,
                },
              });
            }}
            onSuccessUpload={async ({ file, info }) => {
              const fileItem = {
                fileName: file.name,
                fileUrl: info.image_url,
                fileCompleteUrl: info.image_url,
                name: file.name,
              };
              store.changeData({
                personalIncomeTaxInfo: {
                  ...personalIncomeTaxInfo,
                  fileList: [fileItem],
                },
              });
            }}
            renderResult={(f) => (
              <a
                key={f.fileCompleteUrl}
                target="_blank"
                rel="noopener noreferrer"
                href={f.fileCompleteUrl}
                download
              >
                {f?.fileName || t('附件')}
              </a>
            )}
          />
          <div style={{ padding: '10px 0', color: 'gray' }}>
            {t('注：最多上传一个文件，文件大小50M内')}
          </div>
        </Form.Item>
      </Form>
    </Modal>
  );
}

export default IncomeTaxAudit;
