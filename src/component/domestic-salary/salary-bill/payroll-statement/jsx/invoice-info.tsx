import React from 'react';
import {
  Button, Modal, Form, Input, Divider,
} from 'shineout';
import { t } from '@shein-bbl/react';
import store from '../reducers';
import { IPageProps } from '../types';

function InvoiceInfo(props: IPageProps) {
  const {
    invoiceInfo,
    invoiceModalVisible,
  } = props;
  return (
    <Modal
      width={1024}
      visible={invoiceModalVisible}
      title={t('发票信息')}
      onClose={() => store.changeData(({ invoiceModalVisible: false }))}
      footer={(
        <div>
          <Button onClick={() => store.changeData(({ invoiceModalVisible: false }))}>{t('取消')}</Button>
        </div>
      )}
    >
      <Form labelWidth={140} labelAlign="right" inline value={invoiceInfo}>
        {/* 表单项 */}
        <Form.Item label={t('境内/海外')}>
          <Input name="areaBelongName" disabled width={300} />
        </Form.Item>
        <Form.Item label={t('发票类型')}>
          <Input name="invoiceTypeName" disabled width={300} />
        </Form.Item>
        <Form.Item label={t('类型')}>
          <Input name="mediaTypeName" disabled width={300} />
        </Form.Item>
        <Form.Item label={t('发票币种')}>
          <Input name="invoiceCurrency" disabled width={300} />
        </Form.Item>
        <Form.Item label={t('税种名称')}>
          <Input name="taxTypeName" disabled width={300} />
        </Form.Item>
        <Form.Item label={t('发票代码')}>
          <Input name="invoiceCode" disabled width={300} />
        </Form.Item>
        <Form.Item label={t('发票号码')}>
          <Input name="invoiceNo" disabled width={300} />
        </Form.Item>
        <Form.Item label={t('开票日期')}>
          <Input name="invoiceDate" disabled width={300} />
        </Form.Item>
        <Form.Item label={t('含税金额')}>
          <Input name="invoiceAmount" disabled width={300} />
        </Form.Item>
        <Form.Item label={t('发票税额')}>
          <Input name="taxAmount" disabled width={300} />
        </Form.Item>
        <Form.Item label={t('不含税额')}>
          <Input name="invoiceAmountNoTax" disabled width={300} />
        </Form.Item>
        <Form.Item label={t('数据来源')}>
          <Input name="source" disabled width={300} />
        </Form.Item>
        <Divider>{t('发票文件')}</Divider>
        <div>
          <div>
            <a target="_blank" rel="noopener noreferrer" onClick={() => store.downloadInvoiceFile({ id: invoiceInfo?.id })}>{invoiceInfo?.attachmentName}</a>
          </div>
        </div>
      </Form>
    </Modal>
  );
}

export default InvoiceInfo;
