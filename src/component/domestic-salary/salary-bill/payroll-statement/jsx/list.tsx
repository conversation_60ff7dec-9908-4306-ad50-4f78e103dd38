import React from 'react';
import { t } from '@shein-bbl/react';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import OperationModal from '@public-component/modal/operation-modal';
import style from '@src/component/style.less';
import {
  Button,
  Table,
} from 'shineout';
import { TableColumnItem } from '@src/typing/base';
import ShowMore from '@public-component/other/show-more';
import store from '../reducers';
import { IDataItem, IPageProps } from '../types';
import InvoiceInfo from './invoice-info';
import CostInfo from './cost-info';
import ContractInfo from './contract-info';
import styles from '../style.less';

function List(props: IPageProps) {
  const {
    loading,
    list,
    pageInfo,
    selectedRows,
    customObj,
    recordVisible,
    recordId,
  } = props;

  const columns: TableColumnItem<IDataItem>[] = [
    {
      title: t('期间'),
      render: 'period',
      width: 130,
    },
    {
      title: t('审批单号'),
      width: 220,
      render: (record) => (
        <span
          style={{
            fontSize: 14, lineHeight: 1, padding: 0, color: 'rgb(25, 122, 250)', cursor: 'pointer',
          }}
          onClick={() => {
            store.changeData({
              salaryModalVisible: true,
              currentApprovalNo: record?.approvalNo,
              isEditMode: false,
            });
            store.getApproveInfo();
          }}
        >
          {record.approvalNo}
        </span>
      ),
    },
    {
      title: t('报账方式'),
      render: 'reimbursementMethod',
      width: 150,
    },
    {
      title: t('对账单号'),
      render: 'verifyOrder',
      width: 220,
    },
    {
      title: t('对账单状态'),
      render: 'verifyOrderStatusDescr',
      width: 130,
    },
    {
      title: t('报账单号'),
      render: 'payOrder',
      width: 130,
    },
    {
      title: t('报账单状态'),
      render: 'payOrderStatusDescr',
      width: 130,
    },
    {
      title: t('公司'),
      render: 'companyDescr',
      width: 150,
    },
    {
      title: t('公司编码'),
      render: 'company',
      width: 120,
    },
    {
      title: t('费用所属公司'),
      render: 'costCompanyDescr',
      width: 150,
    },
    {
      title: t('付款主体'),
      render: 'payer',
      width: 150,
    },
    {
      title: t('业务大类'),
      render: 'businessCategoryDescr',
      width: 150,
    },
    {
      title: t('业务子类'),
      render: 'subBusinessCategoryDescr',
      width: 150,
    },
    {
      title: t('发薪人数'),
      render: 'payUserNum',
      width: 120,
    },
    {
      title: t('业务发生日期'),
      render: 'transactionDate',
      width: 150,
    },
    {
      title: t('应付金额'),
      render: 'payableAmount',
      width: 120,
    },
    {
      title: t('税率'),
      render: 'taxRate',
      width: 100,
    },
    {
      title: t('应付税额'),
      render: 'payableTaxAmount',
      width: 120,
    },
    {
      title: t('应付不含税额'),
      render: 'payableAmountExcludingTax',
      width: 150,
    },
    {
      title: t('费用返还金额'),
      render: 'refundAmount',
      width: 150,
    },
    {
      title: t('费用返还回执'),
      render: (record) => (record?.returnList || []).map((returnListItem) => (
        <div
          className={styles.link}
          onClick={() => {
            store.downloadInvoiceFile({ id: returnListItem?.id });
          }}
        >
          {returnListItem.attachmentName}
        </div>
      )),
      width: 220,
    },
    {
      title: t('结算周期'),
      render: 'accountPeriodDesc',
      width: 150,
    },
    {
      title: t('结算信息'),
      render: (record) => {
        if (record?.costId) {
          return (
            <span
              className={styles.link}
              onClick={() => store.getCostInfo({
                id: record?.costId,
              })}
            >
              {record?.costCode}
            </span>
          );
        }
        return record?.costCode;
      },
      width: 150,
    },
    {
      title: t('发票信息'),
      render: (record) => (record?.invoiceList || []).map((invoiceListItem) => (
        <div
          className={styles.link}
          onClick={() => {
            store.changeData({
              invoiceInfo: invoiceListItem,
              invoiceModalVisible: true,
            });
          }}
        >
          {invoiceListItem.invoiceNo}
        </div>
      )),
      width: 220,
    },
    {
      title: t('合同信息'),
      render: (record) => {
        if (record?.contractId) {
          return (
            <span
              className={styles.link}
              onClick={() => store.getContractInfo({
                id: record?.contractId,
              })}
            >
              {record?.contractCode}
              {t('合同')}
            </span>
          );
        }
        return record?.contractCode;
      },
      width: 220,
    },
    {
      title: t('核对确认回执'),
      render: (record) => (record?.confirmList || []).map((confirmListItem) => (
        <div
          className={styles.link}
          onClick={() => {
            store.downloadInvoiceFile({ id: confirmListItem?.id });
          }}
        >
          {confirmListItem.attachmentName}
        </div>
      )),
      width: 220,
    },
    {
      title: t('薪资明细链接'),
      render: (row) => (
        <Button
          text type="primary"
          style={{
            wordWrap: 'break-word',
            whiteSpace: 'pre-wrap',
          }} onClick={() => store.queryValidFile({ rowUrl: row?.incomeInfoFile, isInnerUrl: true })}
        >
          {row.incomeInfoFile}
        </Button>
      ),
      width: 150,
    },
    {
      title: t('明细驳回原因'),
      render: (r) => <ShowMore maxLength={10} text={r.rejectReason} />,
      width: 150,
    },
    {
      title: t('个税状态'),
      render: 'taxStatusName',
      width: 150,
    },
    {
      title: t('付薪银行凭证'),
      render: (r) => {
        if (Array.isArray(r?.proofList) && r.proofList.length > 0) {
          return (
            <Button
              type="link"
              className="customLinkStyle"
              onClick={() => {
                store.changeData({
                  bankPaymentVoucherModalVisible: true,
                  proofList: ((r?.proofList || []) as { attachmentName: string; attachmentUrl: string; }[]).map((pi) => ({
                    ...pi,
                    name: pi.attachmentName,
                  })),
                });
              }}
            >
              {t('付薪银行凭证')}
            </Button>
          );
        }
      },
      width: 150,
    },
    {
      title: t('数据版本号'),
      render: 'dataVersion',
      width: 150,
    },
    {
      title: t('更新时间'),
      render: 'updateTime',
      width: 150,
    },
    {
      title: t('审批日志'),
      render: (record) => (
        <div>
          <Button
            type="primary"
            text
            onClick={() => store.getApproveLog({
              approvalCode: record.approvalNo,
            })}
          >
            {t('审批日志')}
          </Button>
        </div>
      ),
      width: 150,
    },
    {
      title: t('操作日志'),
      render: (record) => (
        <div>
          <Button
            type="primary"
            text
            onClick={() => store.changeData({
              recordVisible: true,
              recordId: record.id,
            })}
          >
            {t('操作日志')}
          </Button>
        </div>
      ),
      width: 150,
    },
  ];
  const handelRowSelect = (val: IDataItem[]) => {
    store.changeData({
      selectedRows: val,
    });
  };
  const isEmpty = (value) => {
    let showValue = value;
    if ([null, undefined, ''].includes(value)) {
      showValue = '--';
    }
    return showValue;
  };
  const getTotalInfo = () => {
    let totalInfo: React.JSX.Element = <div />;
    if (list && list.length > 0) {
      totalInfo = (
        <div className={style.paginationLeft} style={{ zIndex: 1 }}>
          <span>{t('应付金额：{}', isEmpty(customObj?.payableCostTotal))}</span>
          <span style={{ paddingLeft: '15px' }}>{t('应付税额：{}', isEmpty(customObj?.payableCostRateTotal))}</span>
          <span style={{ paddingLeft: '15px' }}>{t('应付不含税额：{}', isEmpty(customObj?.payableCostNoTaxTotal))}</span>
          <span style={{ paddingLeft: '15px' }}>{t('费用返还金额：{}', isEmpty(customObj?.refundAmountTotal))}</span>
        </div>
      );
    }
    return totalInfo;
  };

  return (
    <section className={style.tableSection}>
      <SearchAreaTable>
        <Table<IDataItem, IDataItem[]>
          style={{ height: '100%' }}
          rowClassName={() => style.borderInner}
          bordered
          fixed="both"
          loading={!loading}
          data={list}
          columns={columns}
          keygen="id"
          value={selectedRows}
          onRowSelect={handelRowSelect}
          empty={t('暂无数据')}
          size="small"
          width={columns.reduce((pre, current) => pre + (current.width || 100), 50)}
          pagination={{
            align: 'right',
            current: pageInfo.pageNum,
            pageSize: pageInfo.pageSize,
            className: style.pagination,
            layout: [() => getTotalInfo(), ({ total }) => `${t('共')} ${total} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
            onChange: (page, size) => {
              store.handlePaginationChange({
                pageNum: page,
                pageSize: size,
              });
            },
            pageSizeList: pageInfo.pageSizeList,
            total: pageInfo.count,
          }}
        />
        {/* 操作记录 */}
        <OperationModal
          visible={recordVisible}
          param={{
            operateId: recordId,
            operateCode: 'SALARY_PAY_ORDER_MONTH', // todo
          }}
          onCancel={() => store.changeData({ recordVisible: false })}
        />
      </SearchAreaTable>
      <InvoiceInfo {...props} />
      <CostInfo {...props} />
      <ContractInfo {...props} />
    </section>
  );
}

export default List;
