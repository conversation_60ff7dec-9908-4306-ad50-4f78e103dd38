import React from 'react';
import {
  Button, Form, Input, Modal, Select,
} from 'shineout';
import { t } from '@shein-bbl/react';
import store from '../reducers';
import styles from '../style.less';
import { IPageProps } from '../types';

function SalaryModal(props: IPageProps) {
  const {
    loading,
    isEditMode,
    salaryModalVisible,
    salaryModalObj,
    reportMethodOptions,
  } = props;
  return (
    <Modal
      maskCloseAble={null}
      key={salaryModalVisible}
      visible={salaryModalVisible}
      width={800}
      title={<div style={{ textAlign: 'center', fontWeight: 'bold', fontSize: '18px' }}>{t('劳务费薪资报账')}</div>}
      onClose={() => {
        store.changeData({
          salaryModalVisible: false,
        });
      }}
      footer={isEditMode ? (
        <div>
          <Button onClick={() => store.changeData({
            salaryModalVisible: false,
          })}
          >
            {t('取消')}
          </Button>
          <Modal.Submit disabled={!loading}>{t('确认')}</Modal.Submit>
        </div>
      ) : undefined}
    >
      <Form
        labelWidth={110}
        labelAlign="right"
        style={{ maxWidth: 800 }}
        onSubmit={() => {
          store.submitSalaryReport();
        }}
        onChange={(value) => {
          store.changeData({
            salaryModalObj: value,
          });
        }}
        value={salaryModalObj}
        inline
        formRef={(f) => store.changeData({ formRef: f })}
      >
        {isEditMode && (
        <div>
          <b>
            {t('确认对当前：{}期间报账单信息，选择报账方式，提交报账', salaryModalObj.accountPeriod)}
          </b>
        </div>
        )}
        <div>
          <b>
            {t('审批单号')}
            ：
            {salaryModalObj.approvalNo}
          </b>
        </div>
        {!isEditMode && (
        <div>
          <b style={{ color: 'red' }}>
            {t('报账方式')}
            ：
            {salaryModalObj.reportMethodName}
          </b>
        </div>
        )}
        <div className={styles.formItemWrap}>
          <div>
            <Form.Item label={t('账期')}>
              <Input
                name="accountPeriod"
                disabled
                width={200}
              />
            </Form.Item>
          </div>
          <div>
            {isEditMode ? (
              <Form.Item required label={t('报账方式')} style={{ color: 'red' }}>
                <Select
                  autoAdapt
                  name="reportMethod"
                  data={reportMethodOptions}
                  keygen="dictCode"
                  format="dictCode"
                  renderItem="dictNameZh"
                  onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
                  style={{ width: '200px' }}
                  placeholder={t('请选择报账方式')}
                  clearable={false}
                  required
                />
              </Form.Item>
            ) : (
              <Form.Item label={t('报账时间')}>
                <Input
                  name="reportTime"
                  disabled
                  width={200}
                />
              </Form.Item>
            )}
          </div>
        </div>
        <div className={styles.formItemWrap}>
          <div>
            <Form.Item label={t('发薪人数')}>
              <Input
                name="payUserNum"
                disabled
                width={200}
              />
            </Form.Item>
          </div>
          <div>
            <Form.Item label={t('报账金额')} style={{ color: 'red' }}>
              <Input
                name="totalPay"
                disabled
                width={200}
              />
            </Form.Item>
          </div>
        </div>
        <div className={styles.formItemWrap}>
          <div>
            <Form.Item label={t('劳务公司数')}>
              <Input
                name="companyNum"
                disabled
                width={200}
              />
            </Form.Item>
          </div>
          <div>
            <Form.Item label={t('报账单数')}>
              <Input
                name="payOrderNum"
                disabled
                width={200}
              />
            </Form.Item>
          </div>
        </div>
        <div className={styles.formItemWrap}>
          <div>
            <Form.Item label={t('结算产能')}>
              <Input
                name="production"
                disabled
                width={200}
              />
            </Form.Item>
          </div>
          <div>
            <Form.Item label={t('上月结算产能')}>
              <Input
                name="lastProduction"
                disabled
                width={200}
              />
            </Form.Item>
          </div>
        </div>
        <div className={styles.formItemWrap}>
          <div>
            <Form.Item label={t('结算工时')}>
              <Input
                name="supportHour"
                disabled
                width={200}
              />
            </Form.Item>
          </div>
          <div>
            <Form.Item label={t('上月结算工时')}>
              <Input
                name="lastSupportHour"
                disabled
                width={200}
              />
            </Form.Item>
          </div>
        </div>
      </Form>
    </Modal>
  );
}

export default SalaryModal;
