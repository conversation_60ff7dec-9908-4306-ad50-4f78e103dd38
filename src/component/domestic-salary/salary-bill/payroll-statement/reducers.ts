import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { Message, Modal } from 'shineout';
import { getSize } from '@src/middlewares/pagesize';
import { clearEmpty } from '@src/lib/deal-func';
import moment from 'moment/moment';
import { queryValidFileAPI } from '@src/server/common/safety';
import { dictSelect } from '@src/server/basic/dictionary';
import { handleListMsg } from '@src/lib/dealFunc';
import {
  getListAPI,
  exportListAPI,
  queryVerifyListAPI,
  recalculateVerifyDataAPI,
  getApproveInfoAPI,
  submitSalaryReportAPI,
  getApproveLogAPI,
  downloadInvoiceFileAPI,
  getCostInfoAPI,
  getContractInfoAPI,
  repushAPI,
  taxCheckAPI,
} from './server';
import { IGetListAPIResponse, ILimitType, IStateType } from './types';

export const defaultLimit: ILimitType = {
  verifyOrder: '', // 对账单号
  payOrder: '', // 报账单号
  ym: moment().subtract(1, 'months').format('YYYYMM'), // 默认是上个月,
  company: '', // 公司
  costCompany: '', // 费用所属公司
  verifyOrderStatus: '', // 对账单状态
  payOrderStatus: '', // 报账单状态
  subBusinessCategory: '', // 业务子类
  approvalNo: '', // 审批单号
  taxStatus: '', // 个税状态
};

const defaultState: IStateType = {
  limit: defaultLimit,
  ready: false,
  loading: 1, // 0 loading, 1 load success, 2 load fail
  list: [],
  selectedRows: [], // 已选择绑定的用户组
  pageInfo: {
    pageNum: 1, // 页码
    count: 0, // 表格总条数
    pageSize: getSize(),
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  formRef: undefined,
  verifyOrderStatusOptions: [], // 对账单状态下拉
  payOrderStatusOptions: [], // 报账单状态下拉
  subBusinessCategoryOptions: [], // 业务子类
  verifyOrders: [], // 根据报账单ids查询返回的对账单号列表
  recalculateVerifyDataModalVisible: false, // 对账单重算弹窗显示
  customObj: {}, // 汇总信息
  salaryModalVisible: false, // 劳务费薪资报账弹窗显示
  currentApprovalNo: '', // 当前选中的审批单号
  isEditMode: false, // 是否为编辑模式
  reportMethodOptions: [], // 报账方式选项
  salaryModalObj: {}, // 劳务费薪资报账信息
  recordVisible: false, // 操作记录弹窗
  recordId: '', // 操作记录id
  approveLogVisible: false, // 审批日志弹框
  approveLog: {
    list: [],
    approvalCode: '',
  }, // 审批日志弹框列表
  invoiceInfo: {}, // 发票信息
  invoiceModalVisible: false, // 发票信息弹框是否显示
  costInfo: {}, // 结算信息
  costInfoModalVisible: false, // 结算信息弹框是否展示
  servicePersonalList: [], // 账户性质下拉
  payTypeList: [], // 支付方式下拉
  contractInfo: {}, // 合同信息
  contractModalVisible: false, // 合同信息弹框是否展示
  personalIncomeTaxInfo: {
    checkStatus: 1,
  }, // 个税审核信息
  personalIncomeTaxModalVisible: false, // 个税审核弹框
  bankPaymentVoucherModalVisible: false, // 银行付款凭证弹框
  proofList: [], // 银行付款凭证附件
  taxStatusOptions: [],
};

export default {
  state: defaultState,
  $init: () => defaultState, // 可选使用，在页面初始化的时候会重置state
  /**
   * 改变state的值
   */
  changeData(state: Partial<IStateType>, data?: Partial<IStateType>) {
    Object.assign(state, data);
  },
  /**
   * 改搜索条件limit属性值
   */
  changeLimitData(state: Partial<ILimitType>, data?: Partial<ILimitType>) {
    // !!! LCD对于action方法有改动state实际为IState，为保证程序正常强制as一下
    const stateData = state as IStateType;
    Object.assign(stateData, {
      limit: {
        ...stateData.limit,
        ...data,
      },
    });
  },
  /**
   * 重置查询条件
   */
  * clearLimitData() {
    const { formRef }: IStateType = yield '';
    yield this.changeLimitData(defaultLimit);
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },
  // 校验
  * handleFormValidate() {
    const { formRef }: IStateType = this.state;
    let validateFlag = false;
    if (formRef) {
      yield formRef.validate().then(() => {
        validateFlag = true;
      }).catch(() => {
        validateFlag = false;
      });
    }
    return validateFlag;
  },
  /**
   * 页签改变
   * @param {*} arg
   */
  * handlePaginationChange(arg = {}) {
    const validateFlag: boolean = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    const { pageInfo } = yield '';
    yield this.changeData({
      pageInfo: {
        ...pageInfo,
        ...arg,
      },
    });
    yield this.search();
  },
  /**
   * 初始化数据
   */
  * init() {
    const [selectData] = yield Promise.all([
      dictSelect({
        catCode: [
          'SALARY_VERIFY_ORDER_STATUS',
          'SALARY_PAY_ORDER_STATUS',
          'SALARY_BUSINESS_SUB_TYPE',
          'WBMS_BUSINESS_APPROVAL_REPORT_METHOD',
          'WBMS_STANDARD_PROVIDER_COST_CODE_SERVICE_PERSONAL',
          'WBMS_STANDARD_PROVIDER_COST_CODE_EXTEND_PAY_TYPE',
          'SALARY_TAX_CHECK_STATUS',
        ],
      }),
    ]);
    if (selectData.code === '0') {
      yield this.changeData({
        verifyOrderStatusOptions: selectData.info.data.find((x) => x.catCode === 'SALARY_VERIFY_ORDER_STATUS')?.dictListRsps || [],
        payOrderStatusOptions: selectData.info.data.find((x) => x.catCode === 'SALARY_PAY_ORDER_STATUS')?.dictListRsps || [],
        subBusinessCategoryOptions: selectData.info.data.find((x) => x.catCode === 'SALARY_BUSINESS_SUB_TYPE')?.dictListRsps || [], // 业务子类
        reportMethodOptions: selectData.info.data.find((x) => x.catCode === 'WBMS_BUSINESS_APPROVAL_REPORT_METHOD')?.dictListRsps || [], // 报账方式
        servicePersonalList: selectData.info.data.find((x) => x.catCode === 'WBMS_STANDARD_PROVIDER_COST_CODE_SERVICE_PERSONAL')?.dictListRsps || [],
        payTypeList: selectData.info.data.find((x) => x.catCode === 'WBMS_STANDARD_PROVIDER_COST_CODE_EXTEND_PAY_TYPE')?.dictListRsps || [],
        taxStatusOptions: selectData.info.data.find((x) => x.catCode === 'SALARY_TAX_CHECK_STATUS')?.dictListRsps || [],
      });
    } else {
      handleListMsg([selectData], false);
    }
  },
  /**
   * 查询表格数据
   */
  * search() {
    const { limit, pageInfo } = yield '';
    const param = {
      ...limit,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
    };
    markStatus('loading');
    const res: IGetListAPIResponse = yield getListAPI(clearEmpty(param, [0]));
    if (res.code === '0') {
      yield this.changeData({
        list: res.info?.data || [],
        selectedRows: [],
        pageInfo: {
          ...this.state.pageInfo,
          count: res.info?.meta?.count ?? 0,
        },
        customObj: res?.info?.meta?.customObj || {},
      });
    } else {
      Modal.error({
        title: res.msg,
      });
      yield this.changeData({
        list: [],
        pageInfo: {
          ...this.state.pageInfo,
          count: 0,
        },
        customObj: {},
      });
    }
  },
  // 导出
  * exportData() {
    // 校验
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    // 校验通过后执行
    const { limit } = yield '';
    // 一般导出接口不用传页数页码，因为是要导出全部数据：部分旧页面有传就与旧页面一致；新页面跟后端统一按规范不用传页数页码
    // 实际开发时，根据情况决定是否加仓库id，页数页码等字段
    const param = {
      ...limit,
      pageNum: 1,
      pageSize: 100000,
    };
    markStatus('loading');
    const { code, msg } = yield exportListAPI(param);
    if (code === '0') {
      window.open('#/statistical/download');
    } else {
      Modal.error({ title: msg });
    }
  },
  // 根据报账单ids获取对账单号
  * queryVerifyList() {
    const { selectedRows } = yield '';
    markStatus('loading');
    const res = yield queryVerifyListAPI({
      ids: selectedRows.map((si) => (si.id)),
    });
    if (res.code === '0') {
      yield this.changeData({
        recalculateVerifyDataModalVisible: true,
        verifyOrders: (res.info || []).map((i) => (i?.verifyOrder)),
      });
    } else {
      Modal.error({ content: res.msg });
    }
  },
  // 对账单重算
  * recalculateVerifyData() {
    const { verifyOrders } = yield '';
    markStatus('loading');
    const res = yield recalculateVerifyDataAPI({
      verifyOrders,
    });
    if (res.code === '0') {
      yield this.changeData({
        recalculateVerifyDataModalVisible: false,
      });
      Message.success(t('操作成功'));
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ content: res.msg });
    }
  },
  // 获取付薪报账信息
  * getApproveInfo() {
    const { currentApprovalNo } = yield '';
    markStatus('loading');
    const res = yield getApproveInfoAPI({
      approvalCode: currentApprovalNo,
    });
    if (res.code === '0') {
      yield this.changeData({
        salaryModalObj: res.info || {},
      });
    } else {
      Modal.error({ content: res.msg });
    }
  },
  /**
 * 提交付薪报账
 * @param reportMethodName 报账方式
 * @param info 报账信息对象
 */
  * submitSalaryReport() {
    const { currentApprovalNo, salaryModalObj } = yield '';
    markStatus('loading');
    const res = yield submitSalaryReportAPI({
      approvalCode: currentApprovalNo,
      ...salaryModalObj,
    });
    if (res.code === '0') {
      yield this.changeData({
        salaryModalVisible: false,
      });
      Message.success(t('操作成功'));
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ content: res.msg });
    }
  },
  * getApproveLog(params) {
    markStatus('loading');
    const res = yield getApproveLogAPI(params);
    if (res.code === '0') {
      yield this.changeData({
        approveLogVisible: true,
        approveLog: {
          approvalCode: params?.approvalCode,
          list: res?.info?.data || [],
        },
      });
    } else {
      Modal.error({ content: res.msg });
    }
  },
  * downloadInvoiceFile(params) {
    markStatus('loading');
    const res = yield downloadInvoiceFileAPI(params);
    if (res.code === '0') {
      const fileUrl = res?.info?.attachmentUrl;
      if (fileUrl) {
        window.open(fileUrl, '_blank');
      }
    } else {
      Modal.error({ content: res.msg });
    }
  },
  * getCostInfo(params) {
    markStatus('loading');
    const res = yield getCostInfoAPI({
      ...params,
      pageNum: 1,
      pageSize: 10000,
    });
    if (res.code === '0') {
      yield this.changeData({
        costInfo: res?.info || {}, // 结算信息
        costInfoModalVisible: true, // 结算信息弹框是否展示
      });
    } else {
      Modal.error({ content: res.msg });
    }
  },
  * getContractInfo(params) {
    markStatus('loading');
    const res = yield getContractInfoAPI({
      ...params,
      pageNum: 1,
      pageSize: 10000,
    });
    if (res.code === '0') {
      yield this.changeData({
        contractInfo: res?.info || {}, // 结算信息
        contractModalVisible: true, // 结算信息弹框是否展示
      });
    } else {
      Modal.error({ content: res.msg });
    }
  },
  * rePush() {
    const { selectedRows } = yield '';
    markStatus('loading');
    const res = yield repushAPI({
      idList: selectedRows.map((si) => (si.id)),
    });
    if (res.code === '0') {
      Message.success(t('操作成功'));
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ content: res.msg });
    }
  },
  // 个税审核信息获取
  * personalIncomeTaxAudit() {
    const { selectedRows } = yield '';
    markStatus('loading');
    const res = yield queryVerifyListAPI({
      ids: selectedRows.map((si) => (si.id)),
    });
    if (res.code === '0') {
      const personalIncomeTaxInfo = (res?.info || [])[0] || {};
      yield this.changeData({
        personalIncomeTaxModalVisible: true,
        personalIncomeTaxInfo: {
          ...personalIncomeTaxInfo,
          checkStatus: 1,
        },
      });
      // if (personalIncomeTaxInfo?.fileUrl) {
      //   const getValidUrlRes = yield queryValidFileAPI({ url: personalIncomeTaxInfo?.fileUrl });
      //   if (getValidUrlRes?.code === '0') {
      //     yield this.changeData({
      //       personalIncomeTaxInfo: {
      //         ...personalIncomeTaxInfo,
      //         fileList: [{
      //           fileUrl: personalIncomeTaxInfo?.fileUrl,
      //           fileCompleteUrl: getValidUrlRes?.info?.validUrl,
      //           fileName: personalIncomeTaxInfo?.fileName || '',
      //           name: personalIncomeTaxInfo?.fileName || personalIncomeTaxInfo?.fileUrl,
      //         }],
      //       },
      //     });
      //   } else {
      //     Modal.error({ title: getValidUrlRes?.msg });
      //   }
      // }
    } else {
      Modal.error({ content: res.msg });
    }
  },
  // 个税审核提交
  * taxCheck() {
    const { personalIncomeTaxInfo } = yield '';
    markStatus('loading');
    const res = yield taxCheckAPI({
      ...personalIncomeTaxInfo,
      attachmentUrl: (personalIncomeTaxInfo?.fileList || [])[0]?.fileUrl || '',
      attachmentName: (personalIncomeTaxInfo?.fileList || [])[0]?.fileName || '',
    });
    if (res.code === '0') {
      yield this.changeData({
        personalIncomeTaxModalVisible: false,
      });
      Message.success(t('操作成功'));
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ content: res.msg });
    }
  },
  /**
   * 查询鉴权文件
   * @param {*} data
   */
  * queryValidFile({ rowUrl, isInnerUrl }) {
    const downloadFile = (url) => {
      const a = document.createElement('a');
      a.href = url;
      a.download = '';
      document.body.appendChild(a); // 将 <a> 元素添加到 DOM 中
      a.click();
      document.body.removeChild(a); // 从 DOM 中移除 <a> 元素
    };

    // 不用鉴权
    if (!isInnerUrl) {
      downloadFile(rowUrl);
      return;
    }
    console.log('rowUrl', rowUrl);
    console.log('isInnerUrl', isInnerUrl);
    // 鉴权
    try {
      const param = { url: rowUrl };
      const res = yield queryValidFileAPI(param);
      const { code, info, msg } = res;

      if (code === '0') {
        downloadFile(info.validUrl);
      } else {
        Modal.error({ title: msg });
      }
    } catch (error) {
      console.log(error);
      Modal.error({ title: t('请求失败，请稍后重试') });
    }
  },
};
