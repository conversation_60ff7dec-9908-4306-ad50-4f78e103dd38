import { sendPostRequest } from '@src/server/common/public';

// 查询列表
export const getListAPI = (param) => sendPostRequest({
  url: '/salary_pay_order_month/query_list',
  param,
}, process.env.WBMS_FRONT);

// 导出
export const exportListAPI = (param) => sendPostRequest({
  url: '/salary_pay_order_month/export',
  param,
}, process.env.WBMS_FRONT);

// 计薪-报账单管理-报账单查询
export const queryVerifyListAPI = (param) => sendPostRequest({
  url: '/salary_pay_order_month/query_verify_List',
  param,
}, process.env.WBMS_FRONT);

// 计薪-报账单管理-报账单重算
export const recalculateVerifyDataAPI = (param) => sendPostRequest({
  url: '/salary_pay_order_month/recalculate_verify',
  param,
}, process.env.WBMS_FRONT);

// 获取劳务费薪资报账信息
export const getApproveInfoAPI = (param) => sendPostRequest({
  url: '/salary_pay_order_month/approve_info',
  param,
}, process.env.WBMS_FRONT);

// 获取劳务费薪资报账信息
export const submitSalaryReportAPI = (param) => sendPostRequest({
  url: '/salary_pay_order_month/submit_approve',
  param,
}, process.env.WBMS_FRONT);

// 获取审批日志
export const getApproveLogAPI = (param) => sendPostRequest({
  url: '/salary_pay_order_month/approve_log',
  param,
}, process.env.WBMS_FRONT);

// 发票附件下载
export const downloadInvoiceFileAPI = (param) => sendPostRequest({
  url: '/salary_pay_order_month/invoice_url',
  param,
}, process.env.WBMS_FRONT);

// 获取结算信息
export const getCostInfoAPI = (param) => sendPostRequest({
  url: '/supplier_standard/sensitize/cost_code_detail',
  param,
}, process.env.WBMS_FRONT);

// 获取合同信息
export const getContractInfoAPI = (param) => sendPostRequest({
  url: '/std_common/contract_standard/query_detail',
  param,
}, process.env.WBMS_FRONT);

// 重推报账单中台
export const repushAPI = (param) => sendPostRequest({
  url: '/salary_pay_order_month/recalculate_std_pay_order',
  param,
}, process.env.WBMS_FRONT);

// 供应商个税确认
export const taxCheckAPI = (param) => sendPostRequest({
  url: '/salary_pay_order_month/tax_check',
  param,
}, process.env.WBMS_FRONT);
