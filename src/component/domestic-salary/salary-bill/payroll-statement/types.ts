import {
  IFetchResponse, IPageInfo, IViewBaseProps,
} from '@src/typing/base';
import { FormRef } from 'shineout/lib/Form/Props';

export interface IGetListAPIRequest {
  /** 开始创建时间 */
  beginCreateTime?:string;
  /** 状态 */
  enabled?:string;
  /** 结束创建时间 */
  endCreateTime?:string;
  /** 用户组编码 */
  groupCode?:string;
  /** 用户组名称 */
  groupName?:string;
  /** 用户组类型 */
  groupTypeList?:unknown;
  /** 当前页码 */
  pageNum?:number;
  /** 页宽度 */
  pageSize?:number;
  /** 子仓 */
  subWarehouseIdList?:number[];
  /** 作业人员 */
  userName?:string;
  /** 仓库id */
  warehouseIdList?:number[];
}
export interface IInvoiceInfoItem {
  areaBelong?:number;
  /** 所属地区 */
  areaBelongName?:string;
  /** 附件名称 */
  attachmentName?:string;
  /** ID */
  id?:number;
  /** 含税金额 */
  invoiceAmount?:number;
  /** 不含税额 */
  invoiceAmountNoTax?:number;
  /** 发票代码 */
  invoiceCode?:string;
  /** 发票币种 */
  invoiceCurrency?:string;
  /** 发票日期 */
  invoiceDate?:string;
  /** 发票号码 */
  invoiceNo?:string;
  invoiceType?:string;
  /** 发票类型 */
  invoiceTypeName?:string;
  mediaType?:string;
  /** 类型 */
  mediaTypeName?:string;
  /** 数据来源 */
  source?:string;
  /** 发票税额 */
  taxAmount?:number;
  taxType?:string;
  /** 税种名称 */
  taxTypeName?:string;
}
export interface IDataItem {
  /** 调整金额 */
  adjustCost?:number;
  /** 业务大类 */
  businessCategoryDescr?:string;
  /** 薪资确认回执 */
  checkFile?:string;
  /** 公司 L3 */
  company?:string;
  /** 所属公司名称 L3 */
  companyDescr?:string;
  /** 费用所属公司编码 L3 */
  costCompany?:string;
  /** 费用所属公司 */
  costCompanyDescr?:string;
  /** ID */
  id?:number;
  /** 往期结算费用 */
  lastSettleCost?:number;
  /** 报账单号 */
  payOrder?:string;
  /** 报账单状态 1、已报账；0-待报账; */
  payOrderStatusDescr?:string;
  /** 应发合计 */
  payTotalCost?:number;
  /** 发薪人数 */
  payUserNum?:number;
  /** 应付金额 */
  payableAmount?:number;
  /** 应付不含税额 */
  payableAmountExcludingTax?:number;
  /** 应付税额 */
  payableTaxAmount?:number;
  /** 付款主体 */
  payer?:string;
  /** 费用返还回执 */
  paymentReceipt?:string;
  /** 发薪人数 */
  payroll?:number;
  /** 期间 */
  period?:string;
  /** 费用返还金额 */
  refundAmount?:number;
  /** 服务费 */
  serviceCost?:number;
  /** 结算费用 */
  settleCost?:number;
  /** 业务子类编码 */
  subBusinessCategory?:number;
  /** 业务子类 */
  subBusinessCategoryDescr?:string;
  /** 税点金额 */
  taxCost?:number;
  /** 税率 */
  taxRate?:number;
  /** 业务发生日期 */
  transactionDate?:string;
  /** 更新时间 */
  updateTime?:string;
  /** 对账单号 */
  verifyOrder?:string;
  /** 对账单状态编码 1、已核对；0-待核对；2-已驳回; */
  verifyOrderStatus?:number;
  /** 对账单状态 1、已核对；0-待核对；2-已驳回; */
  verifyOrderStatusDescr?:string;
  /** 版本号 */
  version?:number;
  incomeInfoFile?: string;
  dataVersion?: string;
  accountPeriodDesc?: string;
  approvalNo?: string;
  reimbursementMethod?: string;
  invoiceList: IInvoiceInfoItem[];
  [key: string]: number | string | boolean | undefined;
}

type IData = IDataItem[];

interface IMeta {
  /** 数据记录数 */
  count?:number;
  /** 用户自定义扩展 */
  customObj?: {
    payableCostTotal: number;
    payableCostRateTotal: number;
    payableCostNoTaxTotal: number;
    refundAmountTotal?: number;
  };
}

interface IResponseBodyInfo {
  /** 结果 */
  data:IData;
  /** 元数据 */
  meta:IMeta;
}

export type IGetListAPIResponse = IFetchResponse<IResponseBodyInfo>;

export interface ILimitType {
  /** 公司编码 */
  company?:string;
  /** 费用公司编码 */
  costCompany?:string;
  /** 当前页码 */
  pageNum?:number;
  /** 页宽度 */
  pageSize?:number;
  /** 报账单号 */
  payOrder?:string;
  /** 报账单状态 */
  payOrderStatus?:string;
  /** 对账单号 */
  verifyOrder?:string;
  /** 对账单状态 */
  verifyOrderStatus?:string;
  /** 期间 */
  ym?:string;
  /** 业务子类 */
  subBusinessCategory?:string;
  /** 审批单号 */
  approvalNo?:string;
  taxStatus?: string;
}

export interface IDictionaryItem {
  dictCode: number;
  dictNameZh: string;
}
export interface IApproveLogItem {
  /** 审核备注 */
  approvalRemark?:string;
  /** 审核时间 */
  approvalTime?:string;
  /** 审批人 */
  assignee?:string;
  /** 审核平台 */
  platform?:string;
  /** 审批节点 */
  taskName?:string;
}
interface IAttachmentListItem {
  link?:string;
  name?:string;
}
export interface IPersonalIncomeTaxInfo {
  /** 对账单审核状态 */
  checkStatus?:number;
  /** 供应商 */
  companyDesc?:string;
  /** 文件链接 */
  fileUrl?:string;
  /** 对账单id */
  id?:number;
  /** 对账单号列表 */
  verifyOrder?:string;
  fileName?: string;
  fileList?: Array<{
    fileUrl: string;
    fileName: string;
  }>
}
export interface IStateType {
  loading: number; // 0 loading, 1 load success, 2 load fail
  limit: ILimitType;
  ready: boolean;
  list: IDataItem[];
  selectedRows: IDataItem[];
  pageInfo: IPageInfo;
  formRef?: FormRef<ILimitType>
  recalculateVerifyDataModalVisible: boolean;
  verifyOrderStatusOptions: IDictionaryItem[];
  payOrderStatusOptions: IDictionaryItem[]
  subBusinessCategoryOptions: IDictionaryItem[]
  verifyOrders: string[];
  customObj: {
    payableCostTotal?: number;
    payableCostRateTotal?: number;
    payableCostNoTaxTotal?: number;
    refundAmountTotal?: number;
  }
  salaryModalVisible: boolean; // 劳务费薪资报账弹窗显示
  currentApprovalNo: string; // 当前选中的审批单号
  isEditMode: boolean; // 是否为编辑模式
  reportMethodOptions: IDictionaryItem[]; // 报账方式选项
  salaryModalObj: {
    /** 账期 */
    accountPeriod?:string;
    /** 审批单号 */
    approvalNo?:string;
    /** 劳务公司数 */
    companyNum?:number;
    /** 上月结算产能 */
    lastProduction?:number;
    /** 上月结算工时 */
    lastSupportHour?:number;
    /** 报账单数 */
    payOrderNum?:number;
    /** 发薪人数 */
    payUserNum?:number;
    /** 结算产能 */
    production?:number;
    /** 报账方式 */
    reportMethodName?:string;
    /** 结算工时 */
    supportHour?:number;
    /** 报账金额 */
    totalPay?:number;
  },
  recordVisible: boolean,
  recordId?: string | number,
  approveLogVisible: boolean;
  approveLog: {
    list: IApproveLogItem[],
    approvalCode: string,
  },
  invoiceModalVisible: boolean;
  invoiceInfo: IInvoiceInfoItem;
  contractModalVisible: boolean;
  costInfoModalVisible: boolean;
  costInfo: {
    /** 收款账户地区 */
    areaBelong?:number;
    /** 收款银行开户行 */
    bankName?:string;
    /** 收款银行编码类型 */
    bankType?:number;
    /** 开户行城市代码 */
    cityCode?:string;
    /** 开户行城市 */
    cityName?:string;
    /** 账户开户名 */
    collectionBankAccountName?:string;
    /** 收款银行编码 */
    collectionBankCode?:string;
    /** 收款行国家地区 */
    countryCode?:string;
    /** 银行账户/IBAN */
    fareBankAccount?:string;
    /** 收款账户类型/收款银行类型 */
    fareBankAccountType?:number;
    /** 银行类别 */
    fareBankType?:string;
    /** 收款银行开户行代码 */
    openBankCode?:string;
    /** 收款人手机号 */
    payeeMobile?:string;
    /** 收款人身份证号 */
    payeeNumber?:string;
    /** 开户行省份代码 */
    provinceCode?:string;
    /** 开户行省份 */
    provinceName?:string;
    /** 收款账户性质 */
    servicePersonal?:number;
    /** 结算编码 */
    serviceProviderCostCode?:string;
    /** 纳税人识别号 */
    taxpayerIdentificationNumber?:string;
    /** 银行行号(联行号) */
    unionPayNumber?:string;
  },
  contractInfo: {
    /** 附件 */
    attachmentList?:IAttachmentListItem[];
    /** 合同结束日期 */
    contractEffectEndTime?:string;
    /** 合同生效日期 */
    contractEffectStartTime?:string;
    /** 合同编码 */
    contractNo?:string;
    /** 是否框架合同 */
    isFrameworkContract?:number;
    /** 主合同签约公司 */
    mainContractCompany?:string;
    /** 主合同币种 */
    mainContractCurrency?:string;
    isFrameworkContractName?: string;
    contractStatusName?: string;
  };
  servicePersonalList: IDictionaryItem[];
  payTypeList: IDictionaryItem[];
  taxStatusOptions: IDictionaryItem[]
  personalIncomeTaxModalVisible: boolean;
  personalIncomeTaxInfo: IPersonalIncomeTaxInfo
  bankPaymentVoucherModalVisible: boolean;
  proofList: Array<{
    /** 附件名称 */
    attachmentName:string;
    /** 附件链接 */
    attachmentUrl:string;
  }> | undefined
}

export type IPageProps = IViewBaseProps<IStateType>;
