import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Button, Modal } from 'shineout';
import { Link } from 'react-router-dom';
import FileDelayUpload from '@public-component/upload/fileDelayUpload';
import globalStyles from '@src/component/style.less';
import store from '../reducers';

function Handle(props) {
  const {
    loading,
    importModalVisible,
    file,
  } = props;

  return (
    <section className={[globalStyles.handle, 'handleSection'].join(' ')}>
      <Button
        type="primary"
        loading={loading === 0}
        onClick={() => store.exportData()}
      >
        {t('导出')}
      </Button>
      <Link to="/domestic-salary/balance-and-rules/performance-config" style={{ marginLeft: '10px' }}>
        <Button
          type="warning"
          loading={loading === 0}
          style={{ marginBottom: 8, marginRight: 16 }}
        >
          {t('返回')}
        </Button>
      </Link>
      <Button
        type="primary"
        loading={loading === 0}
        onClick={() => {
          store.changeData({
            importModalVisible: true,
            file: '',
          });
        }}
      >
        {t('导入')}
      </Button>
      <Modal
        maskCloseAble={null}
        destory
        visible={importModalVisible}
        title={t('导入')}
        onClose={() => {
          store.changeData({
            importModalVisible: false,
          });
        }}
        footer={[
          <Button
            key="cancel"
            onClick={() => {
              store.changeData({
                importModalVisible: false,
              });
            }}
          >
            {t('取消')}
          </Button>,
          <Button
            type="primary"
            disabled={!file}
            loading={!loading}
            onClick={() => {
              const formData = new FormData();
              formData.append('file', file);
              store.uploadFile({
                formData,
              });
            }}
          >
            {t('确认上传')}
          </Button>,
        ]}
      >
        <>
          <div style={{ padding: '20px 0', display: 'flex' }}>
            <span style={{ display: 'inline-block', marginRight: 20 }}>
              {t('选择文件')}
              :
            </span>
            <FileDelayUpload
              value={file}
              loading={!loading}
              onChange={(f) => {
                store.changeData({
                  file: f,
                });
              }}
              accept=".xls,.xlsx"
            />
          </div>
          <div
            style={{ marginLeft: 75 }}
          >
            <Button
              type="primary"
              size="default"
              loading={loading === 0}
              onClick={() => store.downloadTemplate()}
            >
              {t('下载导入模板')}
            </Button>
          </div>
        </>
      </Modal>
    </section>
  );
}

Handle.propTypes = {
  loading: PropTypes.number,
  importModalVisible: PropTypes.bool,
  file: PropTypes.shape(),
};

export default Handle;
