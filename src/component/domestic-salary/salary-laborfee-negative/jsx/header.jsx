import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Select, DatePicker } from 'shineout';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import { formatSearchData } from '@public-component/search-queries/utils';
import store, { defaultLimit } from '../reducers';

function Header(props) {
  const {
    limit,
    loading,
    accountingStatusOptions,
    userList,
  } = props;

  return (
    <section>
      <div style={{ color: '#999DA8', paddingBottom: '5px' }}>
        {t('其他-劳务费负账')}
      </div>
      <SearchAreaContainer
        clearUndefined={false}
        searching={!loading}
        value={limit}
        // 保留用户所选项，并补全搜索条件
        onChange={(val) => store.changeLimitData(formatSearchData(defaultLimit, val))}
        onClear={() => store.clearLimitData()}
        onSearch={() => {
          // 点搜索按钮，将页码重置为1
          store.handlePaginationChange({ pageNum: 1 });
        }}
        formRef={(f) => store.changeData({ formRef: f })}
      >
        <Select
          label={t('工号')}
          keygen="uname"
          format="uname"
          data={userList}
          clearable
          onFilter={(v) => { store.handleUnameFilter(v); }}
          renderItem="fullName"
          name="uname"
          placeholder={t('请输入工号')}
          style={{ width: 200 }}
        />
        <DatePicker
          label={t('负账期间')}
          name="ym"
          type="month"
          placeholder={t('请选择月份')}
          style={{ width: 200 }}
          clearable
        />
        <Select
          label={t('记账状态')}
          name="negativeStatus"
          data={accountingStatusOptions}
          keygen="dictCode"
          format="dictCode"
          placeholder={t('请选择')}
          renderItem="dictNameZh"
          onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          clearable
          style={{ width: 200 }}
          renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
        />
      </SearchAreaContainer>
    </section>
  );
}

Header.propTypes = {
  limit: PropTypes.shape({}),
  loading: PropTypes.number,
  accountingStatusOptions: PropTypes.arrayOf(PropTypes.shape({})),
  userList: PropTypes.arrayOf(PropTypes.shape({})),
};

export default Header;
