import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { Modal } from 'shineout';
import fileSaver from 'file-saver';
import { dictSelect } from '@src/server/basic/dictionary';
import { formdataPost } from '@src/server/common/fileFetch';
import { getSize } from '@src/middlewares/pagesize';
import { clearEmpty } from '@src/lib/deal-func';
import { STATISTICAL_IMPORT_CENTER } from '@src/lib/jump-url';
import { queryByFullNameAPI } from '@src/server/wbms/server';
import {
  getListAPI, downloadTemplateAPI, exportListAPI, importURL,
} from './server';

// 搜索区域默认条件
export const defaultLimit = {
  uname: '', // 工号
  ym: '', // 负账期间
  negativeStatus: '', // 记账状态
};

const defaultState = {
  formRef: {}, // 绑定 form 的引用
  loading: 1, // 0加载中 1加载成功 2加载失败
  topSearch: true,
  limit: defaultLimit,
  // 列表区域+分页区域
  pageInfo: {
    pageNum: 1,
    pageSize: getSize(),
    count: 0, // 表格总条数
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  list: [], // 表格数据
  selectedRows: [], // 选中行数据
  // 下拉数据
  accountingStatusOptions: [], // 记账状态下拉选项
  userList: [], // 工号下拉选项
  // 导入相关
  importModalVisible: false, // 导入弹框
  file: null,
};

export default {
  state: defaultState,
  // 改state属性值
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件即state.limit属性值
  changeLimitData(state, data) {
    Object.assign(state.limit, data);
  },
  // 重置查询条件
  * clearLimitData() {
    const { formRef } = yield '';
    yield this.changeLimitData(defaultLimit);
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },
  // 页面初始化
  * init() {
    markStatus('loading');

    // 请求下拉框数据
    const selectData = yield dictSelect({
      catCode: ['WBMS_SALARY_LABORFEE_NEGATIVE_STATUS'],
    });

    if (selectData.code === '0') {
      yield this.changeData({
        accountingStatusOptions: selectData.info.data.find(
          (item) => item.catCode === 'WBMS_SALARY_LABORFEE_NEGATIVE_STATUS',
        )?.dictListRsps || [],
      });
    } else {
      Modal.error({ title: selectData.msg });
    }
  },

  // 搜索
  * search() {
    const { limit, pageInfo } = yield '';
    const param = {
      ...limit,
      ym: limit.ym ? Number(limit.ym.replace('-', '')) : null,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
    };

    markStatus('loading');
    const { code, msg, info } = yield getListAPI(param);
    if (code === '0') {
      yield this.changeData({
        // 有复选框的，要重置对应字段成空数组
        list: info.data,
        pageInfo: {
          ...pageInfo,
          count: info.meta.count,
        },
      });
    } else {
      Modal.error({ title: msg });
    }
  },
  // 表单校验
  * handleFormValidate() {
    const { formRef } = yield '';
    // 兼容跳转进来触发搜索时，dom还没渲染完，执行formRef.validate()报错
    if (!formRef || !formRef.validate) {
      return true;
    }
    let validateFlag = false;
    yield formRef.validate().then(() => {
      validateFlag = true;
    }).catch(() => {
      validateFlag = false;
    });
    return validateFlag;
  },
  // 分页改变
  * handlePaginationChange(data = {}) {
    // 校验
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    // 校验通过后执行
    const { pageInfo } = yield '';
    yield this.changeData({
      pageInfo: {
        ...pageInfo,
        ...data,
      },
    });
    yield this.search();
  },

  // 导出
  * exportData() {
    // 校验搜索条件
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }

    const { limit, pageInfo } = yield '';
    const param = {
      ...clearEmpty(limit),
      ym: limit.ym ? Number(limit.ym.replace('-', '')) : null,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize, // 导出全部数据
    };

    markStatus('loading');
    const { code, msg } = yield exportListAPI(param);

    if (code === '0') {
      yield this.changeData({
        importModalVisible: false,
        file: null,
      });
      window.open('#/statistical/download');
    } else {
      Modal.error({ title: msg });
    }
  },
  // 下载模板
  * downloadTemplate() {
    try {
      markStatus('loading');
      const res = yield downloadTemplateAPI();
      const b = yield res.blob();
      const blob = new Blob([b], { type: 'application/octet-stream' });
      fileSaver.saveAs(blob, `${t('导入模板')}.xlsx`);
    } catch (e) {
      Modal.error({ title: e.reason.message });
    }
  },
  // 导入
  * uploadFile({ formData }) {
    markStatus('loading');
    formData.append('function_node', 246);
    const res = yield formdataPost(importURL, formData);
    if (res.code === '0') {
      window.open(STATISTICAL_IMPORT_CENTER);
      yield this.changeData({
        importModalVisible: false,
        file: '',
      });
    } else {
      Modal.error({ content: res.msg });
    }
  },
  // 输入人名进行搜索
  * handleUnameFilter(v) {
    if (!v) { return; }
    const { code, info, msg } = /^\d/.test(v) // 判断当前输入的是否是数字开头
      ? yield queryByFullNameAPI({
        uname: v, // 工号
        pageNum: 1,
        pageSize: 50,
      }) : yield queryByFullNameAPI({
        fullName: v, // 中文名
        pageNum: 1,
        pageSize: 50,
      });
    if (code === '0') {
      yield this.changeData({ userList: info });
    } else {
      Modal.error({ title: msg });
    }
  },
};
