import React from 'react';
import { t } from '@shein-bbl/react';
import style from '@src/component/style.less';
import {
  Table, Tabs,
} from 'shineout';
import classnames from 'classnames';
import { TableColumnItem } from '@src/typing/base';
import store from '../reducers';
import { IDataItem, IPageProps } from '../types';
import styles from '../style.less';

function List(props: IPageProps) {
  const {
    loading,
    list,
    pageInfo,
    tabKey,
    limit,
  } = props;

  // 劳务公司列表
  const laborCompanyColumns: TableColumnItem<IDataItem>[] = [
    {
      title: t('序号'),
      render: (d, index) => {
        const { pageNum, pageSize } = pageInfo;
        return (pageNum - 1) * pageSize + index + 1;
      },
      width: 120,
    },
    {
      title: t('唯一ID'),
      render: 'id',
      width: 100,
    },
    {
      title: t('期间'),
      render: 'ymString',
      width: 140,
    },
    {
      title: t('公司'),
      render: 'companyName',
      width: 140,
    },
    {
      title: t('公司编码'),
      render: 'company',
      width: 140,
    },
    {
      title: t('发薪人数'),
      render: 'payUserNum',
      width: 160,
    },
    {
      title: t('固定成本'),
      render: 'fixedCost',
      width: 140,
    },
    {
      title: t('浮动成本'),
      render: 'variableCost',
      width: 140,
    },
    {
      title: t('出勤工资'),
      render: 'baseBasic',
      width: 140,
    },
    {
      title: t('超休福利假工资'),
      render: 'overDaySalary',
      width: 140,
    },
    {
      title: t('绩效+加班费'),
      render: 'basePerformanceOt',
      width: 160,
    },
    {
      title: t('福利补贴'),
      render: 'allowance',
      width: 120,
    },
    {
      title: t('服务费'),
      render: 'serviceCost',
      width: 120,
    },
    {
      title: t('应发合计'),
      render: 'totalPay',
      width: 160,
    },
    {
      title: t('产能'),
      render: 'production',
      width: 120,
    },
    {
      title: t('支援工时'),
      render: 'supportHour',
      width: 160,
    },
    {
      title: t('上月发薪人数'),
      render: 'lastPayUserNum',
      width: 160,
    },
    {
      title: t('上月出勤工资'),
      render: 'lastBaseBasic',
      width: 180,
    },
    {
      title: t('上月超休工资'),
      render: 'lastOverDaySalary',
      width: 140,
    },
    {
      title: t('上月绩效加班费'),
      render: 'lastBasePerformanceOt',
      width: 140,
    },
    {
      title: t('上月福利补贴'),
      render: 'lastAllowance',
      width: 140,
    },
    {
      title: t('上月服务费'),
      render: 'lastServiceCost',
      width: 120,
    },
    {
      title: t('上月产能'),
      render: 'lastProduction',
      width: 160,
    },
    {
      title: t('上月支援工时'),
      render: 'lastSupportHour',
      width: 160,
    },
  ];
    // 片区园区列表
  const regionAndParkColumns: TableColumnItem<IDataItem>[] = [
    {
      title: t('序号'),
      render: (d, index) => {
        const { pageNum, pageSize } = pageInfo;
        return (pageNum - 1) * pageSize + index + 1;
      },
      width: 120,
    },
    {
      title: t('唯一ID'),
      render: 'id',
      width: 100,
    },
    {
      title: t('期间'),
      render: 'ymString',
      width: 140,
    },
    {
      title: t('片区'),
      render: 'regionName',
      width: 140,
    },
    {
      title: t('园区'),
      render: 'parkName',
      width: 120,
    },
    {
      title: t('发薪人数'),
      render: 'payUserNum',
      width: 160,
    },
    {
      title: t('固定成本'),
      render: 'fixedCost',
      width: 140,
    },
    {
      title: t('浮动成本'),
      render: 'variableCost',
      width: 140,
    },
    {
      title: t('出勤工资'),
      render: 'baseBasic',
      width: 160,
    },
    {
      title: t('超休福利假工资'),
      render: 'overDaySalary',
      width: 180,
    },
    {
      title: t('绩效+加班费'),
      render: 'basePerformanceOt',
      width: 160,
    },
    {
      title: t('福利补贴'),
      render: 'allowance',
      width: 120,
    },
    {
      title: t('服务费'),
      render: 'serviceCost',
      width: 120,
    },
    {
      title: t('应发合计'),
      render: 'totalPay',
      width: 160,
    },
    {
      title: t('产能'),
      render: 'production',
      width: 120,
    },
    {
      title: t('支援工时'),
      render: 'supportHour',
      width: 160,
    },
    {
      title: t('上月发薪人数'),
      render: 'lastPayUserNum',
      width: 160,
    },
    {
      title: t('上月出勤工资'),
      render: 'lastBaseBasic',
      width: 180,
    },
    {
      title: t('上月超休工资'),
      render: 'lastOverDaySalary',
      width: 180,
    },
    {
      title: t('上月绩效加班费'),
      render: 'lastBasePerformanceOt',
      width: 180,
    },
    {
      title: t('上月福利补贴'),
      render: 'lastAllowance',
      width: 180,
    },
    {
      title: t('上月服务费'),
      render: 'lastServiceCost',
      width: 120,
    },
    {
      title: t('上月产能'),
      render: 'lastProduction',
      width: 160,
    },
    {
      title: t('上月支援工时'),
      render: 'lastSupportHour',
      width: 160,
    },
  ];
  // 环节岗位列表
  const partAndJbColumns: TableColumnItem<IDataItem>[] = [
    {
      title: t('序号'),
      render: (d, index) => {
        const { pageNum, pageSize } = pageInfo;
        return (pageNum - 1) * pageSize + index + 1;
      },
      width: 120,
    },
    {
      title: t('唯一ID'),
      render: 'id',
      width: 100,
    },
    {
      title: t('期间'),
      render: 'ymString',
      width: 140,
    },
    {
      title: t('环节'),
      render: 'partName',
      width: 180,
    },
    {
      title: t('岗位'),
      render: 'posnJbName',
      width: 120,
    },
    {
      title: t('发薪人数'),
      render: 'payUserNum',
      width: 160,
    },
    {
      title: t('固定成本'),
      render: 'fixedCost',
      width: 140,
    },
    {
      title: t('浮动成本'),
      render: 'variableCost',
      width: 140,
    },
    {
      title: t('出勤工资'),
      render: 'baseBasic',
      width: 160,
    },
    {
      title: t('超休福利假工资'),
      render: 'overDaySalary',
      width: 180,
    },
    {
      title: t('绩效+加班费'),
      render: 'basePerformanceOt',
      width: 160,
    },
    {
      title: t('福利补贴'),
      render: 'allowance',
      width: 120,
    },
    {
      title: t('服务费'),
      render: 'serviceCost',
      width: 120,
    },
    {
      title: t('应发合计'),
      render: 'totalPay',
      width: 160,
    },
    {
      title: t('产能'),
      render: 'production',
      width: 120,
    },
    {
      title: t('支援工时'),
      render: 'supportHour',
      width: 160,
    },
    {
      title: t('上月发薪人数'),
      render: 'lastPayUserNum',
      width: 160,
    },
    {
      title: t('上月出勤工资'),
      render: 'lastBaseBasic',
      width: 180,
    },
    {
      title: t('上月超休工资'),
      render: 'lastOverDaySalary',
      width: 180,
    },
    {
      title: t('上月绩效加班费'),
      render: 'lastBasePerformanceOt',
      width: 180,
    },
    {
      title: t('上月福利补贴'),
      render: 'lastAllowance',
      width: 180,
    },
    {
      title: t('上月服务费'),
      render: 'lastServiceCost',
      width: 120,
    },
    {
      title: t('上月产能'),
      render: 'lastProduction',
      width: 160,
    },
    {
      title: t('上月支援工时'),
      render: 'lastSupportHour',
      width: 160,
    },
  ];
    // 部门维度列表
  const deptDimensionColumns: TableColumnItem<IDataItem>[] = [
    {
      title: t('序号'),
      render: (d, index) => {
        const { pageNum, pageSize } = pageInfo;
        return (pageNum - 1) * pageSize + index + 1;
      },
      width: 120,
    },
    {
      title: t('唯一ID'),
      render: 'id',
      width: 100,
    },
    {
      title: t('期间'),
      render: 'ymString',
      width: 140,
    },
    {
      title: t('三级部门'),
      render: 'dept4Name',
      width: 180,
    },
    {
      title: t('四级部门'),
      render: 'dept5Name',
      width: 120,
    },
    {
      title: t('发薪人数'),
      render: 'payUserNum',
      width: 160,
    },
    {
      title: t('固定成本'),
      render: 'fixedCost',
      width: 140,
    },
    {
      title: t('浮动成本'),
      render: 'variableCost',
      width: 140,
    },
    {
      title: t('出勤工资'),
      render: 'baseBasic',
      width: 160,
    },
    {
      title: t('超休福利假工资'),
      render: 'overDaySalary',
      width: 180,
    },
    {
      title: t('绩效+加班费'),
      render: 'basePerformanceOt',
      width: 160,
    },
    {
      title: t('福利补贴'),
      render: 'allowance',
      width: 120,
    },
    {
      title: t('服务费'),
      render: 'serviceCost',
      width: 120,
    },
    {
      title: t('应发合计'),
      render: 'totalPay',
      width: 160,
    },
    {
      title: t('产能'),
      render: 'production',
      width: 120,
    },
    {
      title: t('支援工时'),
      render: 'supportHour',
      width: 160,
    },
    {
      title: t('上月发薪人数'),
      render: 'lastPayUserNum',
      width: 160,
    },
    {
      title: t('上月出勤工资'),
      render: 'lastBaseBasic',
      width: 180,
    },
    {
      title: t('上月超休工资'),
      render: 'lastOverDaySalary',
      width: 180,
    },
    {
      title: t('上月绩效加班费'),
      render: 'lastBasePerformanceOt',
      width: 180,
    },
    {
      title: t('上月福利补贴'),
      render: 'lastAllowance',
      width: 180,
    },
    {
      title: t('上月服务费'),
      render: 'lastServiceCost',
      width: 120,
    },
    {
      title: t('上月产能'),
      render: 'lastProduction',
      width: 160,
    },
    {
      title: t('上月支援工时'),
      render: 'lastSupportHour',
      width: 160,
    },
  ];
  // 付款主体列表
  const paySubjectColumns: TableColumnItem<IDataItem>[] = [
    {
      title: t('序号'),
      render: (d, index) => {
        const { pageNum, pageSize } = pageInfo;
        return (pageNum - 1) * pageSize + index + 1;
      },
      width: 120,
    },
    {
      title: t('唯一ID'),
      render: 'id',
      width: 100,
    },
    {
      title: t('期间'),
      render: 'ymString',
      width: 140,
    },
    {
      title: t('费用所属公司'),
      render: 'costCompanyDescr',
      width: 180,
    },
    {
      title: t('费用所属公司编码'),
      render: 'costCompany',
      width: 160,
    },
    {
      title: t('发薪人数'),
      render: 'payUserNum',
      width: 160,
    },
    {
      title: t('劳务报酬'),
      render: 'laborPay',
      width: 160,
    },
    {
      title: t('服务费'),
      render: 'serviceCost',
      width: 180,
    },
    {
      title: t('年终奖'),
      render: 'rewordYear',
      width: 160,
    },
    {
      title: t('税点金额'),
      render: 'taxCost',
      width: 120,
    },
    {
      title: t('结算劳务费'),
      render: 'settleCost',
      width: 120,
    },
  ];
  let columns = [];
  if (tabKey === 0) {
    columns = laborCompanyColumns;
  } else if (tabKey === 1) {
    columns = regionAndParkColumns;
  } else if (tabKey === 2) {
    columns = partAndJbColumns;
  } else if (tabKey === 3) {
    columns = deptDimensionColumns;
  } else if (tabKey === 4) {
    columns = paySubjectColumns;
  }
  const TabTable = (
    <Table<IDataItem, IDataItem[]>
      style={{ height: '100%' }}
      rowClassName={() => style.borderInner}
      bordered
      fixed="both"
      loading={!loading}
      data={list}
      columns={columns}
      keygen="id"
      empty={t('暂无数据')}
      size="small"
      width={columns.reduce((pre, current) => pre + (current.width || 100), 50)}
      pagination={{
        align: 'right',
        current: pageInfo.pageNum,
        pageSize: pageInfo.pageSize,
        className: style.pagination,
        layout: [({ total }) => `${t('共')} ${total} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
        onChange: (page, size) => {
          store.handlePaginationChange({
            pageNum: page,
            pageSize: size,
          });
        },
        pageSizeList: pageInfo.pageSizeList,
        total: pageInfo.count,
      }}
    />
  );

  return (
    <section className={style.tableSection}>
      <Tabs
        shape="card"
        active={tabKey}
        defaultActive={tabKey}
        onChange={(key) => {
          store.changeData({ tabKey: key });
          store.handlePaginationChange({ pageNum: 1 });
        }}
        className={classnames(styles.tabsSection, styles.tabStyle)}
      >
        <Tabs.Panel disabled={!limit.ym} className={styles.tabsPanel} tab={t('劳务公司')}>
          {TabTable}
        </Tabs.Panel>
        <Tabs.Panel disabled={!limit.ym} className={styles.tabsPanel} tab={t('片区园区')}>
          {TabTable}
        </Tabs.Panel>
        <Tabs.Panel disabled={!limit.ym} className={styles.tabsPanel} tab={t('环节岗位')}>
          {TabTable}
        </Tabs.Panel>
        <Tabs.Panel disabled={!limit.ym} className={styles.tabsPanel} tab={t('部门维度')}>
          {TabTable}
        </Tabs.Panel>
        <Tabs.Panel disabled={!limit.ym} className={styles.tabsPanel} tab={t('付款主体')}>
          {TabTable}
        </Tabs.Panel>
      </Tabs>
    </section>
  );
}

export default List;
