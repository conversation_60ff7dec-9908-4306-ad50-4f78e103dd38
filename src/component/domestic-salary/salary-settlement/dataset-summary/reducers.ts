import { markStatus } from 'rrc-loader-helper';
import { Modal } from 'shineout';
import { getSize } from '@src/middlewares/pagesize';
import { STATISTICAL_DOWNLOAD } from '@src/lib/jump-url';
import { checkUrlPermissionAPI } from '@src/server/common/common';
import moment from 'moment/moment';
import { dictSelect } from '@src/server/basic/dictionary';
import { handleListMsg } from '@src/lib/dealFunc';
import {
  ILimitType, IStateType, IGetListAPIResponse,
} from './types';
import {
  getParksAPI,
  getCompaniesAPI,
  getDeptByLevelAPI,
  getPsonJbAPI,
  queryCompanyListAPI,
  queryRegionParkListAPI,
  queryPartPosnJbListAPI,
  queryDeptListAPI,
  queryCostCompanyListAPI,
  queryCompanyListMaskAPI,
  queryRegionParkListMaskAPI,
  queryPartPosnJbListMaskAPI,
  queryDeptListMaskAPI,
  queryCostCompanyListMaskAPI,
  exportCompanyListAPI,
  exportRegionParkListAPI,
  exportPartPosnJbListAPI,
  exportDeptListAPI,
  exportCostCompanyListAPI,
  getRegionAPI,
} from './server';

export const defaultLimit: ILimitType = {
  /** 公司 */
  company: '',
  /** 三级部门 */
  dept4: '',
  /** 四级部门 */
  dept5: '',
  /** 园区 */
  parkId: '',
  /** 环节 */
  part: '',
  /** 岗位 */
  posnJb: '',
  /** 片区 */
  regionId: '',
  /** 期间-年月 */
  ym: moment().subtract(1, 'months').format('YYYYMM'), // 月份
};

const defaultState: IStateType = {
  // 搜索区域+下拉数据
  loading: 1, // 0 loading, 1 load success, 2 load fail  用于搜索和导出
  limit: defaultLimit,
  // 列表区域+分页区域
  pageInfo: {
    pageNum: 1,
    pageSize: getSize(1),
    count: 0, // 表格总条数
    pageSizeList: [20, 50, 100, 1000], // 表格页显示条数
  },
  list: [],
  tabKey: 0, // 0 劳务公司接口查询 1 片区园区接口查询 2 环节岗位接口查询 3 部门维度接口查询 4 付款主体接口查询
  showSensitiveData: false, // 查看敏感信息按钮切换值
  showSensitiveDataBtn: false, // 是否显示查看敏感信息按钮
  parkIdOptions: [], // 园区下拉
  companyOptions: [], // 公司下拉
  dept4Options: [], // 三级部门下拉
  dept5Options: [], // 四级部门下拉
  partOptions: [], // 环节下拉
  posnJbOptions: [], // 岗位下拉
  regionIdOptions: [], // 片区下拉
};

export default {
  state: defaultState,
  $init: () => defaultState, // 可选使用，在页面初始化的时候会重置state
  /**
   * 改变state的值
   */
  changeData(state: Partial<IStateType>, data?: Partial<IStateType>) {
    Object.assign(state, data);
  },
  /**
   * 改搜索条件limit属性值
   */
  changeLimitData(state: Partial<ILimitType>, data?: Partial<ILimitType>) {
    // !!! LCD对于action方法有改动state实际为IState，为保证程序正常强制as一下
    const stateData = state as IStateType;
    Object.assign(stateData, {
      limit: {
        ...stateData.limit,
        ...data,
      },
    });
  },
  /**
   * 重置查询条件
   */
  * clearLimitData() {
    const { formRef }: IStateType = yield '';
    yield this.changeLimitData(defaultLimit);
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },
  // 校验
  * handleFormValidate() {
    const { formRef }: IStateType = this.state;
    let validateFlag = false;
    if (formRef) {
      yield formRef.validate().then(() => {
        validateFlag = true;
      }).catch(() => {
        validateFlag = false;
      });
    }
    return validateFlag;
  },
  /**
   * 页签改变
   * @param {*} arg
   */
  * handlePaginationChange(arg = {}) {
    const validateFlag: boolean = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    const { pageInfo } = yield '';
    yield this.changeData({
      pageInfo: {
        ...pageInfo,
        ...arg,
      },
    });
    yield this.search();
  },
  /**
   * 初始化数据
   */
  * init(action, ctx) {
    // 检查是否有查看敏感信息权限
    yield this.checkPermission();
    const [selectData] = yield Promise.all([
      dictSelect({
        catCode: ['WBMS_SALARY_OA_USER_DEPT_PART'],
      }),
    ]);
    if (selectData.code === '0') {
      yield this.changeData({
        partOptions: selectData.info.data.find((item) => item.catCode === 'WBMS_SALARY_OA_USER_DEPT_PART').dictListRsps || [],
      });
    } else {
      handleListMsg([selectData], false);
    }
    // 获取园区下拉
    yield ctx.getParks();
    // 获取公司下拉
    yield ctx.getCompanies();
    // 获取三级部门下拉
    yield this.getDeptByLevel({
      deptLevel: '4',
      hasChild: 0,
      dept1: '800000',
    });
    // 获取四级部门下拉
    yield this.getDeptByLevel({
      deptLevel: '5',
      hasChild: 0,
      dept1: '800000',
    });
    // 获取岗位下拉
    yield ctx.getPsonJb();
    // 获取片区下拉
    yield ctx.getRegion();
  },
  // 获取片区下拉
  * getRegion() {
    markStatus('loading');
    const { code, info, msg } = yield getRegionAPI({
      pageNum: 1,
      pageSize: 10000,
    });
    if (code === '0') {
      yield this.changeData({
        regionIdOptions: (info?.data || []).map((i) => ({
          dictCode: i.id,
          dictNameZh: i.regionName,
        })),
      });
    } else {
      Modal.error({ content: msg });
    }
  },
  // 获取园区下拉
  * getParks() {
    markStatus('loading');
    const { code, info, msg } = yield getParksAPI({
      pageNum: 1,
      pageSize: 10000,
    });
    if (code === '0') {
      yield this.changeData({
        parkIdOptions: (info?.data || []).map((i) => ({
          dictCode: i.parkId,
          dictNameZh: i.parkName,
        })),
      });
    } else {
      Modal.error({ content: msg });
    }
  },
  // 获取公司下拉
  * getCompanies() {
    markStatus('loading');
    const { code, info, msg } = yield getCompaniesAPI({
      pageNum: 1,
      pageSize: 10000,
    });
    if (code === '0') {
      yield this.changeData({
        companyOptions: (info?.data || []).map((i) => ({
          dictCode: i.epLaborCompanyCode,
          dictNameZh: i.epLaborCompanyName,
        })),
      });
    } else {
      Modal.error({ content: msg });
    }
  },
  // 获取部门下拉
  * getDeptByLevel(params = {}) {
    markStatus('loading');
    const { code, info, msg } = yield getDeptByLevelAPI(params);
    if (code === '0') {
      // 三级部门
      if ((params as { deptLevel?: string })?.deptLevel === '4') {
        yield this.changeData({
          dept4Options: (info?.data || []).map((i) => ({
            dictCode: i.deptId,
            dictNameZh: i.pdDescrLong,
          })),
        });
      } else if ((params as { deptLevel?: string })?.deptLevel === '5') {
        // 四级部门
        yield this.changeData({
          dept5Options: (info?.data || []).map((i) => ({
            dictCode: i.deptId,
            dictNameZh: i.pdDescrLong,
          })),
        });
      }
    } else {
      Modal.error({ content: msg });
    }
  },
  // 获取岗位下拉
  * getPsonJb() {
    markStatus('loading');
    const { code, info, msg } = yield getPsonJbAPI({
      pageNum: 1,
      pageSize: 10000,
    });
    if (code === '0') {
      yield this.changeData({
        posnJbOptions: (info?.data || []).map((i) => ({
          dictCode: i.posnJb,
          dictNameZh: i.posnJbName,
        })),
      });
    } else {
      Modal.error({ content: msg });
    }
  },

  /**
   * 查询表格数据
   */
  * search() {
    const {
      pageInfo: {
        pageNum,
        pageSize,
      },
      limit,
      showSensitiveData,
      tabKey,
    }: IStateType = yield '';
    markStatus('loading');

    const params = {
      ...limit,
      pageNum,
      pageSize,
    };
    let res: IGetListAPIResponse = {};

    // 根据tabKey和showSensitiveData调用不同的API
    if (tabKey === 0) {
      // 劳务公司接口查询
      if (showSensitiveData) {
        res = yield queryCompanyListAPI(params);
      } else {
        res = yield queryCompanyListMaskAPI(params);
      }
    } else if (tabKey === 1) {
      // 片区园区接口查询
      if (showSensitiveData) {
        res = yield queryRegionParkListAPI(params);
      } else {
        res = yield queryRegionParkListMaskAPI(params);
      }
    } else if (tabKey === 2) {
      // 环节岗位接口查询
      if (showSensitiveData) {
        res = yield queryPartPosnJbListAPI(params);
      } else {
        res = yield queryPartPosnJbListMaskAPI(params);
      }
    } else if (tabKey === 3) {
      // 部门维度接口查询
      if (showSensitiveData) {
        res = yield queryDeptListAPI(params);
      } else {
        res = yield queryDeptListMaskAPI(params);
      }
    } else if (tabKey === 4) {
      // 付款主体接口查询
      if (showSensitiveData) {
        res = yield queryCostCompanyListAPI(params);
      } else {
        res = yield queryCostCompanyListMaskAPI(params);
      }
    }

    if (res.code === '0') {
      yield this.changeData({
        list: res.info?.data || [],
        pageInfo: {
          ...this.state.pageInfo,
          count: res.info?.meta?.count ?? 0,
        },
      });
    } else {
      Modal.error({
        title: res.msg,
      });
    }
  },

  /**
   * 导出
   * @returns
   */
  * exportData() {
    const {
      limit, pageInfo: { pageNum, pageSize }, tabKey,
    } = yield '';
    markStatus('loading');

    const params = {
      ...limit,
      pageNum,
      pageSize,
    };
    let res: IGetListAPIResponse = {};

    // 根据tabKey调用对应的导出接口
    if (tabKey === 0) {
      // 劳务公司导出
      res = yield exportCompanyListAPI(params);
    } else if (tabKey === 1) {
      // 片区园区导出
      res = yield exportRegionParkListAPI(params);
    } else if (tabKey === 2) {
      // 环节岗位导出
      res = yield exportPartPosnJbListAPI(params);
    } else if (tabKey === 3) {
      // 部门维度导出
      res = yield exportDeptListAPI(params);
    } else if (tabKey === 4) {
      // 付款主体导出
      res = yield exportCostCompanyListAPI(params);
    }

    if (res.code === '0') {
      window.open(STATISTICAL_DOWNLOAD);
    } else {
      Modal.error({ title: res.msg });
    }
  },
  /**
   * 检查是否有查看敏感品信息权限
   */
  * checkPermission() {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    const res = yield checkUrlPermissionAPI({ url: `${process.env.WBMS_FRONT}/salary_monthly_business_data/query_list_plaintext` });
    yield this.changeData({
      showSensitiveDataBtn: res.code === '0',
    });
  },
};
