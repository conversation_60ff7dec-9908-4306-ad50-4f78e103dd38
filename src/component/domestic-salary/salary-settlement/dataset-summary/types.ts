import {
  IFetchResponse, IPageInfo, IDictItem, IViewBaseProps,
} from '@src/typing/base';
import { FormRef } from 'shineout/lib/Form/Props';

export interface IDataItem {
    /** 福利补贴 */
  allowance?:number;
  /** 出勤工资 */
  baseBasic?:number;
  /** 绩效+加班费 */
  basePerformanceOt?:number;
  /** 公司编码 */
  company?:string;
  /** 公司 */
  companyName?:string;
  /** 唯一ID */
  id?:number;
  /** 上月福利补贴 */
  lastAllowance?:number;
  /** 上月出勤工资 */
  lastBaseBasic?:number;
  /** 上月绩效加班费 */
  lastBasePerformanceOt?:number;
  /** 上月超休工资 */
  lastOverDaySalary?:number;
  /** 上月发薪人数 */
  lastPayUserNum?:number;
  /** 上月产能 */
  lastProduction?:number;
  /** 上月服务费 */
  lastServiceCost?:number;
  /** 上月支援工时 */
  lastSupportHour?:number;
  /** 上月应发合计 */
  lastTotalPay?:number;
  /** 超休福利假工资 */
  overDaySalary?:number;
  /** 发薪人数 */
  payUserNum?:number;
  /** 产能 */
  production?:number;
  /** 服务费 */
  serviceCost?:number;
  /** 支援工时 */
  supportHour?:number;
  /** 应发合计 */
  totalPay?:number;
  ym?:number;
  /** 期间 */
  ymString?:string;
  /** 费用所属公司编码 */
  costCompany?:string;
  /** 费用所属公司名称 */
  costCompanyDescr?:string;
  dept4?:string;
  /** 三级部门 */
  dept4Name?:string;
  dept5?:string;
  /** 四级部门 */
  dept5Name?:string;
  part?:number;
  /** 环节 */
  partName?:string;
  posnJb?:string;
  /** 岗位 */
  posnJbName?:string;
  /** 片区id */
  regionId?:number;
  /** 片区名称 */
  regionName?:string;
    /** 园区id */
  parkId?:number;
  /** 园区名称 */
  parkName?:string;
  /** 唯一ID */
  id?:number;
  /** 劳务报酬 */
  laborPay?:number;
  /** 发薪人数 */
  payUserNum?:number;
  /** 年终奖 */
  rewordYear?:number;
  /** 服务费 */
  serviceCost?:number;
  /** 结算劳务费用 */
  settleCost?:number;
  /** 税点金额 */
  taxCost?:number;
  ym?:number;
  /** 期间 */
  ymString?:string;
  fixedCost?: string;
  variableCost?:string;
}

type IData = IDataItem[];

interface IMeta {
  /** 数据记录数 */
  count?:number;
  /** 用户自定义扩展 */
  customObj?:unknown;
}

interface IResponseBodyInfo {
  /** 结果 */
  data:IData;
  /** 元数据 */
  meta:IMeta;
}

export type IGetListAPIResponse = IFetchResponse<IResponseBodyInfo>;

export interface ILimitType {
  /** 公司 */
  company?:number | string;
  /** 三级部门 */
  dept4?:number | string;
  /** 四级部门 */
  dept5?:number | string;
  /** 当前页码 */
  pageNum?:number | string;
  /** 页宽度 */
  pageSize?:number | string;
  /** 园区 */
  parkId?:number | string;
  /** 环节 */
  part?:number | string;
  /** 岗位 */
  posnJb?:number | string;
  /** 片区 */
  regionId?:number | string;
  /** 期间-年月 */
  ym?:number | string;
}

export interface IStateType {
  loading: number; // 0 loading, 1 load success, 2 load fail
  limit: ILimitType;
  ready?: boolean;
  list: IDataItem[];
  pageInfo: IPageInfo;
  formRef?: FormRef<ILimitType>;
  showSensitiveData: boolean;
  showSensitiveDataBtn: boolean;
  tabKey: number;
  parkIdOptions: IDictItem[], // 园区下拉
  companyOptions: IDictItem[], // 公司下拉
  dept4Options: IDictItem[], // 三级部门下拉
  dept5Options: IDictItem[], // 四级部门下拉
  partOptions: IDictItem[], // 环节下拉
  posnJbOptions: IDictItem[], // 岗位下拉
  regionIdOptions: IDictItem[], // 片区下拉
}
interface IResponseBodyInfo {
  /** 结果 */
  data:IData;
  /** 元数据 */
  meta:IMeta;
}

export type IPageProps = IViewBaseProps<IStateType>;
