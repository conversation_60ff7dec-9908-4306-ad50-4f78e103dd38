import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Table } from 'shineout';
import { handleTablePros } from '@src/lib/deal-func';
import globalStyles from '@src/component/style.less';
import store from '../reducers';

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      pageInfo,
      selectedRows,
    } = this.props;

    const colorMapping = {
      basicGroup: '#420780', // 基本信息
      attendanceGroup: '#018080', // 考勤汇总
      salaryGroup: '#4b7901', // 薪酬
      benefitGroup: '#6d000e', // 福利
      otherIncomeGroup: '#015478', // 其他收入
      preTaxDeductionGroup: '#420780', // 税前扣款
      afterTaxItemsGroup: '#FFA500', // 税后加减项
      individualFundGroup: '#000000', // 社保公积金-个人
      enterpriseFundGroup: '#010b80', // 社保公积金-企业
      normalGroup: '#09656a', // 占位
      detailTaxGroup: '#facd91', // 明细税项
    };

    function groupTitle({ title, groupType }) {
      const backgroundColor = colorMapping[groupType] || '#fff'; // 无匹配时用默认色
      return (
        <div
          style={{
            background: backgroundColor,
            display: 'block',
            lineHeight: '40px',
            color: '#fff',
            padding: '0 10px',
            textAlign: 'left',
          }}
        >
          {title}
        </div>
      );
    }
    const basicGroup = groupTitle({
      title: t('基本信息'),
      groupType: 'basicGroup',
    });
    const attendanceGroup = groupTitle({
      title: t('考勤汇总'),
      groupType: 'attendanceGroup',
    });
    const salaryGroup = groupTitle({
      title: t('薪酬'),
      groupType: 'salaryGroup',
    });
    const benefitGroup = groupTitle({
      title: t('福利'),
      groupType: 'benefitGroup',
    });
    const otherIncomeGroup = groupTitle({
      title: t('其他收入'),
      groupType: 'otherIncomeGroup',
    });
    const preTaxDeductionGroup = groupTitle({
      title: t('税前扣款'),
      groupType: 'preTaxDeductionGroup',
    });
    const afterTaxItemsGroup = groupTitle({
      title: t('个税及税后加减项'),
      groupType: 'afterTaxItemsGroup',
    });
    const individualFundGroup = groupTitle({
      title: t('社保公积金-个人'),
      groupType: 'individualFundGroup',
    });
    const enterpriseFundGroup = groupTitle({
      title: t('社保公积金-企业'),
      groupType: 'enterpriseFundGroup',
    });
    const detailTaxGroup = groupTitle({
      title: t('明细税项'),
      groupType: 'detailTaxGroup',
    });
    const columns = [
      {
        title: t('日历组'),
        render: 'ym',
        width: 160,
        group: [basicGroup],
      },
      {
        title: t('期间'),
        render: 'period',
        width: 140,
        group: [basicGroup],
      },
      {
        title: t('结算状态'),
        render: 'settleStatusName',
        width: 140,
        group: [basicGroup],
      },
      {
        title: t('任务标记'),
        render: 'taskFlagName',
        width: 140,
        group: [basicGroup],
      },
      {
        title: t('工号'),
        render: 'uname',
        width: 140,
        group: [basicGroup],
      },
      {
        title: t('姓名'),
        render: 'fullName',
        width: 140,
        group: [basicGroup],
      },
      {
        title: t('公司'),
        width: 140,
        render: 'companyDescr',
        group: [basicGroup],
      },
      {
        title: t('费用所属公司'),
        width: 140,
        render: 'costCompanyDescr',
        group: [basicGroup],
      },
      {
        title: t('薪资组'),
        width: 140,
        render: 'gpPaygroup',
        group: [basicGroup],
      },
      {
        title: t('所属部门'),
        width: 140,
        render: 'shDptLj',
        group: [basicGroup],
      },
      {
        title: t('集团'),
        width: 140,
        render: 'dept1Desc',
        group: [basicGroup],
      },
      {
        title: t('一级中心'),
        width: 140,
        render: 'dept2Desc',
        group: [basicGroup],
      },
      {
        title: t('二级部门'),
        width: 140,
        render: 'dept3Desc',
        group: [basicGroup],
      },
      {
        title: t('三级部门'),
        width: 140,
        render: 'dept4Desc',
        group: [basicGroup],
      },
      {
        title: t('四级部门'),
        width: 140,
        render: 'dept5Desc',
        group: [basicGroup],
      },
      {
        title: t('五级部门'),
        width: 140,
        render: 'dept6Desc',
        group: [basicGroup],
      },
      {
        title: t('六级部门'),
        width: 140,
        render: 'dept7Desc',
        group: [basicGroup],
      },
      {
        title: t('岗位'),
        render: 'posnJbName',
        width: 140,
        group: [basicGroup],
      },
      {
        title: t('业务环节'),
        render: 'partName',
        width: 140,
        group: [basicGroup],
      },
      {
        title: t('个人职级'),
        render: 'supvLevelName',
        width: 140,
        group: [basicGroup],
      },
      {
        title: t('职级子等'),
        render: 'subLevelName',
        width: 140,
        group: [basicGroup],
      },
      {
        title: t('实际长期工作城市'),
        render: 'locationName',
        width: 140,
        group: [basicGroup],
      },
      {
        title: t('仓库'),
        render: 'warehouseName',
        width: 140,
        group: [basicGroup],
      },
      {
        title: t('片区'),
        render: 'region',
        width: 140,
        group: [basicGroup],
      },
      {
        title: t('园区'),
        render: 'parkName',
        width: 140,
        group: [basicGroup],
      },
      {
        title: t('职类'),
        render: 'jobCategory',
        width: 140,
        group: [basicGroup],
      },
      {
        title: t('序列'),
        render: 'jobSequence',
        width: 140,
        group: [basicGroup],
      },
      {
        title: t('职种'),
        render: 'jobKind',
        width: 140,
        group: [basicGroup],
      },
      {
        title: t('工时制'),
        render: 'workerSystemName',
        width: 140,
        group: [basicGroup],
      },
      {
        title: t('入职时间'),
        render: 'firstHireDate',
        width: 160,
        group: [basicGroup],
      },
      {
        title: t('转正时间'),
        render: 'probationDate',
        width: 160,
        group: [basicGroup],
      },
      {
        title: t('最后工作日'),
        render: 'terminationDate',
        width: 160,
        group: [basicGroup],
      },
      {
        title: t('离职原因'),
        render: 'epLeaveReason',
        width: 160,
        group: [basicGroup],
      },
      {
        title: t('离职子原因'),
        render: 'epLeaveReasonSub',
        width: 160,
        group: [basicGroup],
      },
      {
        title: t('调薪生效日期'),
        render: 'salaryAdjustEffectiveDate',
        width: 160,
        group: [basicGroup],
      },
      {
        title: t('外派开始日期'),
        render: 'expatriateBeginDate',
        width: 160,
        group: [basicGroup],
      },
      {
        title: t('外派结束日期'),
        render: 'expatriateEndDate',
        width: 160,
        group: [basicGroup],
      },
      {
        // 已处理敏感词
        title: t('不结算部分'),
        render: 'noSettlePart',
        width: 160,
        needThousandsPoint: true,
        group: [basicGroup],
      },
      {
        title: t('应出勤天数'),
        render: 'shouldAttendDay',
        width: 160,
        group: [attendanceGroup],
      },
      {
        title: t('实际出勤天数'),
        render: 'actualAttendDurationDay',
        width: 160,
        group: [attendanceGroup],
      },
      {
        title: t('事假(天)'),
        render: 'sjsc',
        width: 140,
        group: [attendanceGroup],
      },
      {
        title: t('病假(天)'),
        render: 'bjsc',
        width: 140,
        group: [attendanceGroup],
      },
      {
        title: t('旷工(天)'),
        render: 'kgsc',
        width: 140,
        group: [attendanceGroup],
      },
      {
        title: t('产假(天)'),
        render: 'cjsc',
        width: 140,
        group: [attendanceGroup],
      },
      {
        title: t('工伤假(天)'),
        render: 'gsjsc',
        width: 140,
        group: [attendanceGroup],
      },
      {
        title: t('调休(天)'),
        render: 'txsc',
        width: 140,
        group: [attendanceGroup],
      },
      {
        title: t('早退(次)'),
        render: 'leaveEarlyCount',
        width: 140,
        group: [attendanceGroup],
      },
      {
        title: t('迟到(次)'),
        render: 'lateCount',
        width: 140,
        group: [attendanceGroup],
      },
      {
        title: t('缺卡(次)'),
        render: 'missCardCount',
        width: 140,
        group: [attendanceGroup],
      },
      {
        title: t('平时加班时长汇总'),
        render: 'usualOtDurationHour',
        width: 140,
        group: [attendanceGroup],
      },
      {
        title: t('周末加班时长汇总'),
        render: 'weekendOtDuration',
        width: 140,
        group: [attendanceGroup],
      },
      {
        title: t('法定节假日加班时长汇总'),
        render: 'festivalOtDuration',
        width: 140,
        group: [attendanceGroup],
      },
      {
        title: t('平日加班次数'),
        render: 'usualOtTimes',
        width: 140,
        group: [attendanceGroup],
      },
      {
        // 已处理敏感词
        title: t('基本工资'),
        render: 'basicSalary',
        width: 140,
        needThousandsPoint: true,
        group: [salaryGroup],
      },
      {
        // 已处理敏感词
        title: t('调薪前基本工资'),
        render: 'oldBasicSalary',
        width: 140,
        needThousandsPoint: true,
        group: [salaryGroup],
      },
      {
        // 已处理敏感词
        title: t('岗位工资'),
        render: 'jobAllowance',
        width: 160,
        needThousandsPoint: true,
        group: [salaryGroup],
      },
      {
        // 已处理敏感词
        title: t('调薪前岗位工资'),
        render: 'oldJobAllowance',
        width: 160,
        needThousandsPoint: true,
        group: [salaryGroup],
      },
      {
        // 已处理敏感词
        title: t('职级津贴'),
        render: 'supvLevelAllowance',
        width: 160,
        needThousandsPoint: true,
        group: [salaryGroup],
      },
      {
        // 已处理敏感词
        title: t('调薪前职级津贴'),
        render: 'oldSupvLevelAllowance',
        width: 160,
        needThousandsPoint: true,
        group: [salaryGroup],
      },
      {
        // 已处理敏感词
        title: t('绩效基数'),
        render: 'performance',
        width: 160,
        group: [salaryGroup],
      },
      {
        // 已处理敏感词
        title: t('出勤工资'),
        render: 'baseBasic',
        width: 160,
        needThousandsPoint: true,
        group: [salaryGroup],
      },
      {
        // 已处理敏感词
        title: t('病假工资'),
        render: 'baseSick',
        width: 160,
        needThousandsPoint: true,
        group: [salaryGroup],
      },
      {
        // 已处理敏感词
        title: t('加班工资'),
        render: 'baseOt',
        width: 160,
        needThousandsPoint: true,
        group: [salaryGroup],
      },
      {
        // 已处理敏感词
        title: t('其他补贴'),
        render: 'otherSubsidy',
        width: 160,
        needThousandsPoint: true,
        group: [salaryGroup],
      },
      {
        // 已处理敏感词
        title: t('绩效'),
        render: 'basePerformance',
        width: 160,
        needThousandsPoint: true,
        group: [salaryGroup],
      },
      {
        // 已处理敏感词
        title: t('月度奖金'),
        render: 'monthBonus',
        width: 160,
        needThousandsPoint: true,
        group: [salaryGroup],
      },
      {
        // 已处理敏感词
        title: t('项目激励'),
        render: 'projectIncentive',
        width: 160,
        needThousandsPoint: true,
        group: [salaryGroup],
      },
      {
        // 已处理敏感词
        title: t('专项激励'),
        render: 'specialIncentive',
        width: 160,
        needThousandsPoint: true,
        group: [salaryGroup],
      },
      {
        // 已处理敏感词
        title: t('其他奖金'),
        render: 'otherBonus',
        width: 160,
        needThousandsPoint: true,
        group: [salaryGroup],
      },
      {
        // 已处理敏感词
        title: t('餐补-常规'),
        render: 'allowanceMeat',
        width: 140,
        needThousandsPoint: true,
        group: [benefitGroup],
      },
      {
        // 已处理敏感词
        title: t('餐补-海外调任'),
        render: 'allowanceMeatAbroad',
        width: 140,
        needThousandsPoint: true,
        group: [benefitGroup],
      },
      {
        // 已处理敏感词
        title: t('餐补-短期调任'),
        render: 'allowanceMeatShort',
        width: 140,
        needThousandsPoint: true,
        group: [benefitGroup],
      },
      {
        // 已处理敏感词
        title: t('房补-常规'),
        render: 'allowanceHouse',
        width: 140,
        needThousandsPoint: true,
        group: [benefitGroup],
      },
      {
        // 已处理敏感词
        title: t('房补-海外调任'),
        render: 'allowanceHouseAbroad',
        width: 140,
        needThousandsPoint: true,
        group: [benefitGroup],
      },
      {
        // 已处理敏感词
        title: t('房补-短期调任'),
        render: 'allowanceHouseShort',
        width: 140,
        needThousandsPoint: true,
        group: [benefitGroup],
      },
      {
        // 已处理敏感词
        title: t('外派补贴'),
        render: 'allowanceExpatriate',
        width: 140,
        needThousandsPoint: true,
        group: [benefitGroup],
      },
      {
        // 已处理敏感词
        title: t('社保补贴'),
        render: 'allowanceSociety',
        width: 140,
        needThousandsPoint: true,
        group: [benefitGroup],
      },
      {
        // 已处理敏感词
        title: t('高温补贴'),
        render: 'allowanceMegathermal',
        width: 140,
        needThousandsPoint: true,
        group: [benefitGroup],
      },
      {
        // 已处理敏感词
        title: t('体检补贴'),
        render: 'allowancePhysical',
        width: 140,
        needThousandsPoint: true,
        group: [benefitGroup],
      },
      {
        // 已处理敏感词
        title: t('通讯补贴'),
        render: 'allowanceCommunication',
        width: 140,
        needThousandsPoint: true,
        group: [benefitGroup],
      },
      {
        // 已处理敏感词
        title: t('交通补贴'),
        render: 'allowanceTransport',
        width: 140,
        needThousandsPoint: true,
        group: [benefitGroup],
      },
      {
        // 已处理敏感词
        title: t('自带电脑补贴'),
        render: 'allowanceComputer',
        width: 140,
        needThousandsPoint: true,
        group: [benefitGroup],
      },
      {
        // 已处理敏感词
        title: t('阶段性补贴'),
        render: 'allowanceStage',
        width: 140,
        needThousandsPoint: true,
        group: [benefitGroup],
      },
      {
        // 已处理敏感词
        title: t('班次补贴'),
        render: 'allowanceNight',
        width: 140,
        needThousandsPoint: true,
        group: [benefitGroup],
      },
      {
        // 已处理敏感词
        title: t('其他方式发放计税'),
        render: 'taxOther',
        width: 140,
        needThousandsPoint: true,
        group: [benefitGroup],
      },
      {
        // 已处理敏感词
        title: t('节假日福利计税'),
        render: 'taxFestival',
        width: 140,
        needThousandsPoint: true,
        group: [benefitGroup],
      },
      {
        // 已处理敏感词
        title: t('推荐奖'),
        render: 'rewardRecommend',
        width: 140,
        needThousandsPoint: true,
        group: [benefitGroup],
      },
      {
        // 已处理敏感词
        title: t('培训津贴'),
        render: 'allowanceTrain',
        width: 140,
        needThousandsPoint: true,
        group: [benefitGroup],
      },
      {
        // 已处理敏感词
        title: t('工商高管岗位津贴'),
        render: 'allowanceExecutive',
        width: 140,
        needThousandsPoint: true,
        group: [benefitGroup],
      },
      {
        // 已处理敏感词
        title: t('其他补发'),
        render: 'allowanceOther',
        width: 140,
        needThousandsPoint: true,
        group: [groupTitle({
          title: t('其他补发'),
          groupType: 'normalGroup',
        })],
      },
      {
        // 已处理敏感词
        title: t('年终奖'),
        render: 'rewordYear',
        width: 140,
        needThousandsPoint: true,
        group: [groupTitle({
          title: t('年终奖'),
          groupType: 'normalGroup',
        })],
      },
      {
        // 已处理敏感词
        title: t('离职补偿金'),
        render: 'moneyResign',
        width: 140,
        needThousandsPoint: true,
        group: [otherIncomeGroup],
      },
      {
        // 已处理敏感词
        title: t('竞业限制补偿金'),
        render: 'moneyCompetition',
        width: 140,
        needThousandsPoint: true,
        group: [otherIncomeGroup],
      },
      {
        // 已处理敏感词
        title: t('签字费'),
        render: 'moneySign',
        width: 140,
        needThousandsPoint: true,
        group: [otherIncomeGroup],
      },
      {
        // 已处理敏感词
        title: t('一次性工伤就业补助金'),
        render: 'moneyOneEmployment',
        width: 140,
        needThousandsPoint: true,
        group: [otherIncomeGroup],
      },
      {
        // 已处理敏感词
        title: t('一次性工伤伤残补助金'),
        render: 'moneyOneCripple',
        width: 140,
        needThousandsPoint: true,
        group: [otherIncomeGroup],
      },
      {
        // 已处理敏感词
        title: t('一次性工伤医疗补助金'),
        render: 'moneyOneMedical',
        width: 140,
        needThousandsPoint: true,
        group: [otherIncomeGroup],
      },
      {
        // 已处理敏感词
        title: t('应发合计'),
        render: 'totalPay',
        width: 140,
        needThousandsPoint: true,
        group: [groupTitle({
          title: t('应发合计'),
          groupType: 'normalGroup',
        })],

      },
      {
        // 已处理敏感词
        title: t('社保'),
        render: 'persShbxTotal',
        width: 140,
        needThousandsPoint: true,
        group: [preTaxDeductionGroup],
      },
      {
        // 已处理敏感词
        title: t('公积金'),
        render: 'persGjj',
        width: 140,
        needThousandsPoint: true,
        group: [preTaxDeductionGroup],
      },
      {
        // 已处理敏感词
        title: t('下月社保-个人'),
        render: 'personalSociety',
        width: 140,
        needThousandsPoint: true,
        group: [preTaxDeductionGroup],
      },
      {
        // 已处理敏感词
        title: t('其他扣款'),
        render: 'otherDeduction',
        width: 140,
        needThousandsPoint: true,
        group: [preTaxDeductionGroup],
      },
      {
        // 已处理敏感词
        title: t('应扣合计'),
        render: 'totalDeduction',
        width: 140,
        needThousandsPoint: true,
        group: [groupTitle({
          title: t('应扣合计'),
          groupType: 'normalGroup',
        })],

      },
      {
        // 已处理敏感词
        title: t('税前工资'),
        render: 'grossPay',
        width: 140,
        needThousandsPoint: true,
        group: [groupTitle({
          title: t('税前工资'),
          groupType: 'normalGroup',
        })],
      },
      {
        // 已处理敏感词
        title: t('个税'),
        render: 'individualIncomeTax',
        width: 140,
        needThousandsPoint: true,
        group: [afterTaxItemsGroup],
      },
      {
        // 已处理敏感词
        title: t('年终奖纳税'),
        render: 'rewordYearTax',
        width: 140,
        needThousandsPoint: true,
        group: [afterTaxItemsGroup],
      },
      {
        // 已处理敏感词
        title: t('离职补偿金纳税'),
        render: 'moneyResignTax',
        width: 140,
        needThousandsPoint: true,
        group: [afterTaxItemsGroup],
      },
      {
        // 已处理敏感词
        title: t('税后扣下月社保公司部分'),
        render: 'companySociety',
        width: 140,
        needThousandsPoint: true,
        group: [afterTaxItemsGroup],
      },
      {
        // 已处理敏感词
        title: t('互助基金扣款'),
        render: 'fundDeductions',
        width: 140,
        needThousandsPoint: true,
        group: [afterTaxItemsGroup],
      },
      {
        // 已处理敏感词
        title: t('税后借支'),
        render: 'taxBorrow',
        width: 140,
        needThousandsPoint: true,
        group: [afterTaxItemsGroup],
      },
      {
        // 已处理敏感词
        title: t('税后扣款'),
        render: 'taxDeductions',
        width: 140,
        needThousandsPoint: true,
        group: [afterTaxItemsGroup],
      },
      {
        // 已处理敏感词
        title: t('税后补发'),
        render: 'taxReissue',
        width: 140,
        needThousandsPoint: true,
        group: [afterTaxItemsGroup],
      },
      {
        // 已处理敏感词
        title: t('年终奖税后扣款'),
        render: 'yearRewardTaxDeduction',
        width: 140,
        needThousandsPoint: true,
        group: [afterTaxItemsGroup],
      },
      {
        // 已处理敏感词
        title: t('实发工资'),
        render: 'realSalary',
        width: 140,
        needThousandsPoint: true,
        group: [groupTitle({
          title: t('实发工资'),
          groupType: 'normalGroup',
        })],
      },
      {
        // 已处理敏感词
        title: t('实发年终奖'),
        render: 'realRewordYear',
        width: 140,
        needThousandsPoint: true,
        group: [groupTitle({
          title: t('实发年终奖'),
          groupType: 'normalGroup',
        })],
      },
      {
        // 已处理敏感词
        title: t('银行实发'),
        render: 'bankRealSalary',
        width: 140,
        needThousandsPoint: true,
        group: [groupTitle({
          title: t('银行实发'),
          groupType: 'normalGroup',
        })],
      },
      {
        // 已处理敏感词
        title: t('公积金-个人'),
        render: 'providentFundIndividual',
        width: 140,
        needThousandsPoint: true,
        group: [individualFundGroup],
      },
      {
        // 已处理敏感词
        title: t('养老保险-个人'),
        render: 'personalYanglaobx',
        width: 140,
        needThousandsPoint: true,
        group: [individualFundGroup],
      },
      {
        // 已处理敏感词
        title: t('医疗保险-个人'),
        render: 'personalYiliaobx',
        width: 140,
        needThousandsPoint: true,
        group: [individualFundGroup],
      },
      {
        // 已处理敏感词
        title: t('失业保险-个人'),
        render: 'personalShiyebx',
        width: 140,
        needThousandsPoint: true,
        group: [individualFundGroup],
      },
      {
        // 已处理敏感词
        title: t('大病医疗-个人'),
        render: 'personalDabingbx',
        width: 140,
        needThousandsPoint: true,
        group: [individualFundGroup],
      },
      {
        // 已处理敏感词
        title: t('生育保险-个人'),
        render: 'personalShengyubx',
        width: 140,
        needThousandsPoint: true,
        group: [individualFundGroup],
      },
      {
        // 已处理敏感词
        title: t('工伤保险-个人'),
        render: 'personalGsbx',
        width: 140,
        needThousandsPoint: true,
        group: [individualFundGroup],
      },
      {
        // 已处理敏感词
        title: t('个人社保公积金合计'),
        render: 'personalShbxTotal',
        width: 140,
        needThousandsPoint: true,
        group: [individualFundGroup],
      },
      {
        // 已处理敏感词
        title: t('公积金-企业'),
        render: 'corpGjj',
        width: 140,
        needThousandsPoint: true,
        group: [enterpriseFundGroup],
      },
      {
        // 已处理敏感词
        title: t('养老保险-企业'),
        render: 'corpYanglaobx',
        width: 140,
        needThousandsPoint: true,
        group: [enterpriseFundGroup],
      },
      {
        // 已处理敏感词
        title: t('医疗保险-企业'),
        render: 'corpYiliaobx',
        width: 140,
        needThousandsPoint: true,
        group: [enterpriseFundGroup],
      },
      {
        // 已处理敏感词
        title: t('失业保险-企业'),
        render: 'corpShiyebx',
        width: 140,
        needThousandsPoint: true,
        group: [enterpriseFundGroup],
      },
      {
        // 已处理敏感词
        title: t('生育保险-企业'),
        render: 'corpShengyubx',
        width: 140,
        needThousandsPoint: true,
        group: [enterpriseFundGroup],
      },
      {
        // 已处理敏感词
        title: t('工伤保险-企业'),
        render: 'corpGsbx',
        width: 140,
        needThousandsPoint: true,
        group: [enterpriseFundGroup],
      },
      {
        // 已处理敏感词
        title: t('大病医疗-企业'),
        render: 'corpZjx',
        width: 140,
        needThousandsPoint: true,
        group: [enterpriseFundGroup],
      },
      {
        // 已处理敏感词
        title: t('补充医疗保险-企业'),
        render: 'corpBcyl',
        width: 140,
        needThousandsPoint: true,
        group: [enterpriseFundGroup],
      },
      {
        // 已处理敏感词
        title: t('公司社保公积金合计'),
        render: 'corpShbxTotal',
        width: 140,
        needThousandsPoint: true,
        group: [enterpriseFundGroup],
      },
      {
        // 已处理敏感词
        title: t('累计子女教育专项扣除'),
        render: 'taxChildEducation',
        width: 140,
        needThousandsPoint: true,
        group: [detailTaxGroup],
      },
      {
        // 已处理敏感词
        title: t('累计赡养老人专项扣除'),
        render: 'taxSupportOld',
        width: 140,
        needThousandsPoint: true,
        group: [detailTaxGroup],
      },
      {
        // 已处理敏感词
        title: t('累计住房贷款利息专项扣除'),
        render: 'taxHouseLoanInterest',
        width: 140,
        needThousandsPoint: true,
        group: [detailTaxGroup],
      },
      {
        // 已处理敏感词
        title: t('累计住房租金专项扣除'),
        render: 'taxHouseRent',
        width: 140,
        needThousandsPoint: true,
        group: [detailTaxGroup],
      },
      {
        // 已处理敏感词
        title: t('累计继续教育专项扣除'),
        render: 'taxContinueEducation',
        width: 140,
        needThousandsPoint: true,
        group: [detailTaxGroup],
      },
      {
        // 已处理敏感词
        title: t('累计婴幼儿照护费用专项扣除'),
        render: 'taxInfantCare',
        width: 140,
        needThousandsPoint: true,
        group: [detailTaxGroup],
      },
      {
        // 已处理敏感词
        title: t('累计收入额'),
        render: 'taxTotalIncome',
        width: 140,
        needThousandsPoint: true,
        group: [detailTaxGroup],
      },
      {
        // 已处理敏感词
        title: t('累计免税收入'),
        render: 'taxTotalExemptIncome',
        width: 140,
        needThousandsPoint: true,
        group: [detailTaxGroup],
      },
      {
        // 已处理敏感词
        title: t('本月累计减除费用'),
        render: 'taxTotalDeduction',
        width: 140,
        needThousandsPoint: true,
        group: [detailTaxGroup],
      },
      {
        // 已处理敏感词
        title: t('累计专项扣除'),
        render: 'taxTotalSpecialDeduction',
        width: 140,
        needThousandsPoint: true,
        group: [detailTaxGroup],
      },
      {
        // 已处理敏感词
        title: t('税率/预扣率'),
        render: 'taxRate',
        width: 140,
        needThousandsPoint: true,
        group: [detailTaxGroup],
      },
      {
        // 已处理敏感词
        title: t('速算扣除数'),
        render: 'taxQuickDeduction',
        width: 140,
        needThousandsPoint: true,
        group: [detailTaxGroup],
      },
      {
        // 已处理敏感词
        title: t('已缴税额'),
        render: 'taxPaid',
        width: 140,
        needThousandsPoint: true,
        group: [detailTaxGroup],
      },
      {
        // 已处理敏感词
        title: t('年会奖金个税扣回'),
        render: 'taxYearBonus',
        width: 140,
        needThousandsPoint: true,
        group: [detailTaxGroup],
      },
      {
        // 已处理敏感词
        title: t('残保金'),
        render: 'cbj',
        width: 140,
        needThousandsPoint: true,
        group: [groupTitle({
          title: t('残保金'),
          groupType: 'normalGroup',
        })],

      },
      {
        // 已处理敏感词
        title: t('劳务报酬'),
        render: 'laborPay',
        width: 140,
        needThousandsPoint: true,
        group: [groupTitle({
          title: t('劳务报酬'),
          groupType: 'normalGroup',
        })],

      },
      {
        // 已处理敏感词
        title: t('服务费'),
        render: 'serviceCost',
        width: 140,
        needThousandsPoint: true,
        group: [groupTitle({
          title: t('服务费'),
          groupType: 'normalGroup',
        })],

      },
      {
        // 已处理敏感词
        title: t('税点金额'),
        render: 'taxCost',
        width: 140,
        needThousandsPoint: true,
        group: [groupTitle({
          title: t('税点金额'),
          groupType: 'normalGroup',
        })],
      },
      {
        // 已处理敏感词
        title: t('更新时间'),
        render: 'lastUpdateTime',
        width: 140,
        group: [groupTitle({
          title: t('更新时间'),
          groupType: 'normalGroup',
        })],

      },
      {
        // 已处理敏感词
        title: t('数据版本号'),
        render: 'version',
        width: 140,
        group: [groupTitle({
          title: t('数据版本号'),
          groupType: 'normalGroup',
        })],
      },
    ].map((column) => ({
      ...column,
      groupKeygen: column.title,
    }));

    return (
      <section className={globalStyles.tableSection}>
        <Table
          {...handleTablePros(columns)}
          loading={!loading}
          data={list}
          keygen="id"
          value={selectedRows}
          onRowSelect={(rows) => {
            store.changeData({
              selectedRows: rows,
            });
          }}
          showFixed={false}
          autoSortable={false}
          pagination={{
            align: 'right',
            current: pageInfo.pageNum,
            pageSize: pageInfo.pageSize,
            className: globalStyles.pagination,
            layout: [({ total }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
            onChange: (page, size) => {
              store.handlePaginationChange({
                pageNum: page,
                pageSize: size,
              });
            },
            pageSizeList: pageInfo.pageSizeList,
            total: pageInfo.count,
          }}
        />
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number.isRequired,
  list: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  pageInfo: PropTypes.shape(),
  selectedRows: PropTypes.arrayOf(PropTypes.shape),
};

export default List;
