import React, { Component } from 'react';
import PropTypes from 'prop-types';
import {
  Button, Modal, Popover, Message, Input, Table, Select, Form, Rule, Radio, Switch, Textarea,
} from 'shineout';
import ImagesPreviewer from '@shein-components/ImagesPreviewer';
import { t } from '@shein-bbl/react';
import styles from '@src/component/style.less';
import UploadPlus from '@shein-components/upload_plus';
import { uploadFileURL } from '@src/server/basic/upload';
import { userNameSessionStorage, userNoSessionStorage } from '@src/lib/storage-new';
import DetailPreviewSubmit from '../../detail-preview-submit';
import store, { defaultShowInfoObj } from '../reducers';
import style from '../style.less';
import SecurityLogRecord from '../../../wbms-public-component/security-log-record';

const { Gallery } = ImagesPreviewer;

const rules = new Rule();
class Handle extends Component {
  render() {
    const {
      loading,
      selectedRows,
      detailInfo,
      detailInfoVisible,
      currencyTypeNameList,
      costExchangeRateTypeList,
      costExchangeRateList,
      importModalVisible,
      fileList,
      isSubmitAfterPreview,
      certificateRecordVisible,
      certificateRecordData,
      certificateRecordPreviewVisible,
      certificateRecordPreviewUrl,
      refundPaymentVisible,
      refundPaymentObj,
      costCodeList,
      // tmpList,
      // pageInfo,
      payOrderStatusList, // 状态下拉
      paymentRuleNo,
      showSensitiveData,
      showPreCommitSensitiveData,
      PreCommitSensitiveDataOrderId,
      isShowServiceProviderSensitiveData,
      allStatusKindCount,
      invalidModalVisible,
      invalidRemark,
      invalidType,
      pushFinancialModalVisible,
      pushFinancialObj,
    } = this.props;

    // // 统计各个状态的条数
    // const allStatusKindCount = tmpList.reduce((countInfo, cur) => {
    //   if (countInfo[cur.payOrderStatus] || countInfo[cur.payOrderStatus] === 0) {
    //     // 合并二审-六审到审批中
    //     if ([1, 22, 23, 24, 25, 26].indexOf(cur.payOrderStatus) > -1) {
    //       countInfo[1] += 1;
    //     } else {
    //       countInfo[cur.payOrderStatus] += 1;
    //     }
    //   } else if ([22, 23, 24, 25, 26].indexOf(cur.payOrderStatus) > -1) {
    //     countInfo[1] += 1;
    //   } else {
    //     countInfo[cur.payOrderStatus] = 1;
    //   }
    //   return countInfo;
    // }, { 1: 0 });

    const certificateRecordColumns = [
      {
        title: t('创建时间'),
        width: 240,
        render: 'createTime',
      },
      {
        title: t('文件名称'),
        width: 200,
        render: 'fileName',
      },
      {
        title: t('操作'),
        width: 160,
        render: (record) => (
          <div style={{
            display: 'flex', alignItems: 'center', flexWrap: 'wrap',
          }}
          >
            <SecurityLogRecord
              businessNo={paymentRuleNo}
              fileUrl={record.fileUrl}
              fileName={record.fileName}
              operationType={7}
              securityLevel={2}
              securityModule={1}
              style={{
                margin: '0 5px',
              }}
            />
            <Button
              size="small"
              text
              type="primary"
              className={style.listOperationButton}
              onClick={() => store.changeData({
                recordVisible: true,
                recordId: record.id,
                operateCode: 'STD_CHARGE_PAY_ORDER_ELECTRONIC_SIGN',
                operationTitle: t('操作日志'),
              })}
            >
              {t('操作日志')}
            </Button>
            {/* <Button */}
            {/*  size="small" */}
            {/*  text */}
            {/*  type="primary" */}
            {/*  className={style.listOperationButton} */}
            {/*  onClick={() => { */}
            {/*    const ext = `${record.fileUrl}`.match(/\.[a-z]+$/i); */}
            {/*    if (ext !== null && ['jpg', 'jpeg', 'png'].indexOf(ext[0]?.substring(1)) > -1) { */}
            {/*      store.changeData({ */}
            {/*        certificateRecordPreviewVisible: true, */}
            {/*        certificateRecordPreviewUrl: record.fileUrl, */}
            {/*      }); */}
            {/*    } else { */}
            {/*      window.open(record.fileUrl, '_blank'); */}
            {/*    } */}
            {/*  }} */}
            {/* > */}
            {/*  {t('预览')} */}
            {/* </Button> */}
          </div>
        ),
      },
    ];
    const approveLogColumns = [
      {
        title: t('审批节点'),
        render: 'taskName',
        width: 150,
      },
      {
        title: t('审批人'),
        render: 'assignee',
        width: 150,
      },
      {
        title: t('审核时间'),
        render: 'approvalTime',
        width: 150,
      },
      {
        title: t('审核备注'),
        render: 'approvalRemark',
        width: 150,
      },
      {
        title: t('审核平台'),
        render: 'platform',
        width: 150,
      },
    ];

    return (
      <section className={[styles.handle, 'handleSection'].join(' ')} style={{ paddingBottom: 0 }}>
        <Button
          type="primary"
          disabled={!loading
            || selectedRows.length !== 1
            || selectedRows.some((si) => ![1, 3].includes(si.payOrderStatus) || si.chargeBillType === 2)}
          onClick={() => {
            // 预提交
            store.preCommit({
              isSubmitAfterPreview: true,
              id: selectedRows[0]?.id,
              check: true,
            });
          }}
        >
          {t('原单提报')}
        </Button>
        <Button
          type="primary"
          disabled={!loading
            || selectedRows.length !== 1
            || selectedRows.some((si) => ![1, 3].includes(si.payOrderStatus) || si.chargeBillType === 2)}
          onClick={() => {
            window.location.href = `#/owc-manage/submit-check-sheet/${JSON.stringify({
              enter: 1, // 入口 1报账单(原单提报) 2子报账单(提交报账)
              payOrderDetailIds: [],
              payOrderId: selectedRows[0]?.id,
              userName: userNameSessionStorage.getItem() || '',
              operateType: 'preSubmit',
            })}`;
          }}
        >
          {t('原单提报(新)')}
        </Button>
        <Button
          disabled={!loading || selectedRows.length !== 1
            || selectedRows.some((si) => ![0, 1, 3].includes(Number(si.payOrderStatus)) || si.chargeBillType === 2)}
          type="primary"
          size="default"
          loading={!loading}
          onClick={() => {
            store.changeData({
              invalidModalVisible: true,
              invalidType: 1, // 整单作废
            });
          }}
        >
          {t('整单作废')}
        </Button>
        <Button
          disabled={!loading
            || selectedRows.length !== 1
            || selectedRows[0]?.payOrderStatus !== 6
            || selectedRows[0]?.chargeBillType === 2
            || selectedRows[0]?.systemLink !== 1}
          type="primary"
          size="default"
          loading={!loading}
          onClick={() => {
            store.changeData({
              invalidModalVisible: true,
              invalidType: 2, // 付款作废
            });
          }}
        >
          {t('付款作废')}
        </Button>
        <Button
          disabled={!loading
            || selectedRows.length !== 1
            || selectedRows[0]?.payOrderStatus !== 2
            || selectedRows[0]?.chargeBillType === 2
            || selectedRows[0]?.systemLink !== 1}
          type="primary"
          size="default"
          loading={!loading}
          onClick={() => {
            store.changeData({
              pushFinancialModalVisible: true,
              pushFinancialObj: {
                id: selectedRows[0]?.id,
                applicant: `${userNameSessionStorage.getItem()} ${userNoSessionStorage.getItem() ? `(${userNoSessionStorage.getItem()})` : ''}`, // 申请人
                payOrderNo: selectedRows[0]?.payOrderNo, // 单号
                urgeTime: '', // 期望完成日期
                urgeReason: '', // 申请催办原因
              },
            });
          }}
        >
          {t('催办财务')}
        </Button>
        <Button
          disabled={!loading
            || !selectedRows.length
            || selectedRows.some((si) => si.payOrderStatus !== 9 || si?.systemLink !== 1 || si.chargeBillType === 2)}
          type="primary"
          size="default"
          loading={!loading}
        >
          <Popover.Confirm
            type="warning"
            okType="primary"
            text={{ ok: t('确认'), cancel: t('取消') }}
            onOk={() => { store.merge(); }}
          >
            {t('是否确定合单付款?')}
          </Popover.Confirm>
          {t('合单付款')}
        </Button>

        <Modal
          key={pushFinancialModalVisible}
          visible={pushFinancialModalVisible}
          width={450}
          title={t('催办财务？')}
          onClose={() => {
            store.changeData({
              pushFinancialModalVisible: false,
            });
          }}
          footer={(
            <div>
              <Button
                onClick={() => {
                  store.changeData({
                    pushFinancialModalVisible: false,
                  });
                }}
              >
                {t('取消')}
              </Button>
              <Button
                type="primary"
                disabled={!pushFinancialObj?.urgeReason}
                loading={loading === 0}
                style={{ marginRight: 15 }}
                onClick={() => {
                  Modal.confirm({
                    title: t(''),
                    content: t('是否确认催办财务？'),
                    onOk: () => {
                      store.submitUrgeNew();
                    },
                    text: { ok: t('确认'), cancel: t('取消') },
                  });
                }}
              >
                {t('确定')}
              </Button>
            </div>
          )}
        >
          <Form
            labelWidth={80}
            labelAlign="right"
          >
            <Form.Item label={t('申请人')}>
              <Input disabled width={200} value={pushFinancialObj?.applicant} />
            </Form.Item>
            <Form.Item label={t('单号')}>
              <Input disabled width={200} value={pushFinancialObj?.payOrderNo} />
            </Form.Item>
            <Form.Item required label={t('申请催办原因')}>
              <Textarea
                rows={5}
                maxLength={200}
                placeholder={t('请输入申请催办理由')}
                delay={0}
                value={pushFinancialObj?.urgeReason}
                onChange={(val) => {
                  store.changeData({
                    pushFinancialObj: {
                      ...pushFinancialObj,
                      urgeReason: val,
                    },
                  });
                }}
              />
            </Form.Item>
          </Form>
        </Modal>
        <Modal
          key={invalidModalVisible}
          visible={invalidModalVisible}
          width={450}
          title={t('是否确认作废？')}
          onClose={() => {
            store.changeData({
              invalidModalVisible: false,
            });
          }}
          footer={(
            <div>
              <Button
                onClick={() => {
                  store.changeData({
                    invalidModalVisible: false,
                  });
                }}
              >
                {t('取消')}
              </Button>
              <Button
                type="primary"
                disabled={!invalidRemark}
                loading={loading === 0}
                style={{ marginRight: 15 }}
                onClick={() => {
                  Modal.confirm({
                    title: t(''),
                    content: t('是否确认作废？'),
                    onOk: () => {
                      // 整单作废
                      if (invalidType === 1) {
                        store.invalid();
                      } else if (invalidType === 2) {
                        // 付款作废
                        store.payInvalid();
                      }
                    },
                    text: { ok: t('作废'), cancel: t('取消') },
                  });
                }}
              >
                {t('确定')}
              </Button>
            </div>
          )}
        >
          <Form
            labelWidth={80}
            labelAlign="right"
          >
            <Form.Item required label={t('作废原因（必填）')}>
              <Textarea
                rows={5}
                maxLength={200}
                placeholder={t('请输入作废原因')}
                delay={0}
                value={invalidRemark}
                onChange={(val) => {
                  store.changeData({
                    invalidRemark: val,
                  });
                }}
              />
              <p style={{ padding: '10px 0' }}>
                {t('最大支持录入200字符')}
              </p>
            </Form.Item>
          </Form>
        </Modal>
        <Button
          type="primary"
          disabled={!loading
            || selectedRows.length !== 1
            || selectedRows.some((si) => si.payOrderStatus !== 1 && si.payOrderStatus !== 3)
            || selectedRows.some((si) => si.chargeBillType === 2)}
        >
          <Popover.Confirm
            type="warning"
            okType="primary"
            text={{ ok: t('确认'), cancel: t('取消') }}
            onOk={() => { store.cancel(); }}
          >
            {t('是否确定作废重提?')}
          </Popover.Confirm>
          {t('作废重提')}
        </Button>
        <Button
          type="primary"
          disabled={!loading
            || selectedRows?.length !== 1
            || selectedRows[0]?.payOrderStatus !== 6
            || selectedRows.some((si) => si.chargeBillType === 2)}
          onClick={() => {
            // 根据服务商id获取结算编码下拉
            store.getCostCodeList((selectedRows[0] || {}).bmChargeServiceProviderId);
            // 打开退票付款填充回显数据
            store.changeData({
              refundPaymentVisible: true,
              refundPaymentObj: selectedRows[0] || {},
            });
          }}
        >
          {t('重新付款')}
        </Button>
        <Button
          type="primary"
          disabled={!loading}
          onClick={() => {
            store.exportData();
          }}
        >
          {t('导出')}
        </Button>

        {/* 查看加密信息 */}
        <div style={{ marginRight: 10, float: 'inline-end' }}>
          <span style={{ verticalAlign: 'middle', marginRight: 8 }}>{t('查看加密信息')}</span>
          <Switch
            value={Boolean(showSensitiveData)}
            onChange={(val) => {
              store.changeData({
                showSensitiveData: val,
              });
              store.search();
            }}
          />
        </div>

        <Radio.Group
          data-bind="filterStatusCode"
          button
          onChange={(val) => {
            // let filterList = [];
            // // 合并二审...六审到审批中
            // if (val === 1) {
            //   // 审批中 = 审批中+二审...六审之和
            //   filterList = tmpList.filter((li) => ([1, 22, 23, 24, 25, 26].indexOf(li.payOrderStatus) > -1));
            // } else {
            //   filterList = tmpList.filter((li) => (li.payOrderStatus === val));
            // }
            // store.changeData({
            //   filterStatusCode: val,
            //   list: filterList,
            //   pageInfo: {
            //     ...pageInfo,
            //     pageNum: 1, // 页码
            //     count: filterList.length,
            //   },
            //   selectedRows: [],
            // });

            store.changeLimitData({
              payOrderStatus: val,
            }).then(() => {
              store.search({
                isOperateSubButton: true,
              });
            });
          }}
        >
          {(payOrderStatusList || []).map((d) => (
            <Radio key={d.dictCode} htmlValue={d.dictCode}>
              {d.dictNameZh}
              (
              {allStatusKindCount[d.dictCode] || 0}
              )
            </Radio>
          ))}
        </Radio.Group>
        {/* 报账单详情信息弹框 */}
        {detailInfoVisible && (
          <DetailPreviewSubmit
            title={t('报账单详情')}
            loading={loading}
            confirmSubmit={(params) => { store.confirmCommit(params); }}
            onClose={() => {
              const newDetailInfo = {
                ...detailInfo,
                ...defaultShowInfoObj,
              };
              store.changeData({
                detailInfoVisible: false,
                showPreCommitSensitiveData: false,
                PreCommitSensitiveDataOrderId: null,
                isShowServiceProviderSensitiveData: false,
                detailInfo: newDetailInfo,
              });
            }}
            isSubmitAfterPreview={isSubmitAfterPreview}
            detailInfo={detailInfo}
            currencyTypeNameList={currencyTypeNameList}
            costExchangeRateTypeList={costExchangeRateTypeList}
            costExchangeRateList={costExchangeRateList}
            showSensitiveData={showPreCommitSensitiveData}
            handleShowSensitiveData={(val) => {
              if (val) {
                store.handleShowSensitiveData();
              } else {
                store.preCommit({
                  isSubmitAfterPreview,
                  id: PreCommitSensitiveDataOrderId || '',
                  check: true,
                });
              }
            }}
            isShowSensitiveData={isShowServiceProviderSensitiveData}
            handleGetServiceProviderSensitiveData={() => { // 获取服务商敏感数据
              store.handleChangeServiceProviderSensitiveSwitch();
            }}
          />
        )}
        {/* 主报账单补传附件 */}
        <Modal
          visible={importModalVisible}
          maskCloseAble={null}
          destroy
          title={t('文件上传')}
          onClose={() => {
            store.changeData({
              importModalVisible: false,
            });
          }}
          footer={[
            <Button
              key="cancel"
              onClick={() => {
                store.changeData({
                  importModalVisible: false,
                });
              }}
            >
              {t('取消')}
            </Button>,
            <Button
              type="primary"
              loading={!loading}
              onClick={() => {
                store.updateFiles();
              }}
            >
              {t('确认更新')}
            </Button>,
          ]}
        >
          <div style={{ padding: '20px 0', display: 'flex' }}>
            <span style={{ display: 'inline-block', marginRight: 20 }}>
              {t('选择文件')}
              :
            </span>
            <UploadPlus
              accept=".xls,.xlsx,.pdf,.docx,.doc,.zip,.jpg,.png,.txt"
              limit={20}
              autoUpload
              action={uploadFileURL}
              fileList={fileList}
              autoUploadKeyName="file"
              filePathKeyName="fileUrl"
              // data={{
              //   is_use_origin_name: true,
              // }}
              onFailUpload={async (_, info) => Message.error(info)}
              onDelete={async (removeItem) => {
                const newFiles = fileList.filter((file) => file.fileUrl !== removeItem.fileUrl);
                store.changeData({
                  fileList: newFiles,
                });
              }}
              onSuccessUpload={async ({ file, info }) => {
                const fullName = file.name;
                const filename = (fullName?.split('.') || [])[0];
                if (!fullName.length) {
                  Message.error(t('文件名和扩展名不能同时为空'));
                  return false;
                }
                // 123..ext
                if (/\s/.test(filename) || /(\.|。){2}/.test(fullName)) {
                  Message.error(t('文件名不能有空字符,文件名尾不能为“.”号'));
                  return false;
                }
                if (/[#+?？:：*^<>|｜-]/g.test(filename) || fullName.match(/\./g)?.length >= 2) {
                  Message.error(t('文件名中不能包含“#、:、 * 、^、 ?、 <、 >、 | 、-、 +、.”中任意字符'));
                  return false;
                }
                if (fullName.length > 255) {
                  Message.error(t('文件名包含扩展名长度不能大于{}个字符', 255));
                  return false;
                }
                const fileItem = {
                  name: file.name,
                  fileName: file.name,
                  fileUrl: info.image_url,
                };
                store.changeData({
                  fileList: [...fileList, { id: Date.now(), ...fileItem }],
                });
              }}
              renderResult={(f) => (
                <a
                  key={f.fileUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  href={f.fileUrl}
                  download
                >
                  {f?.fileName || t('附件')}
                </a>
              )}
            />
          </div>
        </Modal>
        <Modal
          visible={certificateRecordVisible}
          maskCloseAble={null}
          destroy
          width={600}
          bodyStyle={{ maxHeight: '475px', overflow: 'auto' }}
          title={t('电子回单记录')}
          onClose={() => {
            store.changeData({
              certificateRecordVisible: false,
            });
          }}
          footer={null}
        >
          <Table
            keygen="id"
            bordered
            columns={certificateRecordColumns}
            data={certificateRecordData}
            pagination={false}
            fixed="both"
            style={{ maxHeight: 400 }}
          />
        </Modal>
        <Modal
          visible={certificateRecordVisible}
          maskCloseAble={null}
          destroy
          esc={false}
          width={600}
          bodyStyle={{ maxHeight: '475px', overflow: 'auto' }}
          title={t('付款计划单号', paymentRuleNo)}
          onClose={() => {
            store.changeData({
              certificateRecordVisible: false,
            });
          }}
          footer={null}
        >
          <Table
            keygen="id"
            bordered
            columns={certificateRecordColumns}
            data={certificateRecordData}
            pagination={false}
            fixed="both"
            style={{ maxHeight: 400 }}
          />
        </Modal>
        <div>
          {certificateRecordPreviewVisible ? (
            <Gallery
              dataSource={
                      {
                        thumb: [
                          certificateRecordPreviewUrl,
                        ],
                        origin: [
                          certificateRecordPreviewUrl,
                        ],
                      }
                    }
              index={0}
              onClose={() => {
                store.changeData({
                  certificateRecordPreviewVisible: false,
                });
              }}
              zoomAble
              zoomRatio={3}
            />
          ) : null}
        </div>
        {/* 退票付款弹框 */}
        <Modal
          key={refundPaymentVisible}
          visible={refundPaymentVisible}
          width={500}
          maskCloseAble={false}
          title={t('重新付款')}
          onClose={() => store.changeData({ refundPaymentVisible: false })}
          footer={(
            <div>
              <Button onClick={() => store.changeData({ refundPaymentVisible: false })}>{t('关闭')}</Button>
              <Button
                type="primary"
                disabled={!loading || !refundPaymentObj.bmServiceProviderCostCodeId}
              >
                <Popover.Confirm
                  type="warning"
                  okType="primary"
                  text={{ ok: t('确认'), cancel: t('取消') }}
                  onOk={() => { store.refundPaymentModify(refundPaymentObj); }}
                >
                  {t('是否确定修改?')}
                </Popover.Confirm>
                {t('确认')}
              </Button>
            </div>
              )}
        >
          <Form
            labelWidth={100}
            labelAlign="right"
            onChange={(value) => {
              store.changeData({
                refundPaymentObj: value,
              });
            }}
            value={refundPaymentObj}
          >
            <Form.Item label={t('报账单号')}>
              <Input disabled width={250} name="payOrderNo" />
            </Form.Item>
            <Form.Item required label={t('结算编码')}>
              <Select
                autoAdapt
                label={t('结算编码')}
                name="bmServiceProviderCostCodeId"
                keygen="id"
                format="id"
                placeholder={t('全部')}
                data={costCodeList}
                clearable
                width={250}
                rules={[rules.required]}
                renderItem="serviceProviderCostCode"
                renderUnmatched={(r) => r.serviceProviderCostCode || <span style={{ color: '#bbb' }}>{t('全部')}</span>}
                onFilter={(text) => (d) => d.serviceProviderCostCode.toLowerCase().indexOf(text.toLowerCase()) >= 0}
              />
            </Form.Item>
          </Form>
        </Modal>
      </section>
    );
  }
}

Handle.propTypes = {
  loading: PropTypes.number,
  selectedRows: PropTypes.arrayOf(PropTypes.shape),
  detailInfo: PropTypes.shape(),
  detailInfoVisible: PropTypes.bool,
  currencyTypeNameList: PropTypes.arrayOf(PropTypes.shape),
  costExchangeRateTypeList: PropTypes.arrayOf(PropTypes.shape),
  costExchangeRateList: PropTypes.arrayOf(PropTypes.shape),
  importModalVisible: PropTypes.bool,
  fileList: PropTypes.arrayOf(PropTypes.shape),
  isSubmitAfterPreview: PropTypes.bool,
  refundPaymentVisible: PropTypes.bool,
  refundPaymentObj: PropTypes.shape(),
  costCodeList: PropTypes.arrayOf(PropTypes.shape),
  // pageInfo: PropTypes.shape(),
  certificateRecordVisible: PropTypes.bool,
  certificateRecordData: PropTypes.arrayOf(PropTypes.shape),
  certificateRecordPreviewVisible: PropTypes.bool,
  certificateRecordPreviewUrl: PropTypes.string,
  // tmpList: PropTypes.arrayOf(PropTypes.shape()),
  payOrderStatusList: PropTypes.arrayOf(PropTypes.shape()),
  paymentRuleNo: PropTypes.string,
  showSensitiveData: PropTypes.bool,
  showPreCommitSensitiveData: PropTypes.bool,
  PreCommitSensitiveDataOrderId: PropTypes.number,
  isShowServiceProviderSensitiveData: PropTypes.bool,
  allStatusKindCount: PropTypes.shape(),
  invalidModalVisible: PropTypes.bool,
  invalidRemark: PropTypes.string,
  invalidType: PropTypes.number,
  pushFinancialModalVisible: PropTypes.bool,
  pushFinancialObj: PropTypes.shape(),
};
export default Handle;
