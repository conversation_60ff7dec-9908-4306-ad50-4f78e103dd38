import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import {
  Input, Select,
} from 'shineout';
import SearchAreaContainer from '@search-queries/searchArea-container';
import DateRangePicker from '@shein-components/dateRangePicker2';
import FilterSearchSelect from '@src/component/public-component/select/filter-search-select';
import store, { defaultLimit } from '../reducers';

// const rule = Rule({
//   billDayRange: {
//     func: (val, formData, callback) => {
//       // 账期/创建时间不可都为空，输入账单号、业务号除外，最长账期32天
//       if (
//         !formData.payOrderNo
//       ) {
//         if (!formData.createTimeStart && !formData.billDayBegin) {
//           callback(new Error(t('账期/创建时间不可都为空')));
//         }
//         if (moment(formData.billDayEnd)
//           .diff(moment(formData.billDayBegin), 'days', true) > 32) {
//           callback(new Error(t('账期开始时间和结束时间不能超过{}天', 32)));
//         }
//       }
//       callback(true);
//     },
//   },
//   createTimeRange: {
//     func: (val, formData, callback) => {
//       // 时间限制32天
//       // 账期/创建时间不可都为空，输入账单号除外，最长账期32天
//       if (
//         !formData.payOrderNo
//       ) {
//         if (!formData.createTimeStart && !formData.billDayBegin) {
//           callback(new Error(t('账期/创建时间不可都为空')));
//         }
//         // 时间限制32天
//         if (moment(formData.createTimeEnd)
//           .diff(moment(formData.createTimeStart), 'days', true) > 32) {
//           callback(new Error(t('创建开始时间和结束时间不能超过{}天', 32)));
//         }
//       }
//       callback(true);
//     },
//   },
// });

class Header extends Component {
  render() {
    const {
      loading,
      limit,
      warehouseList, // 仓库列表
      subWarehouseList,
      bmServiceProviderList, // 服务商列表
      formRef, // 校验条件
      payOrderStatusList, // 状态下拉
      billTypeList, // 账单类型
      businessCategoryOptions, // 业务大类下拉
      chargeBillTypeOptions, // 账单类型下拉
    } = this.props;

    return (
      <section>
        <SearchAreaContainer
          labelStyle={{ width: 90 }} // 统一修改label宽度，默认60
          value={limit}
          clearUndefined={false}
          searching={!loading}
          onSearch={() => {
            // 点搜索按钮，应该将页码重置为1
            store.handlePaginationChange({ pageNum: 1 });
          }}
          onChange={(val) => {
            // 业务需求隐藏表单就设置默认值
            store.changeLimitData({ ...defaultLimit, ...val });
            // 清空校验信息
            if (formRef && formRef.validate) formRef.validate();
          }}
          onClear={() => store.clearLimitData()}
          formRef={(f) => {
            store.changeData({
              formRef: f,
            });
          }}
          alwaysVisible={[t('账期'), t('创建时间')]}
        >
          <Select
            autoAdapt
            label={t('服务商简称')}
            name="bmChargeServiceProviderId"
            keygen="id"
            format="id"
            placeholder={t('全部')}
            data={bmServiceProviderList}
            clearable
            renderItem="serviceProviderAbbreviation"
            renderUnmatched={(r) => r.serviceProviderAbbreviation || <span style={{ color: '#bbb' }}>{t('全部')}</span>}
            onFilter={(text) => (d) => d.serviceProviderAbbreviation.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
          <Select
            autoAdapt
            label={t('仓库')}
            name="warehouseId"
            data={warehouseList}
            keygen="intId"
            format="intId"
            renderItem="name"
            onFilter={(text) => (d) => d.name.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
            placeholder={t('全部')}
          />
          <Select
            autoAdapt
            label={t('子仓')}
            placeholder={t('请选择')}
            keygen="subWarehouseId"
            format="subWarehouseId"
            renderItem="subWarehouseName"
            onFilter={(text) => (d) => d.subWarehouseName.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            name="subWarehouseId"
            data={subWarehouseList}
            clearable
          />
          <FilterSearchSelect
            label={t('创建人')}
            name="creator"
            required
            placeholder={t('请输入')}
            customFormat="userName"
            clearable
          />
          <Input
            label={t('报账单号')}
            name="payOrderNo"
            clearable
          />
          <Select
            autoAdapt
            label={t('账期类型')}
            name="billType"
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            data={billTypeList}
            clearable
            placeholder={t('请选择')}
            renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
          <Select
            autoAdapt
            label={t('业务大类')}
            name="businessCategory"
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            data={businessCategoryOptions}
            clearable
            placeholder={t('请选择')}
            renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
          <Select
            autoAdapt
            label={t('账单类型')}
            name="chargeBillType"
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            data={chargeBillTypeOptions}
            clearable
            placeholder={t('请选择')}
            renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('请选择')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
          <DateRangePicker
            placeholder={[t('开始时间'), t('结束时间')]}
            type="datetime"
            inputable
            format="yyyy-MM-dd HH:mm:ss"
            name={['billDayBegin', 'billDayEnd']}
            defaultTime={['00:00:00', '23:59:59']}
            label={t('账期')}
            span={2}
            required
            clearable
          />
          <DateRangePicker
            placeholder={[t('开始时间'), t('结束时间')]}
            type="datetime"
            inputable
            format="yyyy-MM-dd HH:mm:ss"
            name={['createTimeBegin', 'createTimeEnd']}
            defaultTime={['00:00:00', '23:59:59']}
            defaultValue={[Date.now() - 100000000, Date.now()]}
            label={t('创建时间')}
                // rules={[rule.createTimeRange()]}
            span={2}
            required
            clearable
          />
          <Select
            autoAdapt
            label={t('状态')}
            name="payOrderStatus"
            data={payOrderStatusList}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('全部')}
            clearable
            renderItem="dictNameZh"
            renderUnmatched={(r) => r.dictNameZh || <span style={{ color: '#bbb' }}>{t('全部')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
        </SearchAreaContainer>
      </section>
    );
  }
}

Header.propTypes = {
  loading: PropTypes.number.isRequired,
  limit: PropTypes.shape(),
  warehouseList: PropTypes.arrayOf(PropTypes.shape()),
  subWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
  bmServiceProviderList: PropTypes.arrayOf(PropTypes.shape()),
  formRef: PropTypes.shape(),
  payOrderStatusList: PropTypes.arrayOf(PropTypes.shape()),
  billTypeList: PropTypes.arrayOf(PropTypes.shape()),
  businessCategoryOptions: PropTypes.arrayOf(PropTypes.shape),
  chargeBillTypeOptions: PropTypes.arrayOf(PropTypes.shape),
};
export default Header;
