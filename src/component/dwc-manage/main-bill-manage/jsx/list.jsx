import React from 'react';
import PropTypes from 'prop-types';
import SearchAreaTable from '@search-queries/tableSorter-container';
import styles from '@src/component/style.less';
import { t } from '@shein-bbl/react';
import {
  Button, Tag,
} from 'shineout';
import { userNameSessionStorage } from '@src/lib/storage-new';
import OperationModal from '@public-component/modal/operation-modal';
import { handleTablePros } from '@src/lib/deal-func';
import { trimDecimalPlaces } from '@src/lib/validate';
import { formatPrice2Thousands } from '@src/lib/number-format';
import moment from 'moment';
import SensitiveTable from '@public-component/search-queries/SensitiveTable';
import store from '../reducers';
import style from '../style.less';

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      selectedRows,
      pageInfo,
      operationTitle, // 日志弹框标题
      recordId, // 行id
      operateCode, // 查询日志编码
      recordVisible, // 日志弹框
      showSensitiveData,
    } = this.props;
    const columns = [
      {
        title: t('审批单号'),
        width: 200,
        render: 'approvalCode',
      },
      {
        title: t('报账单号'),
        width: 200,
        render: (record) => (
          <Button
            type="link"
            className="customLinkStyle"
            onClick={() => {
              // store.preCommit({
              //   isSubmitAfterPreview: false,
              //   id: record.id,
              //   check: false,
              // });
              window.location.href = `#/owc-manage/submit-check-sheet/${JSON.stringify({
                enter: 1, // 入口 1报账单(原单提报) 2子报账单(提交报账)
                payOrderDetailIds: [],
                payOrderId: record?.id,
                userName: userNameSessionStorage.getItem() || '',
                operateType: 'preview', // 预览
              })}`;
            }}
          >
            {record.payOrderNo}
          </Button>
        ),
      },
      {
        title: t('状态'),
        width: 120,
        render: 'payOrderStatusName',
      },
      {
        title: t('服务商简称'),
        width: 160,
        sensitive: true, // 添加属性
        render: 'serviceProviderAbbreviation',
      },
      {
        title: t('仓库'),
        width: 140,
        render: 'warehouseName',
      },
      {
        title: t('子仓'),
        width: 140,
        render: 'subWarehouseName',
      },
      {
        title: t('业务大类'),
        width: 140,
        render: 'businessCategoryName',
      },
      {
        title: t('账单类型'),
        width: 140,
        render: 'chargeBillTypeName',
      },
      {
        title: t('预计财务报账金额'),
        width: 160,
        render: 'costPayPss',
        needThousandsPoint: true,
      },
      {
        title: t('税率%'),
        width: 120,
        render: 'taxRate',
      },
      {
        title: t('预计财务报账税额'),
        width: 160,
        render: 'costRatePss',
        needThousandsPoint: true,
      },
      {
        title: t('预计财务报账不含税额'),
        width: 170,
        render: 'costPayNoTaxPss',
        needThousandsPoint: true,
      },
      {
        title: t('推送财务应付总额'),
        width: 140,
        render: 'costPayablePss',
        needThousandsPoint: true,
      },
      {
        title: t('尾差'),
        width: 120,
        render: 'costDiff',
        needThousandsPoint: true,
      },
      {
        title: t('报账金额'),
        width: 160,
        render: 'costPay',
        needThousandsPoint: true,
      },
      {
        title: t('账单币种/合同币种'),
        width: 140,
        render: 'currencyPay',
      },
      {
        title: t('补扣款金额'),
        width: 140,
        render: 'costDeduct',
        needThousandsPoint: true,
      },
      {
        title: t('账期'),
        width: 210,
        render: (r) => (
          <span>
            {moment(r.billDayBegin).format('YYYY-MM-DD')}
            {r.billDayEnd ? `~${moment(r.billDayEnd).format('YYYY-MM-DD')}` : ''}
          </span>
        ),
      },
      {
        title: t('账期类型'),
        width: 140,
        render: 'accountPeriodTypeName',
      },
      {
        title: t('付款主体'),
        width: 240,
        render: 'bmPayMainName',
      },
      {
        title: t('付款单号'),
        width: 140,
        render: 'paymentPayNumber',
      },
      {
        title: t('银行付款金额'),
        width: 160,
        render: 'bankPayMoney',
      },
      {
        title: t('银行付款币种'),
        width: 150,
        render: 'bankCurCode',
      },
      {
        title: t('银行付款时间'),
        width: 200,
        render: 'bankPayTime',
      },
      {
        title: t('银行付款流水'),
        width: 160,
        render: 'xxx',
      },
      {
        title: t('银行付款凭证'),
        width: 200,
        render: (r) => (
          <Button
            size="small"
            text
            type="primary"
            className={style.listOperationButton}
            onClick={() => {
              const { createTime } = r;
              (r.electronicSignFile || []).forEach((item) => { item.createTime = createTime; });
              store.changeData({
                certificateRecordVisible: true,
                certificateRecordData: r.electronicSignFile || [],
                paymentRuleNo: r.paymentRuleNo,
              });
            }}
          >
            {t('凭证记录')}
          </Button>
        ),
      },
      {
        title: t('退票信息'),
        width: 200,
        render: (record) => (
          <>
            {(record.refundFile || []).map((fi) => (
              <Tag
                style={{ margin: '4px 4px 0 0 ' }}
                key={fi.id}
              >
                {fi.fileName}
              </Tag>
            ))}
          </>
        ),
      },
      {
        title: t('报账备注'),
        width: 200,
        render: 'payRemark',
      },
      {
        title: t('先款后票'),
        width: 140,
        render: 'payBeforeInvoice',
      },
      {
        title: t('创建人'),
        width: 200,
        render: 'creator',
      },
      {
        title: t('报账时间'),
        width: 200,
        render: 'createTime',
      },
      {
        title: t('USD结算金额'),
        width: 120,
        render: 'costAmountByUsd',
        needThousandsPoint: true,
      },
      {
        title: t('CNY结算金额'),
        width: 120,
        render: 'costAmountByCny',
        needThousandsPoint: true,
      },
      {
        title: t('作废原因'),
        width: 220,
        render: 'cancelRemark',
      },
      {
        title: t('业务特殊审批节点'),
        width: 200,
        render: 'lpmpApprovalFlagDesc',
      },
      {
        title: t('财务审批平台'),
        width: 160,
        render: 'financeApprovalPlatform',
      },
      {
        title: t('当前审批人'),
        width: 160,
        render: 'currentApproveUser',
      },
      {
        title: t('操作'),
        width: 200,
        render: (record) => (
          <div>
            <Button
              type="link"
              className="customLinkStyle"
              onClick={() => {
                window.location.href = `#/owc-manage/submit-check-sheet/${JSON.stringify({
                  enter: 1, // 入口 1报账单(原单提报) 2子报账单(提交报账)
                  payOrderDetailIds: [],
                  payOrderId: record?.id,
                  userName: userNameSessionStorage.getItem() || '',
                  operateType: 'edit', // 编辑
                })}`;
              }}
            >
              {t('编辑')}
            </Button>
            <Button
              size="small"
              text
              type="primary"
              className={style.listOperationButton}
              onClick={() => store.changeData({
                recordVisible: true,
                recordId: record.id,
                operateCode: 'WBMS_CHARGE_PAY_ORDER_APPROVE',
                operationTitle: t('审批日志'),
              })}
            >
              {t('审批日志')}
            </Button>
            <Button
              size="small"
              text
              type="primary"
              className={style.listOperationButton}
              onClick={() => store.changeData({
                recordVisible: true,
                recordId: record.id,
                operateCode: 'WBMS_CHARGE_PAY_ORDER_LOG',
                operationTitle: t('操作日志'),
              })}
            >
              {t('操作日志')}
            </Button>
          </div>
        ),
      },
      {
        title: t('附件'),
        fixed: 'right',
        width: 160,
        render: (record) => (
          <>
            {(record.payOrderFile || []).map((fi) => (
              <Tag
                style={{ margin: '4px 4px 0 0 ' }}
                key={fi.id}
              >
                {fi.fileName}
              </Tag>
            ))}
          </>
        ),
      },
    ];
    // 展示勾选行的统计信息 如果选择的多行币种不一样就不展示
    // const getTickedRowsInfoDisplay = () => {
    //   let tickedRowsInfoDisplay = ''; // 勾选行展示统计信息
    //   const tickedRowsInfo = selectedRows.reduce((checkedInfo, cur) => {
    //     checkedInfo.totalCost += cur.cost || 0;
    //     checkedInfo.currencyTypeName.push(cur.currencyTypeName);
    //     checkedInfo.totalDiffCost += cur.diffCost || 0;
    //     checkedInfo.verifyDetailNums += cur.verifyNum || 0;
    //     return checkedInfo;
    //   }, {
    //     totalCost: 0, currencyTypeName: [], totalDiffCost: 0, verifyDetailNums: 0,
    //   });
    //   if (Array.from(new Set(tickedRowsInfo.currencyTypeName)).length === 1) {
    //     tickedRowsInfoDisplay = `${t('已选择')}${selectedRows.length}${t('条明细，结算金额：')}${Number(tickedRowsInfo.totalCost || 0).toFixed(2)}/${tickedRowsInfo.currencyTypeName[0]}；${t('差异金额')}：${Number(tickedRowsInfo.totalDiffCost || 0).toFixed(2)}；${t('核对明细数')} ${tickedRowsInfo.verifyDetailNums} ${t('条')}；  `;
    //   }
    //   return tickedRowsInfoDisplay;
    // };

    // 展示勾选行的统计信息
    const getTickedRowsInfoDisplay = () => {
      // 如果选择的多行币种不一样就不展示
      if (selectedRows.length === 0) return '';

      // 不同币种勾选展示为空
      if (selectedRows.find((x) => x.currencyPay !== selectedRows[0].currencyPay)) {
        return '';
      }

      const sumCostAmount = showSensitiveData ? selectedRows.reduce((pre, cur) => pre + Number(cur.costPay || 0), 0) : '****';
      const renderCostAmount = showSensitiveData ? formatPrice2Thousands(trimDecimalPlaces(Number(sumCostAmount || 0).toFixed(8))) : '****';

      return t(
        '已选择{}条，报账金额：{}/{}',
        selectedRows.length,
        renderCostAmount,
        selectedRows[0].currencyPay,
      );
    };

    return (
      <section className={styles.tableSection}>
        <SearchAreaTable>
          <SensitiveTable
            {...handleTablePros(columns)}
            style={{ height: '100%' }}
            rowClassName={() => styles.borderInner}
            bordered
            fixed="both"
            loading={!loading}
            data={list}
            columns={columns}
            keygen="id"
            empty={t('暂无数据')}
            size="small"
            width={columns.reduce((pre, current) => pre + (current.width || 100), 50)}
            value={selectedRows}
            onRowSelect={(rows) => {
              store.changeData({ selectedRows: rows });
            }}
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              className: styles.pagination,
              // layout: [({ total }) => `${t('共')} ${total} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              layout: [({ total }) => `${getTickedRowsInfoDisplay()}   ${t('共')} ${total} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
          />
        </SearchAreaTable>
        {/* 操作记录 */}
        <OperationModal
          title={operationTitle}
          visible={recordVisible}
          param={{
            operateId: recordId,
            operateCode,
          }}
          onCancel={() => store.changeData({ recordVisible: false })}
        />
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number.isRequired,
  list: PropTypes.arrayOf(PropTypes.shape).isRequired,
  pageInfo: PropTypes.shape().isRequired,
  selectedRows: PropTypes.arrayOf(PropTypes.shape),
  operationTitle: PropTypes.string,
  recordId: PropTypes.number,
  operateCode: PropTypes.string,
  recordVisible: PropTypes.bool,
  showSensitiveData: PropTypes.bool,
};

export default List;
