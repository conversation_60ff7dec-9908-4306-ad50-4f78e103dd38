import { markStatus } from 'rrc-loader-helper';
import moment from 'moment';
import { Message, Modal } from 'shineout';
import { getSize } from '@src/middlewares/pagesize';
import { t } from '@shein-bbl/react';
import {
  dictSelect,
} from '@src/server/basic/dictionary';
import { clearEmpty, paramTrim } from '@src/lib/deal-func';
import { apolloFormatObj } from '@src/lib/dealFunc';
import { userNameSessionStorage, formatNameSessionStorage } from '@src/lib/storage-new';
import { getApolloConfigAPI } from '@src/server/common/common';
import {
  getWarehouseAPI, getSubWarehouseAPI, getServiceProviderIdAPI,
} from '@src/server/wbms/server';
import getServiceProviderAPI from '@src/server/common/dwc-common';
import {
  searchAPI, invalidAPI, preCommitAPI, confirmCommitAPI, cancelAPI, updateFilesAPI, getCostCodeListAPI, rePayAPI, exportListAPI,
  searchSensitizeAPI,
  payInvalidAPI,
  submitUrgeNewAPI,
  mergeAPI,
  getApproveLogAPI,
} from './server';

export const defaultLimit = {
  bmChargeServiceProviderId: '', // 服务商简称
  warehouseId: '', // 仓库
  subWarehouseId: '', // 子仓
  creator: userNameSessionStorage.getItem() || '', // 创建人
  payOrderNo: '', // 报账单号
  billType: '', // 账期类型
  billDayBegin: moment().subtract(1, 'month').startOf('month').format('YYYY-MM-DD 00:00:00'), // 账期开始时间
  billDayEnd: moment().subtract(1, 'month').endOf('month').format('YYYY-MM-DD 23:59:59'), // 账期结束时间
  createTimeBegin: '', // 创建开始时间
  createTimeEnd: '', // 创建结束时间
  payOrderStatus: '', // 状态 默认审批中
  fileStatus: '', // 附件状态
  currencyTypeName: '', // 币种
  businessCategory: '', // 业务大类
  chargeBillType: '', // 账单类型
};

// 服务商数据加密
export const defaultShowInfoObj = {
  collectionBankAccount: '******',
  collectionBankAccountName: '******',
  collectionBankCode: '******',
  collectionBankOriginName: '******',
  serviceProviderCostCode: '******',
  bmPayMainName: '******',
};

const defaultState = {
  formRef: {}, // 绑定 form 的引用, 可以调用某些 form 的方法  validate(校验表单) 具体可以查看 https://shineout.biz.sheincorp.cn/1.6.x/cn/components/Form
  loading: 0, // 0 loading, 1 load success, 2 load fail
  ready: true,
  selectedRows: [],
  limit: defaultLimit,
  pageInfo: {
    pageNum: 1, // 页码
    count: 0, // 表格总条数
    pageSize: getSize(),
    pageSizeList: [20, 50, 100, 1000, 2000], // 表格页显示条数
  },
  warehouseList: [], // 仓库列表
  subWarehouseList: [], // 子仓列表
  bmServiceProviderList: [], // 服务商列表
  list: [],
  tmpList: [], // 暂存列表数据
  detailInfo: {}, // 预提交返回的报账单详情信息
  detailInfoVisible: false, // 预提交成功后弹出报账单详情框
  payOrderStatusList: [], // 状态下拉
  operationTitle: '', // 日志弹框标题
  recordId: null, // 行id
  operateCode: '', // 查询日志编码
  recordVisible: false, // 日志弹框
  filterStatusCode: '', // 点击tab进行前端筛选
  billTypeList: [], // 账单类型
  importModalVisible: false, // 补传附件弹框
  fileList: [], // 补传附件
  fileListTmp: [], // 已上传附件列表缓存 fileList不在fileListTmp中则上传更新 fileListTmp不在fileList中则进行删除更新
  isSubmitAfterPreview: false, // 是否是 true 查看详情后填写表单进行提交 false 仅仅是预览报账单详情
  urgeVisible: false, // 催办弹框是否显示
  urgeObj: {
    urgeTime: '',
    urgeReason: '',
    applicant: `${formatNameSessionStorage.getItem()}`,
  }, // 提交催办的信息
  urgeClicked: [], // 区分点勾选还是单行点击
  certificateRecordVisible: false, // 凭证记录弹框
  certificateRecordData: [], // 凭证记录列表数据
  paymentRuleNo: '', // 付款计划单号
  certificateRecordPreviewVisible: false, // 凭证记录预览弹框
  certificateRecordPreviewUrl: '', // 凭证记录预览图片地址
  refundPaymentVisible: false, // 退票付款弹框
  refundPaymentObj: {},
  costCodeList: [], // 结算编码下拉
  showSensitiveData: false, // 查看加密信息
  showPreCommitSensitiveData: false, // 预提交查看加密信息
  PreCommitSensitiveDataOrderId: null, // 预提交查看加密信息的订单id
  isShowServiceProviderSensitiveData: false, // 是否显示服务商敏感数据
  allStatusKindCount: {}, // 后端返回状态统计映射
  costInfoShowType: 0, // 1 使用新的结算编码展示弹框 0 默认使用老的结算编码弹框
  invalidModalVisible: false, // 整单作废弹框是否显示
  invalidRemark: '', // 整单作废原因 & 付款作废原因
  invalidType: 1, // 1 整单作废 2 付款作废
  pushFinancialModalVisible: false, // 催办财务弹框是否显示
  pushFinancialObj: {
    id: '',
    applicant: '', // 申请人
    payOrderNo: '', // 单号
    urgeTime: '', // 期望完成日期
    urgeReason: '', // 申请催办原因
  },
  businessCategoryOptions: [], // 业务大类下拉
  chargeBillTypeOptions: [], // 账单类型下拉
};

export default {
  state: defaultState,
  * init() {
    markStatus('loading');
    const [warehouseList, subWareRes] = yield Promise.all([
      getWarehouseAPI({}),
      getSubWarehouseAPI({}),
    ]);
    if (warehouseList.code === '0' && subWareRes.code === '0') {
      yield this.changeData({
        warehouseList: warehouseList.info,
        subWarehouseList: subWareRes.info, // 子仓
      });
    } else {
      Modal.error({ title: warehouseList.msg });
    }
    yield this.getApolloConfig();
    yield this.getSelectData();

    // 默认查询
    yield this.search();

    yield this.handleServiceProviderFilter();
  },
  * getApolloConfig() {
    markStatus('loading');
    const apolloConfigRes = yield getApolloConfigAPI({ params: ['WBMS_COST_INFO_SHOW_TYPE'] });
    const apolloConfigFormatRes = apolloFormatObj(apolloConfigRes?.info || {});
    if (apolloConfigRes?.code === '0') {
      yield this.changeData({
        costInfoShowType: apolloConfigFormatRes?.WBMS_COST_INFO_SHOW_TYPE,
      });
    }
  },
  * getSelectData() {
    const { costInfoShowType } = yield '';

    const billTypeKey = costInfoShowType ? 'WBMS_STANDARD_ACCOUNT_PERIOD_TYPE' : 'ACCOUNT_PERIOD_TYPE';

    markStatus('loading');
    const data = yield dictSelect({
      catCode: [
        billTypeKey,
        'COMPANY_BODY',
        'COST_EXCHANGE_RATE_TYPE',
        'COST_EXCHANGE_RATE_VALUE',
        'WBMS_FILE_STATUS',
        'CHARGE_PAY_ORDER_STATUS',
        'WBMS_COST_ITEM_BUSINESS_TYPE',
        'CHARGE_BILL_TYPE_NAME',
      ],
    });

    if (data.code === '0') {
      yield this.changeData({
        // 账单类型
        billTypeList: data.info.data.find((x) => x.catCode === billTypeKey).dictListRsps,
        // 付款主体
        payMainIdList: data.info.data.find((item) => item.catCode === 'COMPANY_BODY').dictListRsps,
        // 付款汇率方式
        costExchangeRateTypeList: data.info.data.find((item) => item.catCode === 'COST_EXCHANGE_RATE_TYPE').dictListRsps,
        // 汇率值下拉
        costExchangeRateList: data.info.data.find((item) => item.catCode === 'COST_EXCHANGE_RATE_VALUE').dictListRsps,
        // 报账单状态
        payOrderStatusList: data.info.data.find((item) => item.catCode === 'CHARGE_PAY_ORDER_STATUS').dictListRsps,
        // 业务大类下拉
        businessCategoryOptions: data.info.data.find((item) => item.catCode === 'WBMS_COST_ITEM_BUSINESS_TYPE').dictListRsps,
        // 账单类型下拉
        chargeBillTypeOptions: data.info.data.find((item) => item.catCode === 'CHARGE_BILL_TYPE_NAME').dictListRsps,
      });
    } else {
      Modal.error({ title: data.msg });
    }
  },
  // 服务商列表下拉模糊搜索
  * handleServiceProviderFilter() {
    const { code, info, msg } = yield getServiceProviderIdAPI({});
    if (code === '0') {
      yield this.changeData({ bmServiceProviderList: info });
    } else {
      Modal.error({ title: msg });
    }
  },
  // 获取结算编码
  * getCostCodeList(id) {
    const res = yield getCostCodeListAPI({
      bmChargeServiceProviderId: id,
    });
    if (res.code === '0') {
      yield this.changeData({
        costCodeList: res.info || [],
      });
    } else {
      Modal.error({
        title: res.msg,
      });
    }
  },
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件即state.limit属性值
  changeLimitData(state, data) {
    Object.assign(state.limit, data);
  },
  // 重置查询条件
  * clearLimitData() {
    const { formRef } = this.state;
    // 清空校验信息
    if (formRef && formRef.clearValidate) {
      formRef.clearValidate();
    }
    yield this.changeLimitData({
      ...defaultLimit,
    });
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },
  // 查询
  * search(params = {}) {
    const { limit, pageInfo, showSensitiveData } = yield '';
    const param = {
      ...limit,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
    };
    markStatus('loading');
    const handleUrl = showSensitiveData ? searchSensitizeAPI : searchAPI;
    const { code, info, msg } = yield handleUrl(clearEmpty(paramTrim(param), [0, '0', false]));
    if (code === '0') {
      yield this.changeData({
        list: info.data,
        tmpList: info.data, // list暂存
        pageInfo: {
          ...this.state.pageInfo,
          count: info.meta.count,
        },
        selectedRows: [],
        filterStatusCode: '', // 清空tab状态筛选
        urgeClicked: [],
      });
      if (!params?.isOperateSubButton) {
        yield this.changeData({
          allStatusKindCount: info.meta?.customObj || {},
        });
      }
    } else {
      Modal.error({
        title: msg,
      });
    }
  },
  // 页签改变
  * handlePaginationChange(arg = {}) {
    // 校验
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    // 校验通过后执行
    yield this.changeData({
      pageInfo: {
        ...this.state.pageInfo,
        ...arg,
      },
    });
    yield this.search();
  },
  // 校验
  * handleFormValidate() {
    const { formRef } = yield '';
    // 兼容跳转进来触发搜索时，dom还没渲染完，执行formRef.validate()报错
    if (!formRef || !formRef.validate) {
      return true;
    }
    let validateFlag = false;
    yield formRef.validate().then(() => {
      validateFlag = true;
    }).catch(() => {
      validateFlag = false;
    });
    return validateFlag;
  },
  // 预提交
  * preCommit({ isSubmitAfterPreview, id }) {
    markStatus('loading');
    // const { detailInfo } = yield '';
    yield this.changeData({
      PreCommitSensitiveDataOrderId: id,
    });
    const { code, info, msg } = yield preCommitAPI({
      enter: 1, // 1-报账单页面 2-子报账单页面
      payOrderId: id,
    });

    if (code === '0') {
      const newDetailInfo = {
        ...(info || {}),
        // collectionBankAccount: detailInfo.collectionBankAccount,
        // collectionBankAccountName: detailInfo.collectionBankAccountName,
        // collectionBankCode: detailInfo.collectionBankCode,
        // collectionBankOriginName: detailInfo.collectionBankOriginName,
        // serviceProviderCostCode: detailInfo.serviceProviderCostCode,
        // // bmPayMainId: detailInfo.bmPayMainId,
        // bmPayMainName: detailInfo.bmPayMainName,
        ...defaultShowInfoObj,
        files: ((info || {}).files || []).map((f) => ({ ...f, name: f.fileName })),
        invoiceFiles: ((info || {}).invoiceFiles || []).map((f) => ({ ...f, name: f.fileName })),
        id,
      };
      yield this.changeData({
        detailInfo: newDetailInfo,
        detailInfoVisible: true,
        isSubmitAfterPreview,
        showPreCommitSensitiveData: false,
      });
    } else {
      Modal.error({
        title: msg,
      });
    }
  },
  // 提交审批
  * confirmCommit(params) {
    // const { isShowServiceProviderSensitiveData } = yield '';
    markStatus('loading');
    const {
      billDayBegin = '',
      billDayEnd = '',
      bmServiceProviderId = '',
      costExchangeRate,
      costExchangeRateFixed,
      costExchangeRateType = '',
      files = [],
      invoiceFiles = [],
      id = '',
      paymentContent = [],
      paymentCurrency = [],
      warehouseId = '',
      bmServiceProviderCostCodeId = null,
      bmPayMainId = null,
    } = params || {};
    const { selectedRows } = yield '';

    const queryParams = {
      payOrderId: selectedRows[0].id,
      enter: 1, // 1-报账单页面 2-子报账单页面
      billDayBegin,
      billDayEnd,
      bmServiceProviderId,
      costExchangeRate,
      costExchangeRateFixed,
      costExchangeRateType,
      files,
      invoiceFiles,
      id,
      paymentContent,
      paymentCurrency,
      warehouseId,
      bmServiceProviderCostCodeId,
      bmPayMainId,
    };
    // 未获取服务商敏感数据时，需要先获取付款主体
    // if (!isShowServiceProviderSensitiveData) {
    //   const showInfoObj = yield this.handleGetServiceProviderSensitiveData();
    //   if (showInfoObj) {
    //     queryParams.bmPayMainId = showInfoObj?.bmPayMainId || '';
    //   }
    // }

    const { code, msg } = yield confirmCommitAPI(queryParams);
    if (code === '0') {
      Message.success(t('提交审批成功'));
      yield this.handleCloseDetailPreviewSubmit();
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({
        title: msg,
      });
    }
  },
  // 整单作废
  * invalid() {
    markStatus('loading');
    const { selectedRows, invalidRemark } = yield '';
    const ids = selectedRows.map((val) => (val.id));
    const { code, msg } = yield invalidAPI(clearEmpty(paramTrim({ invalidRemark, id: ids[0] }), [0, '0', false]));
    if (code === '0') {
      Message.success(t('作废成功'));
      yield this.changeData({
        invalidModalVisible: false,
      });
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({
        title: msg,
      });
    }
  },
  // 付款作废
  * payInvalid() {
    markStatus('loading');
    const { selectedRows, invalidRemark } = yield '';
    const ids = selectedRows.map((val) => (val.id));
    const { code, msg } = yield payInvalidAPI(clearEmpty(paramTrim({ invalidRemark, id: ids[0] }), [0, '0', false]));
    if (code === '0') {
      Message.success(t('作废成功'));
      yield this.changeData({
        invalidModalVisible: false,
      });
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({
        title: msg,
      });
    }
  },
  // 催办财务
  * submitUrgeNew() {
    markStatus('loading');
    const { pushFinancialObj } = yield '';
    const { code, msg } = yield submitUrgeNewAPI(clearEmpty(paramTrim(pushFinancialObj), [0, '0', false]));
    if (code === '0') {
      Message.success(t('操作成功'));
      yield this.changeData({
        pushFinancialModalVisible: false,
      });
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({
        title: msg,
      });
    }
  },
  // 合单付款
  * merge() {
    markStatus('loading');
    const { selectedRows } = yield '';
    const ids = selectedRows.map((val) => (val.id));
    const { code, msg } = yield mergeAPI(clearEmpty(paramTrim({ ids }), [0, '0', false]));
    if (code === '0') {
      Message.success(t('操作成功'));
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({
        title: msg,
      });
    }
  },
  // 作废重提
  * cancel() {
    markStatus('loading');
    const { selectedRows } = yield '';
    const ids = selectedRows.map((s) => (s.id));
    const { code, msg } = yield cancelAPI(clearEmpty(paramTrim({ id: ids[0] }), [0, '0']));
    if (code === '0') {
      Message.success(t('作废更新成功，请重新提交报账'));
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: msg });
    }
  },
  // 补传附件
  * updateFiles() {
    markStatus('loading');
    const { payOrderId, fileList, fileListTmp } = yield '';
    // fileListTmp中的文件不在fileList中则进行删除
    const needDeleteFiles = fileListTmp.filter((ft) => (fileList.map((f) => (f.id)).indexOf(ft.id) < 0));
    // fileList中的文件不在fileListTmp中则进行上传
    const needUploadFiles = fileList.filter((f) => (fileListTmp.map((ft) => (ft.id)).indexOf(f.id) < 0));
    const needDeleteList = []; // 删除文件的请求列表
    const needUploadList = []; // 上传文件的请求列表
    needDeleteFiles.forEach((item) => {
      needDeleteList.push({
        fileId: item.id,
        id: payOrderId,
      });
    });
    needUploadFiles.forEach((item) => {
      const { fileName, fileUrl } = item;
      needUploadList.push({
        fileName,
        fileUrl,
        id: payOrderId,
      });
    });
    const updateRps = yield updateFilesAPI([...needDeleteList, ...needUploadList]);
    if (updateRps.code === '0') {
      Message.success(t('附件更新完成！'));
      yield this.changeData({
        importModalVisible: false,
      });
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: updateRps.msg });
    }
  },
  // 更新文件上传信息
  * deleteFiles(params) {
    markStatus('loading');
    const res = yield updateFilesAPI(params);
    if (res.code === '0') {
      Message.success(t('附件更新成功！'));
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: res.msg });
    }
  },
  /**
   * 重新付款
   * @param params
   * @returns {Generator<Promise<unknown>|*, boolean, *>}
   */
  * refundPaymentModify({ payOrderNo, bmServiceProviderCostCodeId }) {
    markStatus('loading');
    const { code, msg } = yield rePayAPI({ payOrderNo, costCodeId: bmServiceProviderCostCodeId });
    if (code === '0') {
      Message.success(t('操作成功！'));
      yield this.changeData({ refundPaymentVisible: false });
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: msg });
    }
  },

  // 导出
  * exportData() {
    // 校验
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    // 校验通过后执行
    const {
      limit, pageInfo, selectedRows,
    } = yield '';
    const param = {
      ...limit,
      ids: selectedRows.map((v) => (v.id)),
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
    };
    markStatus('loading');
    const { code, msg } = yield exportListAPI(clearEmpty(paramTrim(param), [0, '0', false]));
    if (code === '0') {
      window.open('#/statistical/download');
    } else if (code === '410101') {
      Modal.warn({ title: t('暂无导出权限，请联系管理员！') });
    } else {
      Modal.error({ title: msg });
    }
  },

  /**
   * 切换服务商敏感数据开关
   * @returns
   */
  * handleChangeServiceProviderSensitiveSwitch() {
    const { isShowServiceProviderSensitiveData, detailInfo } = yield '';
    // 如果已经显示敏感数据，则再次点击则隐藏
    if (isShowServiceProviderSensitiveData) {
      const newDetailInfo = {
        ...detailInfo,
        ...defaultShowInfoObj,
      };
      yield this.changeData({
        isShowServiceProviderSensitiveData: false,
        detailInfo: newDetailInfo,
      });
      return;
    }
    // 如果未显示敏感数据，则再次点击则显示
    yield this.handleGetServiceProviderSensitiveData();
  },

  /**
   * 获取服务商敏感数据
   * @returns
   */
  * handleGetServiceProviderSensitiveData() {
    const { detailInfo } = yield '';

    // 获取结算信息
    const params = {
      bmServiceProviderCostCodeId: detailInfo?.bmServiceProviderCostCodeId || '',
      businessNo: detailInfo?.payOrderNo || t('3PL报账单详情-{}-{}', detailInfo?.bmServiceProviderId, detailInfo?.billDayBegin),
      securityModule: 3,
    };
    const { code, info, msg } = yield getServiceProviderAPI(params);
    if (code === '0') {
      const newShowInfoObj = {
        collectionBankAccount: info.collectionBankAccount || '',
        collectionBankAccountName: info.collectionBankAccountName || '',
        collectionBankCode: info.collectionBankCode || '',
        collectionBankOriginName: info.collectionBankOriginName || '',
        serviceProviderCostCode: info.serviceProviderCostCode || '',
        // bmPayMainId: info.bmPayMainId || '',
        bmPayMainName: info.bmPayMainName || '',
      };
      const newDetailInfo = {
        ...detailInfo,
        ...newShowInfoObj,
      };
      yield this.changeData({
        isShowServiceProviderSensitiveData: true,
        detailInfo: newDetailInfo,
      });
      return newShowInfoObj;
    }
    Modal.error({ title: msg });
  },

  /**
   * 关闭报账单详情
   */
  * handleCloseDetailPreviewSubmit() {
    const { detailInfo } = yield '';
    const newDetailInfo = {
      ...detailInfo,
      ...defaultShowInfoObj,
    };
    yield this.changeData({
      detailInfoVisible: false,
      showPreCommitSensitiveData: false,
      PreCommitSensitiveDataOrderId: null,
      isShowServiceProviderSensitiveData: false,
      detailInfo: newDetailInfo,
    });
  },
};
