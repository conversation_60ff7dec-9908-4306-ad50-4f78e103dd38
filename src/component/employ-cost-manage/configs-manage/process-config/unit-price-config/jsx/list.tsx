import React from 'react';
import { t } from '@shein-bbl/react';
import { Table, Button, Popover } from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import OperationModal from '@public-component/modal/operation-modal';
import Icon from '@shein-components/Icon';
import { handleTablePros } from '@src/lib/deal-func';
import globalStyles from '@src/component/style.less';
import store from '../reducers';
import { queryOperationAPI } from '../server';
import { IPageProps, ILogDataItem, IDataItem } from '../types';
import style from '../style.less';

const dataDealFn = (data: ILogDataItem[]) => {
  data.forEach((item: ILogDataItem) => {
    const desc: string[] = [];
    (item.operateDiff || []).forEach((itemIn) => {
      const str = `【${itemIn.field}】${t(':由{')}${itemIn.oldValue}}${t(
        '修改为{',
      )}${itemIn.newValue}}`;
      desc.push(str);
      desc.push(item.operateContentDetail || '');
    });
    item.desc = desc;
  });
  return data;
};

const operColumn = [{
  title: t('操作'),
  fixed: 'right',
  width: 100,
  render: (record: IDataItem) => (
    <div>
      <Button
        size="small"
        text
        type="primary"
        className={globalStyles.tableTextButton}
        onClick={() => {
          store.openOperModal({
            type: 2,
            record,
          });
        }}
      >
        {t('编辑')}
      </Button>
      <Button
        size="small"
        text
        type="primary"
        className={globalStyles.tableTextButton}
        onClick={() => store.changeData({ recordVisible: true, recordId: record.id })}
      >
        {t('操作日志')}
      </Button>
    </div>
  ),
}];

function List(props: IPageProps) {
  const {
    loading, list, pageInfo, recordId, recordVisible, selectedRows, type,
  } = props;

  let columns = [
    {
      title: <span className={style.required}>{t('工序/环节')}</span>,
      render: 'outLinkName',
      width: 100,
      keygen: t('工序/环节'),
    },
    {
      title: <span className={style.required}>{t('服务/子环节')}</span>,
      render: 'outSubLinkName',
      width: 120,
      keygen: t('服务/子环节'),
    },
    {
      title: <span className={style.required}>{t('单位')}</span>,
      render: 'operateUnitName',
      width: 100,
      keygen: t('单位'),
    },
    {
      title: <span className={style.required}>{t('价格开始日期')}</span>,
      render: 'startDate',
      width: 180,
      keygen: t('价格开始日期'),
    },
    {
      title: (
        <span className={style.required}>
          <Popover style={{ width: 200, padding: 20 }}>
            <span>{t('注：价格开始日期≤价格有效日期＜价格结束日期，配置时请注意不要时间重复。')}</span>
          </Popover>
          {t('价格结束日期')}
          <Icon name="pc-alert-shineout" style={{ color: '#39f', marginLeft: '5px', fontSize: 16 }} />
        </span>),
      render: 'endDate',
      width: 180,
      keygen: t('价格结束日期'),
    },
    {
      title: <span className={style.required}>{t('单价')}</span>,
      render: 'priceDesc',
      width: 100,
      keygen: t('单价'),
    },
    {
      title: t('备注'),
      render: 'remark',
      width: 100,
    },
    {
      title: t('操作人'),
      render: 'lastUpdateUser',
      width: 100,
    },
    {
      title: t('最后操作时间'),
      render: 'lastUpdateTime',
      width: 180,
    },
  ];

  // 审核页面不展示操作列
  if (type !== 2) {
    // @ts-expect-error operColumn
    columns = columns.concat(operColumn);
  }

  const logColumns = [
    {
      title: t('操作日志记录模块/节点'),
      render: 'operateContent',
      width: 180,
    },
    {
      title: t('操作内容取值'),
      width: 300,
      render: (record: ILogDataItem) => {
        // 优先展示list的数据
        if ((record.desc || []).length === 0) {
          return record.operateContentDetail;
        }
        return (
          <>
            {(record.desc || []).map((item) => (
              <div style={{ marginBottom: 5 }}>{item}</div>
            ))}
          </>
        );
      },
    },
    {
      title: t('操作人'),
      render: 'operateUser',
      width: 180,
    },
    {
      title: t('操作时间'),
      render: 'createTime',
      width: 180,
    },
  ];

  const onRowSelectChange = (rows: IDataItem[]) => {
    store.changeData({ selectedRows: rows });
  };

  const tableOtherProps = type === 2 ? {} : {
    value: selectedRows,
    onRowSelect: onRowSelectChange,
  };

  return (
    <section className={globalStyles.tableSection}>
      <SearchAreaTable>
        <Table
          {...handleTablePros(columns)}
          loading={!loading}
          data={list}
          keygen="id"
          pagination={{
            align: 'right',
            current: pageInfo.pageNum,
            pageSize: pageInfo.pageSize,
            layout: [
              ({ total }) => `${t('共')} ${String(total)} ${t('条')}`,
              'links',
              'list',
              () => t('跳至'),
              'jumper',
              () => t('页'),
            ],
            onChange: (page, size) => {
              store.handlePaginationChange({
                pageNum: page,
                pageSize: size,
              });
            },
            className: globalStyles.pagination,
            pageSizeList: pageInfo.pageSizeList,
            total: pageInfo.count,
          }}
          {...tableOtherProps}
        />
      </SearchAreaTable>

      {/* 操作记录 */}
      <OperationModal
        type="table"
        modalProps={{ width: 1000 }}
        apiName={queryOperationAPI}
        param={{
          operateId: recordId,
          operateCode: 'WBMS_EMPLOY_OUT_SETTLE_PRICE',
        }}
        dataDealFn={dataDealFn}
        columns={logColumns}
        visible={recordVisible}
        onCancel={() => store.changeData({ recordVisible: false })}
      />
    </section>
  );
}

export default List;
