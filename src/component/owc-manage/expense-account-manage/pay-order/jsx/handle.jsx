import React, { Component } from 'react';
import PropTypes from 'prop-types';
import {
  Button, Modal, Popover, Message, Textarea, DatePicker, Input, Table, Select, Form, Rule, Radio,
} from 'shineout';
import moment from 'moment';
import ImagesPreviewer from '@shein-components/ImagesPreviewer';
import { t } from '@shein-bbl/react';
import styles from '@src/component/style.less';
import UploadPlus from '@shein-components/upload_plus';
import { uploadFileURL } from '@src/server/basic/upload';
import DetailPreviewSubmit from '@src/component/owc-manage/owc-public-component/detail-preview-submit';
import store from '../reducers';
import style from '../style.less';

const { Gallery } = ImagesPreviewer;

const rules = new Rule();
class Handle extends Component {
  render() {
    const {
      loading,
      selectedRows,
      detailInfo,
      detailInfoVisible,
      currencyTypeNameList,
      costExchangeRateTypeList,
      costExchangeRateList,
      importModalVisible,
      fileList,
      isSubmitAfterPreview,
      urgeVisible,
      urgeObj,
      urgeClicked = [],
      certificateRecordVisible,
      certificateRecordData,
      certificateRecordPreviewVisible,
      certificateRecordPreviewUrl,
      refundPaymentVisible,
      refundPaymentObj,
      costCodeList,
      tmpList,
      pageInfo,
      payOrderStatusList, // 状态下拉
      isExecuteOldRule,
      cancelModalVisible,
      invalidPayReason,
    } = this.props;
    // 统计各个状态的条数
    const allStatusKindCount = tmpList.reduce((countInfo, cur) => {
      if (countInfo[cur.payOrderStatus] || countInfo[cur.payOrderStatus] === 0) {
        // 合并二审-六审到审批中
        if ([1, 22, 23, 24, 25, 26].indexOf(cur.payOrderStatus) > -1) {
          countInfo[1] += 1;
        } else {
          countInfo[cur.payOrderStatus] += 1;
        }
      } else if ([22, 23, 24, 25, 26].indexOf(cur.payOrderStatus) > -1) {
        countInfo[1] += 1;
      } else {
        countInfo[cur.payOrderStatus] = 1;
      }
      return countInfo;
    }, { 1: 0 });
    const certificateRecordColumns = [
      {
        title: t('序号'),
        width: 80,
        render: (r, i) => (i + 1),
      },
      {
        title: t('时间'),
        width: 240,
        render: 'pushTime',
      },
      {
        title: t('文件名称'),
        width: 120,
        render: 'fileName',
      },
      {
        title: t('类型'),
        width: 100,
        render: 'operTypeName',
      },
      {
        title: t('操作'),
        width: 120,
        render: (record) => (
          <div>
            <Button
              size="small"
              text
              type="primary"
              className={style.listOperationButton}
              onClick={() => window.open(record.fileUrl, '_blank')}
            >
              {t('下载')}
            </Button>
            <Button
              size="small"
              text
              type="primary"
              className={style.listOperationButton}
              onClick={() => {
                const ext = `${record.fileUrl}`.match(/\.[a-z]+$/i);
                if (ext !== null && ['jpg', 'jpeg', 'png'].indexOf(ext[0]?.substring(1)) > -1) {
                  store.changeData({
                    certificateRecordPreviewVisible: true,
                    certificateRecordPreviewUrl: record.fileUrl,
                  });
                } else {
                  window.open(record.fileUrl, '_blank');
                }
              }}
            >
              {t('预览')}
            </Button>
          </div>
        ),
      },
    ];
    return (
      <section className={[styles.handle, 'handleSection'].join(' ')} style={{ paddingBottom: 0 }}>
        <Button
          type="primary"
          disabled={!loading || selectedRows.length !== 1}
          onClick={() => {
            // 预提交
            store.preCommit({
              isSubmitAfterPreview: true,
              id: selectedRows[0]?.id,
              check: true,
            });
          }}
        >
          {t('原单提报')}
        </Button>
        <Button
          disabled={!loading || selectedRows.length !== 1}
          type="primary"
          size="default"
          loading={!loading}
        >
          <Popover.Confirm
            type="warning"
            okType="primary"
            text={{ ok: t('确认'), cancel: t('取消') }}
            onOk={() => { store.invalid(); }}
          >
            {t('是否确定整单作废?')}
          </Popover.Confirm>
          {t('整单作废')}
        </Button>
        <Button
          type="primary"
          disabled={!loading || selectedRows.length !== 1}
        >
          <Popover.Confirm
            type="warning"
            okType="primary"
            text={{ ok: t('确认'), cancel: t('取消') }}
            onOk={() => { store.cancel(); }}
          >
            {t('是否确定作废重提?')}
          </Popover.Confirm>
          {t('作废重提')}
        </Button>
        <Button
          type="primary"
          disabled={!loading || !selectedRows?.length}
          onClick={() => {
            if (selectedRows.some((s) => (Number(s.payOrderStatus) !== 3))) {
              Message.info(t('报账单当前未进入财务审核，不可催办'));
              return;
            }
            if (selectedRows?.length > 200) {
              Message.info(t('批量催办的数量不能超过{}条', 200));
              return;
            }
            store.changeData({
              urgeVisible: true,
              urgeClicked: selectedRows,
              urgeObj: {
                ...urgeObj,
                ids: selectedRows.map((s) => (s.id)),
                urgeTime: '',
                urgeReason: '',
              },
            });
          }}
        >
          {t('催办')}
        </Button>
        <Button
          type="primary"
          disabled={!loading || selectedRows?.length !== 1 || !selectedRows[0]?.bankRefundStatus}
          onClick={() => {
            // 根据服务商id获取结算编码下拉
            store.getCostCodeList((selectedRows[0] || {}).bmServiceProviderId);
            // 打开退票付款填充回显数据
            store.changeData({
              refundPaymentVisible: true,
              refundPaymentObj: selectedRows[0] || {},
            });
          }}
        >
          {t('重新付款')}
        </Button>
        <Button
          type="primary"
          disabled={!loading || !selectedRows?.length || selectedRows.some((si) => (si.payOrderStatus !== 27))}
          onClick={() => {
            Modal.confirm({
              title: t('重新提交财务TSS报账？'),
              content: <span>{t('已选择{}条数据', selectedRows?.length)}</span>,
              onOk: () => {
                store.rePushTss({
                  idList: selectedRows.map((si) => (si.id)),
                });
              },
              text: { ok: t('确认'), cancel: t('取消') },
            });
          }}
        >
          {t('重推财务')}
        </Button>
        <Button
          disabled={!loading || !selectedRows.length}
          type="primary"
          size="default"
          loading={!loading}
        >
          <Popover.Confirm
            type="warning"
            okType="primary"
            text={{ ok: t('确认'), cancel: t('取消') }}
            onOk={() => {
              store.merge({
                ids: selectedRows.map((si) => (si.id)),
              });
            }}
          >
            {t('是否确定合单付款?')}
          </Popover.Confirm>
          {t('合单付款')}
        </Button>
        <Button
          type="primary"
          disabled={!loading || selectedRows.length !== 1 || selectedRows.some((si) => si.payOrderStatus !== 6)}
          onClick={() => {
            store.changeData({
              cancelModalVisible: true,
            });
          }}
        >
          {t('付款作废')}
        </Button>
        <Modal
          visible={cancelModalVisible}
          width={450}
          title={t('是否确认作废？')}
          onClose={() => {
            store.changeData({
              cancelModalVisible: false,
            });
          }}
          footer={(
            <div>
              <Button
                onClick={() => {
                  store.changeData({
                    cancelModalVisible: false,
                  });
                }}
              >
                {t('取消')}
              </Button>
              <Button
                type="primary"
                disabled={!invalidPayReason}
                loading={loading === 0}
                style={{ marginRight: 15 }}
                onClick={() => {
                  Modal.confirm({
                    title: t(''),
                    content: t('是否确认作废？'),
                    onOk: () => store.invalidPay(),
                    text: { ok: t('作废'), cancel: t('取消') },
                  });
                }}
              >
                {t('确定')}
              </Button>
            </div>
          )}
        >
          <Form
            labelWidth={80}
            labelAlign="right"
          >
            <Form.Item required label={t('作废原因（必填）')}>
              <Textarea
                rows={5}
                maxLength={200}
                placeholder={t('请输入作废原因')}
                delay={0}
                value={invalidPayReason}
                onChange={(val) => {
                  store.changeData({
                    invalidPayReason: val,
                  });
                }}
              />
              <p style={{ padding: '10px 0' }}>
                {t('最大支持录入200字符')}
              </p>
            </Form.Item>
          </Form>
        </Modal>
        <Radio.Group
          data-bind="filterStatusCode"
          button
          onChange={(val) => {
            let filterList = [];
            // 合并二审...六审到审批中
            if (val === 1) {
              // 审批中 = 审批中+二审...六审之和
              filterList = tmpList.filter((li) => ([1, 22, 23, 24, 25, 26].indexOf(li.payOrderStatus) > -1));
            } else {
              filterList = tmpList.filter((li) => (li.payOrderStatus === val));
            }
            store.changeData({
              filterStatusCode: val,
              list: filterList,
              pageInfo: {
                ...pageInfo,
                pageNum: 1, // 页码
                count: filterList.length,
              },
              selectedRows: [],
            });
          }}
        >
          {(payOrderStatusList || []).filter((pi) => ([22, 23, 24, 25, 26].indexOf(pi.dictCode) < 0)).map((d) => (
            <Radio key={d.dictCode} htmlValue={d.dictCode} style={{ marginBottom: '5px' }}>
              {d.dictNameZh}
              (
              {allStatusKindCount[d.dictCode] || 0}
              )
            </Radio>
          ))}
        </Radio.Group>
        {/* 报账单详情信息弹框 */}
        {detailInfoVisible && (
          <DetailPreviewSubmit
            loading={loading}
            confirmSubmit={(params) => { store.confirmCommit(params); }}
            onClose={() => store.changeData({ detailInfoVisible: false })}
            isSubmitAfterPreview={isSubmitAfterPreview}
            detailInfo={detailInfo}
            currencyTypeNameList={currencyTypeNameList}
            costExchangeRateTypeList={costExchangeRateTypeList}
            costExchangeRateList={costExchangeRateList}
          />
        )}
        {/* 主报账单补传附件 */}
        <Modal
          visible={importModalVisible}
          width={1100}
          bodyStyle={{ maxHeight: '550px', overflow: 'auto' }}
          maskCloseAble={null}
          destroy
          title={t('文件上传')}
          onClose={() => {
            store.changeData({
              importModalVisible: false,
            });
          }}
          footer={[
            <Button
              key="cancel"
              onClick={() => {
                store.changeData({
                  importModalVisible: false,
                });
              }}
            >
              {t('取消')}
            </Button>,
            <Button
              type="primary"
              loading={!loading}
              disabled={!loading}
              onClick={() => {
                if (isExecuteOldRule) { // 如果开启执行旧规则的开关 WBMS_PAY_ORDER_EXECUTE_OLD = 1，执行旧规则
                  store.oldUpdateFiles();
                } else { // 否则执行新的轮训规则
                  store.updateFiles(store);
                }
              }}
            >
              {t('确认更新')}
            </Button>,
          ]}
        >
          <div style={{ paddingBottom: '20px' }}>
            <span style={{ display: 'inline-block', marginBottom: 20, fontWeight: 'bolder' }}>
              {t('发票')}
              :
            </span>
            <UploadPlus
              accept=".xls,.xlsx,.pdf,.docx,.doc,.jpg,.png,"
              limit={20}
              autoUpload
              action={uploadFileURL}
              disabled={fileList.length >= 20}
              fileList={fileList.filter((f) => f.businessType === 9)}
              autoUploadKeyName="file"
              filePathKeyName="fileUrl"
              // data={{
              //   is_use_origin_name: true,
              // }}
              onFailUpload={async (_, info) => Message.error(info)}
              onDelete={async (removeItem) => {
                const newFiles = fileList.filter((file) => file.fileUrl !== removeItem.fileUrl);
                store.changeData({
                  fileList: newFiles,
                });
              }}
              onSuccessUpload={async ({ file, info }) => {
                const fullName = file.name;
                const filename = (fullName?.split('.') || [])[0];
                if (!fullName.length) {
                  Message.error(t('文件名和扩展名不能同时为空'));
                  return false;
                }
                // 123..ext
                if (/\s/.test(filename) || /(\.|。){2}/.test(fullName)) {
                  Message.error(t('文件名不能有空字符,文件名尾不能为“.”号'));
                  return false;
                }
                if (/[#+?？:：*^<>|｜-]/g.test(filename) || fullName.match(/\./g)?.length >= 2) {
                  Message.error(t('文件名中不能包含“#、:、 * 、^、 ?、 <、 >、 | 、-、 +、.”中任意字符'));
                  return false;
                }
                if (fullName.length > 100) {
                  Message.error(t('文件名包含扩展名长度不能大于{}个字符', 100));
                  return false;
                }
                const fileItem = {
                  name: file.name,
                  fileName: file.name,
                  fileUrl: info.image_url,
                  businessType: 9,
                };
                store.changeData({
                  fileList: [...fileList, { id: Date.now(), ...fileItem }],
                });
              }}
              renderResult={(f) => (
                <a
                  key={f.fileUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  href={f.fileUrl}
                  download
                >
                  {f?.fileName || t('附件')}
                </a>
              )}
            />
          </div>
          <div style={{ paddingBottom: '20px' }}>
            <span style={{ display: 'inline-block', marginBottom: 20, fontWeight: 'bolder' }}>
              {t('附件')}
              :
            </span>
            <UploadPlus
              accept=".xls,.xlsx,.pdf,.docx,.doc,.zip,.jpg,.png"
              limit={20}
              autoUpload
              disabled={fileList.length >= 20}
              maxSize={200}
              action={uploadFileURL}
              fileList={fileList.filter((f) => f.businessType === 2)}
              autoUploadKeyName="file"
              filePathKeyName="fileUrl"
              // data={{
              //   is_use_origin_name: true,
              // }}
              onFailUpload={async (_, info) => Message.error(info)}
              onDelete={async (removeItem) => {
                const newFiles = fileList.filter((file) => file.fileUrl !== removeItem.fileUrl);
                store.changeData({
                  fileList: newFiles,
                });
              }}
              onSuccessUpload={async ({ file, info }) => {
                const fullName = file.name;
                const filename = (fullName?.split('.') || [])[0];
                if (!fullName.length) {
                  Message.error(t('文件名和扩展名不能同时为空'));
                  return false;
                }
                // 123..ext
                if (/\s/.test(filename) || /(\.|。){2}/.test(fullName)) {
                  Message.error(t('文件名不能有空字符,文件名尾不能为“.”号'));
                  return false;
                }
                if (/[#+?？:：*^<>|｜-]/g.test(filename) || fullName.match(/\./g)?.length >= 2) {
                  Message.error(t('文件名中不能包含“#、:、 * 、^、 ?、 <、 >、 | 、-、 +、.”中任意字符'));
                  return false;
                }
                if (fullName.length > 255) {
                  Message.error(t('文件名包含扩展名长度不能大于{}个字符', 255));
                  return false;
                }
                const fileItem = {
                  name: file.name,
                  fileName: file.name,
                  fileUrl: info.image_url,
                  businessType: 2,
                };
                store.changeData({
                  fileList: [...fileList, { id: Date.now(), ...fileItem }],
                });
              }}
              renderResult={(f) => (
                <a
                  key={f.fileUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  href={f.fileUrl}
                  download
                >
                  {f?.fileName || t('附件')}
                </a>
              )}
            />
          </div>
        </Modal>
        <Modal
          visible={urgeVisible}
          width={850}
          maskCloseAble={false}
          title={t('催办')}
          onClose={() => store.changeData({ urgeVisible: false })}
          footer={(
            <div>
              <Button onClick={() => store.changeData({ urgeVisible: false })}>{t('关闭')}</Button>
              <Button
                type="primary"
                loading={!loading}
                disabled={!loading}
                onClick={() => {
                  if (isExecuteOldRule) { // 如果开启执行旧规则的开关 WBMS_PAY_ORDER_EXECUTE_OLD = 1，执行旧规则
                    store.oldUrge(urgeObj);
                  } else { // 否则执行新的轮训规则
                    store.urge({ params: urgeObj, store });
                  }
                }}
              >
                {t('确认')}
              </Button>
            </div>
          )}
        >
          <div className={styles.block_label}>
            <span className={`${styles.add_label}`} />
            <span style={{ color: 'gray' }}>
              {t('当前已勾选', urgeClicked.length)}
              <span style={{ color: 'red' }}>{urgeClicked.length}</span>
              {t('条单据')}
            </span>
          </div>
          <div className={styles.block_label}>
            <span className={`${styles.add_label}`}>
              {t('申请人')}
              :
            </span>
            <Input
              label={t('申请人')}
              width={220}
              disabled
              value={urgeObj.applicant}
            />
          </div>
          <div className={styles.block_label}>
            <span className={`${styles.add_label}`}>
              {t('单号')}
              :
            </span>
            <b style={{ color: 'red' }}>
              {urgeClicked.length > 3 ? `${urgeClicked.slice(0, 3).map((i) => (i.payOrderNo)).join('、')}...` : urgeClicked.slice(0, 3).map((i) => (i.payOrderNo)).join('、')}
            </b>
          </div>
          <div className={styles.block_label}>
            <span className={`${styles.add_label}`}>
              {t('期望完成日期')}
              :
            </span>
            <DatePicker
              type="date"
              width={220}
              min={Date.now()}
              clearable
              value={urgeObj.urgeTime}
              onChange={(value) => {
                store.changeData({
                  urgeObj: {
                    ...urgeObj,
                    urgeTime: value ? moment(value).format('YYYY-MM-DD 00:00:00') : '',
                  },
                });
              }}
            />
          </div>
          <div className={styles.block_label}>
            <span className={`${styles.add_label}`}>
              {t('催办原由')}
              :
            </span>
            <Textarea
              rows={6}
              placeholder={t('请输入催办原由')}
              maxLength={100}
              info={100}
              style={{ width: '350px' }}
              value={urgeObj.urgeReason}
              onChange={(value) => {
                store.changeData({
                  urgeObj: {
                    ...urgeObj,
                    urgeReason: value,
                  },
                });
              }}
            />
          </div>
        </Modal>
        <Modal
          visible={certificateRecordVisible}
          maskCloseAble={null}
          destroy
          width={600}
          bodyStyle={{ maxHeight: '475px', overflow: 'auto' }}
          title={t('电子回单记录')}
          onClose={() => {
            store.changeData({
              certificateRecordVisible: false,
            });
          }}
          footer={null}
        >
          <Table
            keygen="id"
            bordered
            columns={certificateRecordColumns}
            data={certificateRecordData}
            pagination={false}
            fixed="both"
            style={{ maxHeight: 400 }}
          />
        </Modal>
        <Modal
          visible={certificateRecordVisible}
          maskCloseAble={null}
          destroy
          esc={false}
          width={600}
          bodyStyle={{ maxHeight: '475px', overflow: 'auto' }}
          title={t('电子回单记录')}
          onClose={() => {
            store.changeData({
              certificateRecordVisible: false,
            });
          }}
          footer={null}
        >
          <Table
            keygen="id"
            bordered
            columns={certificateRecordColumns}
            data={certificateRecordData}
            pagination={false}
            fixed="both"
            style={{ maxHeight: 400 }}
          />
        </Modal>
        <div>
          {certificateRecordPreviewVisible ? (
            <Gallery
              dataSource={
                {
                  thumb: [
                    certificateRecordPreviewUrl,
                  ],
                  origin: [
                    certificateRecordPreviewUrl,
                  ],
                }
              }
              index={0}
              onClose={() => {
                store.changeData({
                  certificateRecordPreviewVisible: false,
                });
              }}
              zoomAble
              zoomRatio={3}
            />
          ) : null}
        </div>
        {/* 退票付款弹框 */}
        <Modal
          key={refundPaymentVisible}
          visible={refundPaymentVisible}
          width={500}
          maskCloseAble={false}
          title={t('重新付款')}
          onClose={() => store.changeData({ refundPaymentVisible: false })}
          footer={(
            <div>
              <Button onClick={() => store.changeData({ refundPaymentVisible: false })}>{t('关闭')}</Button>
              <Button
                type="primary"
                disabled={!loading || !refundPaymentObj.bmServiceProviderCostCodeId}
              >
                <Popover.Confirm
                  type="warning"
                  okType="primary"
                  text={{ ok: t('确认'), cancel: t('取消') }}
                  onOk={() => { store.refundPaymentModify(refundPaymentObj); }}
                >
                  {t('是否确定修改?')}
                </Popover.Confirm>
                {t('确认')}
              </Button>
            </div>
          )}
        >
          <Form
            labelWidth={100}
            labelAlign="right"
            onChange={(value) => {
              store.changeData({
                refundPaymentObj: value,
              });
            }}
            value={refundPaymentObj}
          >
            <Form.Item label={t('报账单号')}>
              <Input disabled width={250} name="payOrderNo" />
            </Form.Item>
            <Form.Item label={t('退款单号')}>
              <Input disabled width={250} name="bankRefundNumber" />
            </Form.Item>
            <Form.Item label={t('原付款单')}>
              <Input disabled width={250} name="payNumber" />
            </Form.Item>
            <Form.Item label={t('付款公司主体')}>
              <Input disabled width={250} name="bmPayMainName" />
            </Form.Item>
            <Form.Item required label={t('结算编码')}>
              <Select
                autoAdapt
                label={t('结算编码')}
                name="bmServiceProviderCostCodeId"
                keygen="id"
                format="id"
                placeholder={t('全部')}
                data={costCodeList}
                clearable
                width={250}
                rules={[rules.required]}
                renderItem="serviceProviderCostCode"
                renderUnmatched={(r) => r.serviceProviderCostCode || <span style={{ color: '#bbb' }}>{t('全部')}</span>}
                onFilter={(text) => (d) => d.serviceProviderCostCode.toLowerCase().indexOf(text.toLowerCase()) >= 0}
              />
            </Form.Item>
          </Form>
        </Modal>
      </section>
    );
  }
}

Handle.propTypes = {
  loading: PropTypes.bool,
  selectedRows: PropTypes.arrayOf(PropTypes.shape),
  detailInfo: PropTypes.shape(),
  detailInfoVisible: PropTypes.bool,
  currencyTypeNameList: PropTypes.arrayOf(PropTypes.shape),
  costExchangeRateTypeList: PropTypes.arrayOf(PropTypes.shape),
  costExchangeRateList: PropTypes.arrayOf(PropTypes.shape),
  importModalVisible: PropTypes.bool,
  fileList: PropTypes.arrayOf(PropTypes.shape),
  isSubmitAfterPreview: PropTypes.bool,
  urgeVisible: PropTypes.bool,
  urgeObj: PropTypes.shape(),
  urgeClicked: PropTypes.arrayOf(PropTypes.shape),
  refundPaymentVisible: PropTypes.bool,
  refundPaymentObj: PropTypes.shape(),
  costCodeList: PropTypes.arrayOf(PropTypes.shape),
  pageInfo: PropTypes.shape(),
  certificateRecordVisible: PropTypes.bool,
  certificateRecordData: PropTypes.arrayOf(PropTypes.shape),
  certificateRecordPreviewVisible: PropTypes.bool,
  certificateRecordPreviewUrl: PropTypes.string,
  tmpList: PropTypes.arrayOf(PropTypes.shape()),
  payOrderStatusList: PropTypes.arrayOf(PropTypes.shape()),
  isExecuteOldRule: PropTypes.bool,
  cancelModalVisible: PropTypes.bool,
  invalidPayReason: PropTypes.string,
};
export default Handle;
