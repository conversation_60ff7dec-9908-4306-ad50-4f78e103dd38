import { markStatus } from 'rrc-loader-helper';
import moment from 'moment';
import { Message, Modal } from 'shineout';
import { getSize } from '@src/middlewares/pagesize';
import { t } from '@shein-bbl/react';
import {
  dictCatQuery, dictSelect, getWarehouseApi,
} from '@src/server/basic/dictionary';
import { clearEmpty, paramTrim } from '@src/lib/deal-func';
import { getServiceProviderAPI, getCurrencyTypeAPI, getCostCodeListAPI } from '@src/server/common/owc-common';
import { userNameSessionStorage, formatNameSessionStorage } from '@src/lib/storage-new';
import { getApolloConfigAPI } from '@src/server/common/common';
import { apolloFormatObj } from '@src/lib/dealFunc';
import {
  searchAPI, invalidAPI, preCommitAPI, confirmCommitAPI, cancelAPI, updateFilesAPI, urgeAPI, submitUpdateFilesAPI, submitUrgeAPI, refundPaymentModifyAPI,
  rePushTssAPI, mergeAPI, pollUpdateFilesAPI, pollUrgeAPI,
  invalidPayAPI,
} from './server';

export const defaultLimit = {
  bmServiceProviderId: '', // 服务商
  warehouseId: '', // 仓库
  creator: userNameSessionStorage.getItem() || '', // 创建人
  payOrderNo: '', // 报账单号
  billType: 1, // 账单类型
  billDayBegin: moment().subtract(1, 'month').startOf('month').format('YYYY-MM-DD 00:00:00'), // 账期开始时间
  billDayEnd: moment().subtract(1, 'month').endOf('month').format('YYYY-MM-DD 23:59:59'), // 账期结束时间
  createTimeStart: moment().subtract(1, 'month').startOf('month').format('YYYY-MM-DD 00:00:00'), // 创建开始时间
  createTimeEnd: moment().subtract(1, 'month').endOf('month').format('YYYY-MM-DD 23:59:59'), // 创建结束时间
  payOrderStatus: 1, // 状态 默认审批中
  fileStatus: '', // 附件状态
  currencyTypeName: '', // 币种
};

const defaultState = {
  formRef: {}, // 绑定 form 的引用, 可以调用某些 form 的方法  validate(校验表单) 具体可以查看 https://shineout.biz.sheincorp.cn/1.6.x/cn/components/Form
  loading: 0, // 0 loading, 1 load success, 2 load fail
  ready: true,
  selectedRows: [],
  limit: defaultLimit,
  pageInfo: {
    pageNum: 1, // 页码
    count: 0, // 表格总条数
    pageSize: getSize(),
    pageSizeList: [20, 50, 100, 1000, 2000], // 表格页显示条数
  },
  warehouseList: [], // 仓库列表
  currencyTypeNameList: [], // 币种
  bmServiceProviderList: [], // 服务商列表
  list: [],
  tmpList: [], // 暂存列表数据
  detailInfo: {}, // 预提交返回的报账单详情信息
  detailInfoVisible: false, // 预提交成功后弹出报账单详情框
  payOrderStatusList: [], // 状态下拉
  operationTitle: '', // 日志弹框标题
  recordId: '', // 行id
  operateCode: '', // 查询日志编码
  recordVisible: false, // 日志弹框
  filterStatusCode: '', // 点击tab进行前端筛选
  fileStatusList: [], // 附件状态类型下拉
  billTypeList: [], // 账单类型
  importModalVisible: '', // 补传附件弹框
  fileList: [], // 补传附件
  fileListTmp: [], // 已上传附件列表缓存 fileList不在fileListTmp中则上传更新 fileListTmp不在fileList中则进行删除更新
  isSubmitAfterPreview: false, // 是否是 true 查看详情后填写表单进行提交 false 仅仅是预览报账单详情
  urgeVisible: false, // 催办弹框是否显示
  urgeObj: {
    urgeTime: '',
    urgeReason: '',
    applicant: `${formatNameSessionStorage.getItem()}`,
  }, // 提交催办的信息
  urgeClicked: [], // 区分点勾选还是单行点击
  certificateRecordVisible: false, // 凭证记录弹框
  certificateRecordData: [], // 凭证记录列表数据
  certificateRecordPreviewVisible: false, // 凭证记录预览弹框
  certificateRecordPreviewUrl: '', // 凭证记录预览图片地址
  refundPaymentVisible: false, // 退票付款弹框
  refundPaymentObj: {},
  costCodeList: [], // 结算编码下拉
  pollIntervalId: null, // 轮询更新附件的定时器id
  isExecuteOldRule: false, // 是否执行旧补传附件/旧催办逻辑
  cancelModalVisible: false, // 付款作废弹框
  invalidPayReason: '', // 付款作废原因
};

export default {
  state: defaultState,
  * init() {
    markStatus('loading');
    const [data, warehouseList] = yield Promise.all([
      dictSelect({ catCode: ['BM_BILL_TYPE', 'COMPANY_BODY', 'COST_EXCHANGE_RATE_TYPE', 'COST_EXCHANGE_RATE_VALUE', 'WBMS_FILE_STATUS', 'WBMS_PAY_ORDER_STATUS'] }),
      getWarehouseApi({ enabled: 1 }),
      dictCatQuery({ pageNum: 1, pageSize: 50 }),
    ]);
    if (data.code === '0') {
      yield this.changeData({
        warehouseList: warehouseList.info.data,
        // 账单类型
        billTypeList: data.info.data.find((x) => x.catCode === 'BM_BILL_TYPE').dictListRsps,
        // 付款主体
        payMainIdList: data.info.data.find((item) => item.catCode === 'COMPANY_BODY').dictListRsps,
        // 付款汇率方式
        costExchangeRateTypeList: data.info.data.find((item) => item.catCode === 'COST_EXCHANGE_RATE_TYPE').dictListRsps,
        // 汇率值下拉
        costExchangeRateList: data.info.data.find((item) => item.catCode === 'COST_EXCHANGE_RATE_VALUE').dictListRsps,
        // 附件状态下拉
        fileStatusList: data.info.data.find((item) => item.catCode === 'WBMS_FILE_STATUS').dictListRsps,
        // 报账单状态
        payOrderStatusList: data.info.data.find((item) => item.catCode === 'WBMS_PAY_ORDER_STATUS').dictListRsps,
      });
    } else {
      Modal.error({ title: data.msg });
    }

    // 获取币种下拉列表数据
    yield this.getCurrencyType();

    // 获取服务商列表
    yield this.getServiceProvider();

    // 获取apollo配置
    yield this.getApolloConfig();

    // 默认查询
    yield this.search();
  },
  // 获取币种下拉列表数据
  * getCurrencyType() {
    const { code, info, msg } = yield getCurrencyTypeAPI({ currencyStatus: 1 });
    if (code === '0') {
      yield this.changeData({ currencyTypeNameList: info });
    } else {
      Modal.error({ title: msg });
    }
  },
  // 获取服务商下拉列表数据
  * getServiceProvider() {
    const { code, info, msg } = yield getServiceProviderAPI();
    if (code === '0') {
      yield this.changeData({ bmServiceProviderList: info });
    } else {
      Modal.error({ title: msg });
    }
  },
  // 获取结算编码
  * getCostCodeList(id) {
    const res = yield getCostCodeListAPI({
      serviceProviderId: id,
    });
    if (res.code === '0') {
      yield this.changeData({
        costCodeList: res.info || [],
      });
    } else {
      Modal.error({
        title: res.msg,
      });
    }
  },
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 改搜索条件即state.limit属性值
  changeLimitData(state, data) {
    Object.assign(state.limit, data);
  },
  // 重置查询条件
  * clearLimitData() {
    const { formRef } = this.state;
    // 清空校验信息
    if (formRef && formRef.clearValidate) {
      formRef.clearValidate();
    }
    yield this.changeLimitData({
      ...defaultLimit,
    });
    // 清空校验信息
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },
  // 查询
  * search() {
    const { limit, pageInfo } = yield '';
    const param = {
      ...limit,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
    };
    markStatus('loading');
    const { code, info, msg } = yield searchAPI(clearEmpty(paramTrim(param), [0, '0', false]));
    if (code === '0') {
      yield this.changeData({
        list: info.data,
        tmpList: info.data, // list暂存
        pageInfo: {
          ...this.state.pageInfo,
          count: info.meta.count,
        },
        selectedRows: [],
        filterStatusCode: '', // 清空tab状态筛选
        urgeClicked: [],
      });
    } else {
      Modal.error({
        title: msg,
      });
    }
  },
  // 页签改变
  * handlePaginationChange(arg = {}) {
    // 校验
    const validateFlag = yield this.handleFormValidate();
    if (!validateFlag) {
      return;
    }
    // 校验通过后执行
    yield this.changeData({
      pageInfo: {
        ...this.state.pageInfo,
        ...arg,
      },
    });
    yield this.search();
  },
  // 校验
  * handleFormValidate() {
    const { formRef } = yield '';
    // 兼容跳转进来触发搜索时，dom还没渲染完，执行formRef.validate()报错
    if (!formRef || !formRef.validate) {
      return true;
    }
    let validateFlag = false;
    yield formRef.validate().then(() => {
      validateFlag = true;
    }).catch(() => {
      validateFlag = false;
    });
    return validateFlag;
  },
  // 预提交
  * preCommit({ isSubmitAfterPreview, id, check }) {
    markStatus('loading');
    const { code, info, msg } = yield preCommitAPI(clearEmpty(paramTrim({ id, check }), [0, '0', false]));
    if (code === '0') {
      yield this.changeData({
        detailInfo: {
          ...(info || {}),
          files: ((info || {}).files || []).map((f) => ({ ...f, name: f.fileName })),
          invoiceFiles: ((info || {}).invoiceFiles || []).map((f) => ({ ...f, name: f.fileName })),
          id,
        },
        detailInfoVisible: true,
        isSubmitAfterPreview,
      });
    } else {
      Modal.error({
        title: msg,
      });
    }
  },
  // 提交审批
  * confirmCommit(params) {
    markStatus('loading');
    const {
      billDayBegin = '',
      billDayEnd = '',
      bmServiceProviderId = '',
      costExchangeRate,
      costExchangeRateFixed,
      costExchangeRateType = '',
      files = [],
      invoiceFiles = [],
      id = '',
      paymentContent = [],
      paymentCurrency = [],
      warehouseId = '',
    } = params || {};
    const { code, msg } = yield confirmCommitAPI({
      billDayBegin,
      billDayEnd,
      bmServiceProviderId,
      costExchangeRate,
      costExchangeRateFixed,
      costExchangeRateType,
      files,
      invoiceFiles,
      id,
      paymentContent,
      paymentCurrency,
      warehouseId,
    });
    if (code === '0') {
      Message.success(t('提交审批成功'));
      yield this.changeData({
        detailInfoVisible: false,
      });
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({
        title: msg,
      });
    }
  },
  // 作废报账单
  * invalid() {
    markStatus('loading');
    const { selectedRows } = yield '';
    const ids = selectedRows.map((s) => (s.id));
    const { code, msg } = yield invalidAPI(clearEmpty(paramTrim({ id: ids[0] }), [0, '0', false]));
    if (code === '0') {
      Message.success(t('作废成功'));
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({
        title: msg,
      });
    }
  },
  // 作废重提
  * cancel() {
    markStatus('loading');
    const { selectedRows } = yield '';
    const ids = selectedRows.map((s) => (s.id));
    const { code, msg } = yield cancelAPI(clearEmpty(paramTrim({ id: ids[0] }), [0, '0']));
    if (code === '0') {
      Message.success(t('作废更新成功，请重新提交报账'));
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: msg });
    }
  },
  // 补传附件
  * updateFiles(store) {
    yield this.changeData({
      loading: 0,
    });
    const {
      payOrderId, fileList, fileListTmp, pollIntervalId,
    } = yield '';
    // fileListTmp中的文件不在fileList中则进行删除
    const needDeleteFiles = fileListTmp.filter((ft) => (fileList.map((f) => (f.id)).indexOf(ft.id) < 0));
    // fileList中的文件不在fileListTmp中则进行上传
    const needUploadFiles = fileList.filter((f) => (fileListTmp.map((ft) => (ft.id)).indexOf(f.id) < 0));
    const needDeleteList = []; // 删除文件的请求列表
    const needUploadList = []; // 上传文件的请求列表
    needDeleteFiles.forEach((item) => {
      needDeleteList.push({
        fileBusinessType: item.businessType,
        fileId: item.id,
        id: payOrderId,
      });
    });
    needUploadFiles.forEach((item) => {
      const { fileName, fileUrl } = item;
      needUploadList.push({
        fileBusinessType: item.businessType,
        fileName,
        fileUrl,
        id: payOrderId,
      });
    });
    if (!needDeleteList.length && !needUploadList.length) {
      Message.success(t('无更新数据！'));
      yield this.changeData({
        importModalVisible: false,
      });
      return;
    }
    const { code, info, msg } = yield submitUpdateFilesAPI([...needDeleteList, ...needUploadList]);
    if (code === '0') {
      // 轮询获取更新结果
      if (pollIntervalId) {
        clearInterval(pollIntervalId);
      }
      const newPollIntervalId = setInterval(async () => {
        // 非敏感词
        store.handlePollUpdateFiled({ reqToken: info.reqToken || '', type: 'update' }); // 轮询更新结果，type: update-更新附件
      }, info.pollIntervalMs || 1000);
      yield this.changeData({
        pollIntervalId: newPollIntervalId,
      });
    } else {
      Modal.error({ title: msg });
    }
  },
  // 更新文件上传信息
  * deleteFiles(actions) {
    const { params, store } = actions;
    yield this.changeData({
      loading: 0,
    });
    const { code, info, msg } = yield submitUpdateFilesAPI(params);
    if (code === '0') {
      const { pollIntervalId } = yield '';
      if (pollIntervalId) {
        clearInterval(pollIntervalId);
      }
      const newPollIntervalId = setInterval(async () => {
        // 非敏感词
        store.handlePollUpdateFiled({ reqToken: info.reqToken || '', type: 'update' }); // 轮询更新结果，type: update-更新附件
      }, info.pollIntervalMs || 1000);
      yield this.changeData({
        pollIntervalId: newPollIntervalId,
      });
    } else {
      Modal.error({ title: msg });
    }
  },
  /**
   * 催办
   * @param params
   * @returns {Generator<Promise<unknown>|*, boolean, *>}
   */
  * urge(actions) {
    const { params, store } = actions;
    yield this.changeData({
      loading: 0,
    });
    const { code, info, msg } = yield submitUrgeAPI(params);
    if (code === '0') {
      const { pollIntervalId } = yield '';
      if (pollIntervalId) {
        clearInterval(pollIntervalId);
      }
      const newPollIntervalId = setInterval(async () => {
        // 非敏感词
        store.handlePollUpdateFiled({ reqToken: info.reqToken || '', type: 'urge' }); // 轮询更新结果，type: urge-催办
      }, info.pollIntervalMs || 1000);
      yield this.changeData({
        pollIntervalId: newPollIntervalId,
      });
    } else {
      Modal.error({ title: msg });
    }
  },
  /**
   * 重新付款
   * @param params
   * @returns {Generator<Promise<unknown>|*, boolean, *>}
   */
  * refundPaymentModify({ id, bmServiceProviderCostCodeId }) {
    markStatus('loading');
    const { code, msg } = yield refundPaymentModifyAPI({ id, bmServiceProviderCostCodeId });
    if (code === '0') {
      Message.success(t('操作成功！'));
      yield this.changeData({ refundPaymentVisible: false });
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: msg });
    }
  },
  * rePushTss(params) {
    markStatus('loading');
    const { code, msg, info } = yield rePushTssAPI(params);
    if (code === '0') {
      Message.success(info);
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: msg });
    }
  },
  * merge(params) {
    markStatus('loading');
    const { code, msg } = yield mergeAPI(params);
    if (code === '0') {
      Message.success(t('操作成功！'));
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: msg });
    }
  },

  /**
   * 异步更新附件 - 轮询获取更新结果
   */
  // 非敏感词
  * handlePollUpdateFiled({ reqToken, type }) {
    const { pollIntervalId } = yield '';
    yield this.changeData({
      loading: 0,
    });
    const tips = type === 'urge' ? t('操作成功') : t('附件更新完成！');
    const reqUrl = type === 'urge' ? pollUrgeAPI : pollUpdateFilesAPI;
    // 非敏感词
    const { code, info, msg } = yield reqUrl({ reqToken });

    if (code === '0') {
      // 更新成功
      if (info?.result === 1) { // result: 0-未完成 1-成功 2-失败
        clearInterval(pollIntervalId);
        Message.success(tips);
        yield this.changeData({
          importModalVisible: false,
          urgeVisible: false,
          loading: 1,
        });
        yield this.handlePaginationChange({ pageNum: 1 });
        yield this.changeData({
          loading: 1,
        });
      }
      // 更新失败
      if (info?.result === 2) {
        yield this.changeData({
          loading: 1,
        });
        clearInterval(pollIntervalId);
        Modal.error({ title: msg });
      }
    } else { // 更新失败
      yield this.changeData({
        loading: 1,
      });
      clearInterval(pollIntervalId);
      Modal.error({ title: msg });
    }
  },

  /**
   * 获取apollo配置
   */
  * getApolloConfig() {
    const apolloConfigRes = yield getApolloConfigAPI({ params: ['WBMS_PAY_ORDER_EXECUTE_OLD'] });
    const apolloConfigFormatRes = apolloFormatObj(apolloConfigRes?.info || {});

    if (apolloConfigRes?.code === '0') {
      yield this.changeData({
        isExecuteOldRule: apolloConfigFormatRes.WBMS_PAY_ORDER_EXECUTE_OLD === '1',
      });
    }
  },

  /**
   * 旧补传附件逻辑 - 通过开关控制是否执行旧逻辑
   */
  * oldUpdateFiles() {
    markStatus('loading');
    const { payOrderId, fileList, fileListTmp } = yield '';
    // fileListTmp中的文件不在fileList中则进行删除
    const needDeleteFiles = fileListTmp.filter((ft) => (fileList.map((f) => (f.id)).indexOf(ft.id) < 0));
    // fileList中的文件不在fileListTmp中则进行上传
    const needUploadFiles = fileList.filter((f) => (fileListTmp.map((ft) => (ft.id)).indexOf(f.id) < 0));
    const needDeleteList = []; // 删除文件的请求列表
    const needUploadList = []; // 上传文件的请求列表
    needDeleteFiles.forEach((item) => {
      needDeleteList.push({
        fileBusinessType: item.businessType,
        fileId: item.id,
        id: payOrderId,
      });
    });
    needUploadFiles.forEach((item) => {
      const { fileName, fileUrl } = item;
      needUploadList.push({
        fileBusinessType: item.businessType,
        fileName,
        fileUrl,
        id: payOrderId,
      });
    });
    if (!needDeleteList.length && !needUploadList.length) {
      Message.success(t('无更新数据！'));
      yield this.changeData({
        importModalVisible: false,
      });
      return;
    }
    const updateRps = yield updateFilesAPI([...needDeleteList, ...needUploadList]);
    if (updateRps.code === '0') {
      Message.success(t('附件更新完成！'));
      yield this.changeData({
        importModalVisible: false,
      });
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: updateRps.msg });
    }
  },

  /**
   * 旧删除附件逻辑 - 通过开关控制是否执行旧逻辑
   */
  * oldDeleteFiles(params) {
    markStatus('loading');
    const res = yield updateFilesAPI(params);
    if (res.code === '0') {
      Message.success(t('附件更新成功！'));
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: res.msg });
    }
  },

  /**
   * 旧催办逻辑 - 通过开关控制是否执行旧逻辑
   * @param params
   * @returns {Generator<Promise<unknown>|*, boolean, *>}
   */
  * oldUrge(params) {
    markStatus('loading');
    const { code, msg } = yield urgeAPI(params);
    if (code === '0') {
      Message.success(t('操作成功！'));
      yield this.changeData({ urgeVisible: false });
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: msg });
    }
  },
  /**
   * 付款作废
   */
  * invalidPay() {
    markStatus('loading');
    const { selectedRows, invalidPayReason } = yield '';
    const id = selectedRows[0]?.id;
    const res = yield invalidPayAPI({ id, invalidPayReason });
    if (res.code === '0') {
      Message.success(t('作废成功!'));
      yield this.changeData({
        cancelModalVisible: false,
      });
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({
        title: res.msg,
      });
    }
  },
};
