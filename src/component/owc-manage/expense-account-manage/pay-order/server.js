import { sendPostRequest } from '@src/server/common/public';

// 查询接口
export const searchAPI = (param) => sendPostRequest({
  url: '/pay_order/query_list',
  param,
}, process.env.WBMS_FRONT);

// 作废重提
export const cancelAPI = (param) => sendPostRequest({
  url: '/pay_order/cancel',
  param,
}, process.env.WBMS_FRONT);

// 整单作废
export const invalidAPI = (param) => sendPostRequest({
  url: '/pay_order/invalid',
  param,
}, process.env.WBMS_FRONT);

// 预提交
export const preCommitAPI = (param) => sendPostRequest({
  url: '/pay_order/pre_commit',
  param,
}, process.env.WBMS_FRONT);

// 确认提交审核-原单提报
export const confirmCommitAPI = (param) => sendPostRequest({
  url: '/pay_order/commit',
  param,
}, process.env.WBMS_FRONT);

// 主报账单补传附件
export const updateFilesAPI = (param) => sendPostRequest({
  url: '/pay_order/update_files',
  param,
}, process.env.WBMS_FRONT);

/**
 * 催办
 * @param param
 * @returns {*}
 */
export const urgeAPI = (param) => sendPostRequest({
  url: '/pay_order/urge',
  param,
}, process.env.WBMS_FRONT);

/**
 * 退票付款
 * @param param
 * @returns {*}
 */
export const refundPaymentModifyAPI = (param) => sendPostRequest({
  url: '/pay_order/refund_payment',
  param,
}, process.env.WBMS_FRONT);

/**
 * 重新报账
 * @param param
 * @returns {*}
 */
export const rePushTssAPI = (param) => sendPostRequest({
  url: '/pay_order/re_push_tss',
  param,
}, process.env.WBMS_FRONT);

/**
 * 合单付款
 * @param param
 * @returns {*}
 */
export const mergeAPI = (param) => sendPostRequest({
  url: '/pay_order/merge',
  param,
}, process.env.WBMS_FRONT);

/**
 * 异步更新附件（提交）
 * @param param
 * @returns {*}
 */
export const submitUpdateFilesAPI = (param) => sendPostRequest({
  url: '/pay_order/submit_update_files',
  param,
}, process.env.WBMS_FRONT);

/**
 * 异步更新附件（轮询）
 * @param param
 * @returns {*}
 */
export const pollUpdateFilesAPI = (param) => sendPostRequest({
  url: '/pay_order/poll_update_files',
  param,
}, process.env.WBMS_FRONT);

/**
 * 报账单催办（提交）
 * @param param
 * @returns {*}
 */
export const submitUrgeAPI = (param) => sendPostRequest({
  url: '/pay_order/submit_urge',
  param,
}, process.env.WBMS_FRONT);

/**
 * 报账单催办（提交）
 * @param param
 * @returns {*}
 */
export const pollUrgeAPI = (param) => sendPostRequest({
  url: '/pay_order/poll_urge',
  param,
}, process.env.WBMS_FRONT);

/**
 * 付款作废
 * @param param
 * @returns {*}
 */
export const invalidPayAPI = (param) => sendPostRequest({
  url: '/pay_order/invalid_pay',
  param,
}, process.env.WBMS_FRONT);
