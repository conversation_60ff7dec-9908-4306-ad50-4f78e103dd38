import React from 'react';
import {
  Table, Tabs,
} from 'shineout';
import { handleTablePros, round } from '@src/lib/deal-func';
import { t } from '@shein-bbl/react';
import { TableColumnItem } from '@src/typing/base';
import type { SummaryItem } from 'shineout/lib/Table/Props';
import { Link } from 'react-router-dom';
import store from '../reducers';
import { IPageProps, IBusinessOrderInfoListItem, IItemOrderInfoListItem } from '../types';
import style from '../style.less';

function BillInfo(props: IPageProps) {
  const {
    currentDetailItem,
  } = props;
  // 拆分返回列表数据 最后一行数据是汇总数据
  function splitList<T>(list: T[]): { allButLastItem: T[]; lastItem: T | Record<string, unknown> } {
    const listLength = list.length;
    // 如果只有一条数据，特殊处理
    if (listLength === 1) {
      return {
        allButLastItem: list,
        lastItem: list[0],
      };
    }
    return {
      // 除最后一行的数据
      allButLastItem: listLength > 1 ? list.slice(0, -1) : [],
      // 最后一行数据，兜底为空对象
      lastItem: listLength > 0 ? list[listLength - 1] : {},
    };
  }
  const { businessOrderInfoList, itemOrderInfoList } = currentDetailItem;
  // 业务子类列
  const businessSubTypeListResult = splitList(businessOrderInfoList || []);
  const businessSubTypeColumns: (TableColumnItem<IBusinessOrderInfoListItem> & {
    needThousandsPoint?: boolean;
  })[] = [
    {
      title: '',
      render: (d, i) => {
        if (i === (businessSubTypeColumns.length - 1)) {
          return t('汇总');
        }
      },
      width: 120,
    },
    {
      title: t('业务子类'),
      render: 'businessSubTypeName',
      width: 120,
    },
    {
      title: t('报账金额'),
      width: 120,
      render: (r) => (round(r?.costPay || 0, 8)),
      needThousandsPoint: true,
    },
    {
      title: t('税率%'),
      render: (r) => (round(r?.taxRate || 0, 2)),
      needThousandsPoint: true,
      width: 120,
    },
    {
      title: t('税额'),
      render: (r) => (round(r?.costRate || 0, 8)),
      needThousandsPoint: true,
      width: 120,
    },
    {
      title: t('报账不含税额'),
      render: (r) => (round(r?.costPayNoTax || 0, 8)),
      needThousandsPoint: true,
      width: 120,
    },
    {
      title: t('推送财务报账金额'),
      render: (r) => (round(r?.costPayPss || 0, 2)),
      needThousandsPoint: true,
      width: 140,
    },
    {
      title: t('推送财务税额'),
      render: (r) => (round(r?.costRatePss || 0, 2)),
      needThousandsPoint: true,
      width: 140,
    },
    {
      title: t('推送财务不含税额'),
      render: (r) => (round(r?.costPayNoTaxPss || 0, 2)),
      needThousandsPoint: true,
      width: 140,
    },
  ];
  const businessSubTypeColumnsSummary: SummaryItem[][] = [
    [
      { render: () => <span>{t('汇总')}</span> },
      { render: () => <span /> },
      {
        render: () => <span>{businessSubTypeListResult.lastItem?.costPay}</span>,
      },
      {
        render: () => <span>{businessSubTypeListResult.lastItem?.taxRate}</span>,
      },
      {
        render: () => <span>{businessSubTypeListResult.lastItem?.costRate}</span>,
      },
      {
        render: () => <span>{businessSubTypeListResult.lastItem?.costPayNoTax}</span>,
      },
      {
        render: () => <span>{businessSubTypeListResult.lastItem?.costPayPss}</span>,
      },
      {
        render: () => <span>{businessSubTypeListResult.lastItem?.costRatePss}</span>,
      },
      {
        render: () => <span>{businessSubTypeListResult.lastItem?.costPayNoTaxPss}</span>,
      },
    ],
  ];
  // 费用项明细列
  const costItemsDetailListResult = splitList(itemOrderInfoList || []);
  // 业务单号列跳转相关页面
  const renderLink = (d) => {
    // 账单类型 1: 补扣款单 2: 应付明细单 3: 补扣款对账单
    const billType = Number(d?.billType);
    switch (billType) {
      case 1:
        return (
          <Link
            target="_blank"
            to={`/dwc-manage/supplement-deduct-order?deductOrder=${d.businessOrderNo}`}
          >
            {d.businessOrderNo}
          </Link>
        );

      case 2:
        return (
          <Link
            target="_blank"
            to={`/dwc-manage/bill-management/payable-order-detail?payableOrderDetailNo=${d.businessOrderNo}`}
          >
            {d.businessOrderNo}
          </Link>
        );

      case 3:
        return (
          <Link
            target="_blank"
            to={`/dwc-manage/supplement-deduct-verify-order?deductVerifyOrder=${d.businessOrderNo}`}
          >
            {d.businessOrderNo}
          </Link>
        );

      default:
        return d.businessOrderNo;
    }
  };

  const costItemsDetailColumns: (TableColumnItem<IItemOrderInfoListItem> & {
    needThousandsPoint?: boolean;
  })[] = [
    {
      title: '',
      render: () => <span />,
      width: 120,
    },
    {
      title: t('业务子类'),
      render: 'businessSubTypeName',
      width: 120,
    },
    {
      title: t('业务单号'),
      render: (d) => (renderLink(d)),
      width: 220,
    },
    {
      title: t('费用项名称'),
      render: 'bmCostItemName',
      width: 160,
    },
    {
      title: t('费用所属部门'),
      render: 'costDeptName',
      width: 160,
    },
    {
      title: t('子仓'),
      render: 'subWarehouseName',
      width: 120,
    },
    {
      title: t('暂估明细数'),
      render: 'estimateNum',
      width: 120,
    },
    {
      title: t('核对明细数'),
      render: 'settleNum',
      width: 120,
    },
    {
      title: t('价格配置名称'),
      render: (r) => (
        <span
          className={style.pointer}
          onClick={() => {
            store.changeData({
              priceConfigId: r?.bmStdPriceConfigId,
              priceConfigPreviewVisible: true,
            });
          }}
        >
          {r?.priceConfigName}
        </span>
      ),
      width: 160,
    },
    {
      title: t('报账金额'),
      render: (r) => (round(r?.costPay || 0, 8)),
      needThousandsPoint: true,
      width: 120,
    },
    {
      title: t('税率%'),
      render: (r) => (round(r?.taxRate || 0, 2)),
      needThousandsPoint: true,
      width: 120,
    },
    {
      title: t('税额'),
      render: (r) => (round(r?.costRate || 0, 8)),
      needThousandsPoint: true,
      width: 120,
    },
    {
      title: t('报账不含税额'),
      render: (r) => (round(r?.costPayNoTax || 0, 8)),
      needThousandsPoint: true,
      width: 140,
    },
    {
      title: t('备注'),
      render: 'payRemark',
      width: 220,
    },
    {
      title: t('核对人'),
      render: 'creator',
      width: 220,
    },
    {
      title: t('核对时间'),
      render: 'createTime',
      width: 220,
    },
  ];
  const costItemsDetailColumnsSummary: SummaryItem[][] = [
    [
      { render: () => <span>{t('汇总')}</span> },
      { render: () => <span /> },
      { render: () => <span /> },
      { render: () => <span /> },
      { render: () => <span /> },
      { render: () => <span /> },
      { render: () => <span /> },
      { render: () => <span /> },
      { render: () => <span /> },
      {
        render: () => <span>{costItemsDetailListResult.lastItem?.costPay}</span>,
      },
      {
        render: () => <span>{costItemsDetailListResult.lastItem?.taxRate}</span>,
      },
      {
        render: () => <span>{costItemsDetailListResult.lastItem?.costRate}</span>,
      },
      {
        render: () => <span>{costItemsDetailListResult.lastItem?.costPayNoTax}</span>,
      },
      { render: () => <span /> },
      { render: () => <span /> },
      { render: () => <span /> },
    ],
  ];
  return (
    <Tabs shape="bordered" inactiveBackground="#f2f2f2" style={{ width: '100%' }}>
      <Tabs.Panel size="small" tab={t('业务子类汇总')} style={{ width: '100%' }}>
        <Table<IBusinessOrderInfoListItem, IBusinessOrderInfoListItem[]>
          data={businessSubTypeListResult.allButLastItem}
          {...handleTablePros(businessSubTypeColumns)}
          columns={businessSubTypeColumns}
          bordered
          style={{ maxHeight: '300px' }}
          keygen="id"
          empty={t('暂无数据')}
          size="small"
          summary={businessSubTypeColumnsSummary}
        />
      </Tabs.Panel>
      <Tabs.Panel tab={t('费用项明细')} style={{ width: '100%' }}>
        <Table<IItemOrderInfoListItem, IItemOrderInfoListItem[]>
          {...handleTablePros(costItemsDetailColumns)}
          fixed="both"
          data={costItemsDetailListResult.allButLastItem}
          columns={costItemsDetailColumns}
          bordered
          style={{ maxHeight: '300px' }}
          keygen="id"
          empty={t('暂无数据')}
          size="small"
          summary={costItemsDetailColumnsSummary}
        />
      </Tabs.Panel>
    </Tabs>
  );
}

export default BillInfo;
