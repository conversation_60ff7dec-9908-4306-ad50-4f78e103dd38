import {
  IFetchResponse, IViewBaseProps,
  IDictItem,
} from '@src/typing/base';

export interface IAppendixInfoListItem {
  /** 数据来源 */
  businessTypeName?:string;
  /** 操作人 */
  createUserName?:string;
  /** 文件名 */
  fileName?:string;
  fileStatus?: number| string;
  /** 附件状态 */
  fileStatusName?:string;
  /** 地址 */
  fileUrl?:string;
  /** 主键id */
  id?:number;
  /** 更新时间 */
  lastUpdateTime?:string;
  /** 数据来源 */
  sourceName?:string;
}

type IAppendixInfoList = IAppendixInfoListItem[];

interface IBasicInfo {
  /** 账期起始日期 */
  billDayBegin?:string;
  /** 账期结束日期 */
  billDayEnd?:string;
  bmPayMainId?:number;
  /** 付款主体 */
  bmPayMainName?:string;
  businessClass?:number;
  /** 业务大类 */
  businessClassName?:string;
  /** 应付金额 */
  costAmount?:number;
  /** 账单币种/合同币种 */
  currency?:string;
  /** 对账项目名称 */
  reconciliationItemName?:string;
  /** 服务商简称 */
  serviceProviderAbbreviation?:string;
  /** 结算编码; */
  serviceProviderCostCode?:string;
  /** 系统链路名称 */
  systemLinkName?:string;
  /** 尾差调整 */
  tailDifference?:number;
  /** 仓库 */
  warehouseName?:string;
  areaBelong?: number;
  bmServiceProviderCostCodeId?: string;
  bmChargeServiceProviderId?: string;
  reconciliationItemId?: string;
}

export interface IBusinessOrderInfoListItem {
  businessSubType?:number;
  /** 业务子类 */
  businessSubTypeName?:string;
  /** 报账金额 */
  costPay?:number;
  /** 报账不含税额 */
  costPayNoTax?:number;
  /** 推送财务不含税额 */
  costPayNoTaxPss?:number;
  /** 推送财务报账金额 */
  costPayPss?:number;
  /** 税额 */
  costRate?:number;
  /** 推送财务税额 */
  costRatePss?:number;
  /** 税率 */
  taxRate?:number;
}

type IBusinessOrderInfoList = IBusinessOrderInfoListItem[];

export interface IInvoiceInfoListItem {
  areaBelong?:number | string;
  /** 境内/境外 */
  areaBelongName?:string;
  /** 数据来源 */
  businessTypeName?:string;
  /** 操作人 */
  createUserName?:string;
  /** 文件名 */
  fileName?:string;
  /** 附件状态 */
  fileStatus?:string;
  fileStatusName?:string;
  /** 地址 */
  fileUrl?:string;
  /** 主键id */
  id?:string;
  /** 发票金额（含税） */
  invoiceAmount?:number | string;
  /** (不含税）金额 */
  invoiceAmountNoTax?:number| string;
  /** 发票代码 */
  invoiceCode?:string;
  /** 发票币种 */
  invoiceCurrency?:string;
  /** 开票日期 */
  invoiceDate?:string;
  /** 发票号码 */
  invoiceNo?:string;
  /** 发票类型 */
  invoiceType?:string;
  /** 发票类型名称 */
  invoiceTypeName?:string;
  /** 更新时间 */
  lastUpdateTime?:string;
  /** 类型 */
  mediaType?:string;
  /** 类型名称 */
  mediaTypeName?:string;
  /** 税额 */
  taxAmount?:number | string;
  /** 税率 */
  taxRate?:number | string;
  taxType?:string;
  /** 税种名称 */
  taxTypeName?:string;
  /** 是否全部核销 */
  writeOff?:string;
  /** 核销金额 */
  writeOffAmount?:number | string;
  /** 是否全部核销名称 */
  writeOffName?:string;
  /** 数据来源 */
  sourceName?:string;
  source?: number;
}

type IInvoiceInfoList = IInvoiceInfoListItem[];

export interface IItemOrderInfoListItem {
  /** 结算费用项id */
  bmCostItemId?:string;
  /** 费用项名称 */
  bmCostItemName?:string;
  /** 业务单号 */
  businessOrderNo?:string;
  businessSubType?:number;
  /** 业务子类 */
  businessSubTypeName?:string;
  /** 核对时间 */
  commitTime?:string;
  /** 核对人 */
  commitUser?:string;
  /** 报账金额 */
  costPay?:number;
  /** 报账不含税额 */
  costPayNoTax?:number;
  /** 推送财务不含税额 */
  costPayNoTaxPss?:number;
  /** 推送财务报账金额 */
  costPayPss?:number;
  /** 税额 */
  costRate?:number;
  /** 推送财务税额 */
  costRatePss?:number;
  /** 暂估数量=已对账明细数 */
  estimateNum?:number;
  /** 付款备注 */
  payRemark?:string;
  /** 已对账明细数 */
  settleNum?:number;
  /** 子仓id */
  subWarehouseId?:number;
  /** 子仓 */
  subWarehouseName?:string;
  /** 税率 */
  taxRate?:number;
  bmStdPriceConfigId?: string;
  priceConfigName?: string;
  billType?: 1 | 2 | 3;
  creator?: string;
  createTime?: string;
  costDeptName?: string;
}

type IItemOrderInfoList = IItemOrderInfoListItem[];

interface IModuleOptionInfo {
  /** 是否允许编辑附件 */
  appendixEditFlag?:boolean;
  /** 是否允许编辑发票 */
  invoiceEditFlag?:boolean;
  /** 是否允许编辑付款信息 */
  payEditFlag?:boolean;
  /** 是否允许编辑付款备注 */
  payRemarkEditFlag?:boolean;
}

export interface IPayEditInfo {
  /** 争议金额 */
  disputeOrderMoney?:number | string;
  /** 账单争议问题类型 */
  disputeOrderType?:string;
  /** 账单付款截止时间 */
  limitOrderTime?:string;
  payPeriodType?:number;
  /** 付款周期类型 */
  payPeriodTypeName?:string;
  /** 服务商发票接收时间 */
  receiveInvoiceTime?:string;
  /** 服务商账单接收时间 */
  receiveOrderTime?:string;
}

export interface IPayInfo {
  /** 预计支付金额 */
  costAmount?:number | string;
  /** 支付币种 */
  costCurrency?:string;
  /** 汇率 */
  exchangeRate?:number | string;
  /** 汇率方式 */
  exchangeRateType?:string | number;
}

export interface IRspListItem {
  /** 附件信息 */
  appendixInfoList?:IAppendixInfoList;
  /** 基础信息 */
  basicInfo?:IBasicInfo;
  /** 业务子类汇总 */
  businessOrderInfoList?:IBusinessOrderInfoList;
  /** 入口 1报账单(原单提报) 2子报账单(提交报账) */
  enter?:number;
  /** 发票信息 */
  invoiceInfoList?:IInvoiceInfoList;
  /** 费用项明细 */
  itemOrderInfoList?:IItemOrderInfoList;
  /** 模块操作选项信息 */
  moduleOptionInfo?:IModuleOptionInfo;
  /** 付款相关信息 */
  payEditInfo?:IPayEditInfo;
  /** 付款信息 */
  payInfo?:IPayInfo;
  /** 子报账单列表(提交报账)时必填 */
  payOrderDetailIds?:string[];
  /** 主报账单列表(原单提报)报账时必填 */
  payOrderId?:number;
  /** 付款备注 */
  payRemark?:string;
  // 报账单号
  payOrderNo?: string;
}

export type IRspList = IRspListItem[];

export interface IDataItem {
  /** 单个报账单详情 */
  rspList?:IRspList;
  /** 服务商简称 */
  serviceProviderAbbreviation?:string;
}

type IData = IDataItem[];

interface IMeta {
  /** 数据记录数 */
  count?:number;
  /** 用户自定义扩展 */
  customObj?:unknown;
}

interface IResponseBodyInfo {
  /** 结果 */
  data:IData;
  /** 元数据 */
  meta:IMeta;
}

export type IGetListAPIRequest = {
  /** 入口 1报账单(原单提报) 2子报账单(提交报账) */
  enter?:number;
  isSensitized?:boolean;
  /** 子报账单列表(提交报账)时必填 */
  payOrderDetailIds?:number[];
  /** 主报账单列表(原单提报)报账时必填 */
  payOrderId?:number;
  userName?:string;
}
export type IGetListAPIResponse = IFetchResponse<IResponseBodyInfo>;

export type IRequestParams = {
  enter?: 1 | 2,
  payOrderDetailIds?: Array<string>
  payOrderId?: string;
  operateType?: string;
}
export interface IStateType {
  loading: number; // 0 loading, 1 load success, 2 load fail
  ready: boolean;
  formRef?: undefined,
  showMenu: boolean, // 是否显示左侧菜单
  currCostCodeId: string, // 当前供应商账户信息id
  currAreaBelong: number, // 当前供应商账户信息地区
  supplierCostCodeViewModalVisible: boolean, // 供应商账户信息查看弹窗 —— 境内
  supplierAbroadCostCodeViewModalVisible: boolean, // 供应商账户信息查看弹窗 —— 境外
  reconciliationPreviewVisible: boolean, // 查看对账项目弹框
  serviceProviderList: IData, // 服务商列表数据
  currentProviderItem: IDataItem, // 当前选择的服务商
  currentDetailItem: IRspListItem, // 选中的报账单对象信息
  currentDetailTabKey: number, // 选中的报账单tabkey
  costExchangeRateTypeList: IDictItem[], // 付款汇率方式下拉
  payPeriodTypeOptions: IDictItem[], // 付款周期类型下拉
  disputeOrderTypeOptions: IDictItem[], // 账单争议问题类型下拉
  addAndEditInvoiceItemObj: IInvoiceInfoListItem, // 新增编辑发票对象信息
  addAndEditInvoiceModalVisible: boolean, // 发票信息新增编辑弹框
  addAndEditInvoiceModalType: 1 | 2, // 1 新增 2 编辑
  areaBelongOptions: IDictItem[], // 海外/境内下拉
  invoiceCurrencyOptions: IDictItem[], // 发票币种下拉
  writeOffOptions: IDictItem[], // 是否全部核销下拉
  taxTypeOptions: IDictItem[], // 税种名称
  mediaTypeOptions: IDictItem[], // 类型下拉
  invoiceTypeOptions: IDictItem[], // 发票类型下拉
  addAndEditAttachmentItemObj: IAppendixInfoListItem, // 新增编辑附件对象信息
  addAndEditAttachmentModalVisible: boolean, // 附件信息新增编辑弹框
  requestParams: IRequestParams,
  priceConfigId: string, // 单价配置参数
  priceConfigPreviewVisible: boolean, // 单价配置弹框是否显示
}

export type IPageProps = IViewBaseProps<IStateType>;
