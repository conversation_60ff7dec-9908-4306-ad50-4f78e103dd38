// eslint-disable-next-line max-classes-per-file
import React from 'react';
import { ApmPageWrapper } from '@shein/apm-helper';
import { sendError } from 'sheinq';
import { Spin } from 'shineout';
import { BaseApp, MicroApp } from '@alita/react';
import Proptypes from 'prop-types';
import {
  Redirect, Route as RawRoute, Router, Switch,
} from 'react-router-dom';
import injectStore from 'rrc-loader-helper/lib/inj-dispatch';
import {
  fullScreenRoutes, outboundPath, inboundPath, wmsStandardPath,
} from '@src/lib/constants';
// TODO：中台开关代码，先注释，无问题后续删除
import { standardRollbackPageLocalStorage } from '@src/lib/storage-new';
import { camel2Under } from '@src/lib/camal-case-convertor';
import { createHashHistory } from 'history';
import { ErrorPageExtraInfo } from '@src/lib/deal-func';
// eslint-disable-next-line import/no-unresolved
import _routers from '@@/lessCoding/routers';
import { setReducers } from 'rrc-loader-helper/lib/imp';
import Nav from './nav/view';
import KanbanComponent from './common/kanban-component';
import Forbidden from './forbidden/index';
import ErrorComponent from './public-component/error-component';

/* eslint-disable */
const WrapperComponent = Component => props => <Component {...props} params={props.match.params} />;
export const KanbanComponentBox = Component => props => <KanbanComponent><Component {...props} params={props.match.params} /></KanbanComponent>;

class Route extends React.Component {
  constructor(props) {
    super(props);
    this.map = new Map();
  }
  render() {
    const { component, isKanban, path } = this.props;
    if (!this.map.has(component)) {
      if (fullScreenRoutes.has(path)) {
        this.map.set(component, KanbanComponentBox(component))
      } else if (isKanban) {
        this.map.set(component, KanbanComponentBox(component))
      } else {
        this.map.set(component, WrapperComponent(component))
      }
    }
    return <RawRoute {...this.props} component={this.map.get(component)} />;
  }
}
const isMicroApp = path => {
  console.log('[isMicroApp] path', path)
  if([...outboundPath].some(i => path.indexOf(i) > -1)) {
    return true;
  }
  if([...inboundPath].some(i => path.indexOf(i) > -1)) {
    return true;
  }
  // if([...inboundStandardPath].some(i => path.indexOf(i) > -1)) {
  //   return true;
  // }
  if([...wmsStandardPath].some(i => path.indexOf(i) > -1)) {
    return true;
  }
  return false;
}
const NotFound = () => {
  if(isMicroApp(window.location.hash)) return null;

  return (
    <div style={{ textAlign: 'center', marginTop: 200,}}>
      <h1>404 - 页面未找到</h1>
      <p>抱歉，您访问的页面不存在。</p>
      <ErrorPageExtraInfo />
      <a href='/#/'>返回首页</a>
    </div>
  );
}

class ErrorBoundary extends React.Component {
  componentDidCatch(error, info) {
    sendError(error);
    try {
      window.__apmPlugin__.whiteScreen?.sendRenderError(error);
      window.__apmPlugin__.whiteScreen?.execSendRenderError();
    } catch {
      // do nothing
    }
  }
  render() {
    return this.props.children;
  }
}

// 👮 禁止删除 alert!! for loader
import reducers from './index';
import routerSwitch from './nav/router-switch';
let store;
setReducers(reducers);

// TODO：中台切换开关，先注释
async function getSwitch() {
  return new Promise((resolve, rej) => {
    window.fetch('/wgs/front/config/getConfig', {
      method: 'POST',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(camel2Under({
        params: ['STANDARD_ROLLBACK_PAGE'],
      })), // body data type must match "Content-Type" header
    }).then((res) => resolve(res)).catch((err) => {
      rej(err);
    });
  });
}

const NavWrapper = props => {
  // TODO：中台路由切换
  routerSwitch();

  // 增加加载子应用的loading
  const Loading =  <Spin  name="cube-grid" color="#1890ff" style={{
    "position": "absolute",
    "top": "50%",
    "left": "50%",
    "transform": "translate(-50%, -50%)"
  }}/>

  return (
    <Nav {...{ fullScreenRoutes, ...props }}>
      <Switch>
        <Redirect exact from="/" to="/index" />
        {_routers(reducers).map(x => {
          return <Route {...x} />;
        })}
        <Route path="*" component={NotFound} />
      </Switch>
      {/* 出库子应用 */}
      <MicroApp name='outbound' path={[...outboundPath]} address='/outbound.html'
       scopeCss loading={Loading}
       onError={err => <ErrorComponent errorStack={err} />}
       />
      {/* 入库子应用 */}
      <MicroApp name='inbound' path={[...inboundPath]} address='/inbound.html' scopeCss loading={Loading}
      onError={err => <ErrorComponent errorStack={err} />}
      />
      {/*/!* 入库中台应用 *!/*/}
      {/*<MicroApp name='inbound-standard' path={[...inboundStandardPath]} address='/inbound-standard.html' scopeCss loading={Loading}*/}
      {/*onError={err => <ErrorComponent errorStack={err} />}*/}
      {/*/>*/}
      {/* 标准库子应用 */}
      <MicroApp name='wms-standard' path={[...wmsStandardPath]} address='/wms-standard.html'  scopeCss loading={Loading}
      onError={err => <ErrorComponent errorStack={err} />}
      />
    </Nav>
  );
};


const Routes = ({ innerStore }) => {
  window.standardRollbackPage = [];
  // TODO：中台开关代码，先注释
  window.standardRollbackPage = (standardRollbackPageLocalStorage.getItem() || []);
  React.useEffect(() => {
    async function fetchData() {
      try {
        const res = await getSwitch();
        const switchData = await res.json();
        if (switchData?.info && Array.isArray(switchData.info)) {
          standardRollbackPageLocalStorage.setItem(switchData.info[0].configValue || []);
        } else {
          standardRollbackPageLocalStorage.setItem([]);
        }
      } catch (error) {
        standardRollbackPageLocalStorage.setItem([]);
      }
    }
    fetchData();
    return () => {}
  }, []);
  store = innerStore;
  injectStore(innerStore);
  // https://sodoc.sheincorp.cn/independent/138/other/faq.html
  const history = createHashHistory()
  return (
    <BaseApp
      onMount={({name})=>{
        // 子应用 非沙盒保持一致. setTimeout 使用 window.setTimeout 兼容lcd
        delete Alita.apps[name].proxyWindow.setTimeout
     }}
     sandbox={{
      scopeVariable: [
        'sheinq',
      ],
    }}
    onError={err => <ErrorComponent errorStack={err} />}
    >
      <ErrorBoundary>
        <Router history={history}>
          <Switch>
            <Route path="/forbidden" component={Forbidden}/>
            <Route path="/" component={ApmPageWrapper(NavWrapper, 'nav')} />
          </Switch>
        </Router>
      </ErrorBoundary>
    </BaseApp>
  );
};
/* eslint-disable */
Routes.propTypes = {
  history: Proptypes.shape(),
  innerStore: Proptypes.shape(),
};

export default Routes;
