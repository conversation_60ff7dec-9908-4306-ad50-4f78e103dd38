// 不需要菜单栏，默认全屏的路由
// 菜单栏中点击对应的路由会新开页面
export const fullScreenRoutes = new Set(
  [
    '/board/counting-monitoring',
    '/board/combination',
    '/board/replenishment-progress-board',
    '/board/stock-full-process',
    '/board/wellen-balance-monitoring',
    '/board/automatic-storehouse-board',
    '/board/comb-exp-on-shelf',
    '/board/first-board',
    '/board/packing-board',
    '/board/pick-timeout-board',
    '/board/main-board',
    '/wms-standard/board-mgt/call-task-board',
    '/board/national-line-monitor',
    '/board/second-board',
    '/board/in-warehouse-board',
    '/board/stock-scroll-screen',
    '/board/rfid-tools-control',
    '/board/in-warehouse-monitor',
    '/board/inbound-board',
    '/board/outbound-board',
    '/board/combination-overdue-monitoring-board',
    '/board/collection-monitor',
    '/board/rbc-deliver-collect-board',
    '/board/rbc-collection-board',
    '/board/main-board-2020',
    '/board/rbc-wave-operation-board',
    '/home/<USER>',
    '/home/<USER>',
    '/board/global-capacity-dashboard',
    '/login-new',
  ],
);

export default fullScreenRoutes;

// outbound仓库页面路径
export const outboundPath = [
  '/oversea', // 海外
  '/overseas-forwarding', // 海外转发
  '/management', // 出库开发者工具
  '/outbound', // 出库
  '/combination', // 合包业务
  '/special-out', // 特殊出库管理
  '/exception', // 异常管理
  '/shipping-execution', // 发货管理
  // --- 拣货相关配置 ---
  '/basic/stock-out-config/pick_task_overtime_config',
  // --- 出库输送线配置 ---
  '/basic/transfer-line-config',
  // --- 波次合并配置 ---
  '/basic/wellen-merge-config',
  // --- 合包集货位看板 ---
  '/board/cb-location-query',
  // --- 超时包裹监控 ---
  '/board/timeout-package-monitoring',
  // --- 闪电波监控看板 ---
  '/board/flash-wave-monitoring',
  // -- 合包暂存位看板 ---
  '/board/cb-stash-location',
  // --- 集货转运配置 ---
  '/sysconfig/outbound/transshipment-configuration',
  // --- 小件二分袋查询 ---
  '/transfer-management/second-sorting-bag',
  // --- 小件二分查询-查看日志页 ---
  '/transfer-management/small-item-binary-detail',
  // --- 小件二分查询 ---
  '/transfer-management/small-item-binary-query',
  // --- 小件分播明细查询 ---
  '/transfer-management/small-seed-detail',
  // --- 格口周转箱关系管理 ---
  '/basic/grid-container-management',
  // --- 格口管理 ---
  '/basic/grid-management',
  // --- 格口信息查询 ---
  '/basic/grid-message-search',
  // --- 快手一分格口配置 ---
  '/basic/quick-hand-first-configure',
  //  --- 计价管理 ---
  '/user-manage/salary-manage/salary-config',
  // --- 批次库存查询 ---
  '/stock-manage/batch-of-stock-query',
  // --- 托盘库存 ---
  '/stock-manage/pallet-inventory-query',
  // --- 特殊出库预占配置 ---
  '/basic/special-stock-occupy-config',
  // --- 特殊出库转运配置 ---
  '/basic/transfer-config',
  // --- 自动化设备配置 ---
  '/basic/auto-device-config',
  // --- 自动化设备接口地址配置 ---
  '/basic/auto-device-interface-config',
  // --- 主看板配置 ---
  '/basic/main-kanban-configuration',
  // --- 国家线排序配置 ---
  '/basic/national-line-monitor-configuration',
  // --- 作业环节超时看板 ---
  '/basic/operate-timeout-config',
  // --- 包裹取消原因 ---
  '/basic/package-cancel-reason',
  // --- 包裹标识管理 ---
  '/basic/parcel-identification-management',
  //  --- 出库环节超时企微推送配置 ---
  '/basic/stock-out-config/wechat-push-config',
  //  --- 出库作业配置 ---
  '/basic/weight-diff-config',
  //  --- 包裹打印 ---
  '/basic-functions/package-print',
  //  --- 独立品牌耗材推荐规则 ---
  '/basic/independent-brand-consumables',
  //  --- 耗材推荐策略维护 ---
  '/basic/material-recommend-config',
  //  --- 渠道耗材规则维护 ---
  '/basic/material-rule-config',
  //  --- 包裹面单打印前置配置 ---
  '/basic/package-sheet-print',
  //  --- 包裹重量差对应关系表 ---
  '/basic/package-weight-relation',
  //  --- 物料配置 ---
  '/sysconfig/outbound/material-configuration/scan',
  //  --- 物料配置 ---
  '/sysconfig/outbound/material-configuration/view',
  //  --- 子仓集货看板 ---
  '/board/childStorehouse',
  //  --- 主仓集货看板 ---
  '/board/mainStorehouse',
  //  --- 库区任务看板 ---
  '/board/fp-monitor-board/location-task-monitor',
  //  --- 波次汇总看板 ---
  '/board/fp-monitor-board/wave-summary-monitor',
  //  --- 出库积压实时监控 ---
  '/board/outstock-monitoring',
  //  --- 小件集货暂存查询 ---
  '/transfer-management/small-collection-storage',
  //  --- 小件进度查询 ---
  '/transfer-management/small-item-pace-query',
  //  --- 实时看板 ---
  '/board/real-time-board',
  //  --- 销量预测 ---
  '/basic/sales-predict/list',
  //  --- 物料配置 ---
  '/sysconfig/outbound/material-configuration/template',
  // --- 拣货相关配置 ---
  '/basic/area-pick-task-config',
  // --- 多穿自动化配置 ---
  '/basic/automatic-storehouse-configuration',
  // --- 包裹看板 ---
  '/basic/broad',
  // --- 集货类型与波次对应表 ---
  '/basic/collection-type-and-wellen/list',
  // --- 产能管理 ---
  '/basic/destination-port-management',
  // --- 拣货相关配置 ---
  '/basic/fp-balance-distribute',
  // --- FP滑槽管理 ---
  '/basic/fp-chute-manage',
  // --- 命中规则管理 ---
  '/basic/intelligence-wellen-hit-rule',
  // --- 命中策略管理 ---
  '/basic/intelligence-wellen-hit-strategy',
  // --- 智能波次标记规则 ---
  '/basic/intelligence-wellen-mark-rule',
  // --- 智能波次标记策略 ---
  '/basic/intelligence-wellen-mark-strategy',
  // --- 智能波次组波策略 ---
  '/basic/intelligence-wellen-pull-strategy',
  // --- 智能波次组波策略日程管理  ---
  '/basic/intelligence-wellen-pull-strategy-schedule',
  // --- 合包配置  ---
  '/basic/merge-management',
  // --- 多任务配置  ---
  '/basic/mult-task-config',
  // --- 渠道组管理配置  ---
  '/basic/outbound-config/channel-group-configuration',
  // --- 包裹国家线配置 ---
  '/basic/package-national-line-config',
  // --- 拣货相关配置 ---
  '/basic/pick-task-priority',
  // --- 产能管理 ---
  '/basic/production-capacity-management',
  // --- 分播配置 ---
  '/basic/sowing-config',
  // --- 出库环节作业时效配置 ---
  '/promise/efficiencies-of-outbound-operation',
  // 等上线后再迁移
  // --- 智能波次组波规则 ---
  '/basic/intelligence-wellen-pull-rule',
  // --- 组波规则配置 ---
  '/basic/outbound-wave-group-rule',
  // --- 渠道管理（新）  ---
  '/basic/outbound-config/channel-management',
  // --- 集货配置 ---
  '/basic/gather-config',
  // --- 出库作业配置 ---
  '/basic/outbound-work-configuration',
  // --- 多产品混合配置 ---
  '/basic/channel-group-config',
  // --- 耗材管理 ---
  '/basic/consumable-management',
  // --- 库内作业配置 ---
  '/basic/in-library-worker-config',
  // --- 作业生成配置 ---
  '/basic/job-create-configuration',
  // --- 作业流程配置 ---
  '/basic/job-flow-configuration',
  // --- 标签模板配置 ---
  '/basic/label-template-config',
  // --- 分波配置 ---
  '/basic/wave-config',
  // --- 组波规则配置(新) ---
  '/basic/outbound-wave-group-rule-new',
  // 组波拉取规则
  '/basic/group-wave-pulling-rules',
];

// inbound仓库页面路径
export const inboundPath = [
  // --- 入库管理 ---
  '/qms',
  // --- 交接混装策略 ---
  '/basic/handover-mixed-strategy',
  // --- 退货收包国家线配置 ---
  '/basic/configuration-national-line',
  // --- 商品收货规则配置 ---
  '/basic/goods-receipt-restriction-rules',
  // --- 交接转运配置 ---
  '/basic/handover-and-transfer-configuration',
  // --- 交接流程配置 ---
  '/basic/handover-process-configuration',
  // --- 次品/黑码箱品类配置 ---
  '/basic/ib-type-config',
  // --- 分拣口配置 ---
  '/basic/in-storage-configuration',
  // --- 入库分拣口维护 ---
  '/basic/in-storage-maintenance',
  // --- 分仓送货管理 ---
  '/basic/inbound-configuration/delivery-management',
  // --- 子仓参数管理 ---
  '/basic/inbound-configuration/subwarehouse-params-management',
  // --- 库区入库规则配置 ---
  '/basic/inbound-configuration/warehouse-entry-rules',
  // --- 抽检重量差异配置 ---
  '/basic/inbound-configuration/weight-discrepancy-allocation',
  // --- 收货存储属性配置 ---
  '/basic/receipt-goods-type-config',
  // --- 上架运营规则 ---
  '/basic/shelf-rules-configuration',
  // --- 上架/暂存推荐规则  ---
  '/basic/staging-recommendation-rules',
  // --- 供应商送货子仓信息  ---
  '/basic/supplier-sub-warehouse',
  // --- 面料溯源历史skc  ---
  '/basic/turnover-handover-record',
  // --- 备货异常任务列表 ---
  '/board/abnormal-stock',
  // --- 订单入库差异看板 ---
  '/board/diff-of-Inwarehose-order',
  // --- 打标数据 ---
  '/board/marking/list',
  // --- 上架/暂存监控看板 ---
  '/board/putaway-temporary-save',
  // --- 入库流程库存状态监控 ---
  '/board/qc-inventory-status-monitoring',
  // --- 收货入库上架报表 ---
  '/board/receipt-report',
  // --- 备货下架任务查询 ---
  '/board/stock-off-shelf',

  // --- 前置仓叫车单查询 ---
  '/in-warehouse/call-cab-query',
  // --- 车辆调度查询 ---
  '/in-warehouse/car-scheduling',
  // --- 交接拆托记录 ---
  '/in-warehouse/handover-disass',
  // --- 交接操作明细查询 ---
  '/in-warehouse/handover-operation-detail/list',
  // --- 交接流程记录查询 ---
  '/in-warehouse/handover-process-record',
  // --- 交接差异明细 ---
  '/in-warehouse/receipt-difference-detail',
  // --- 交接单查询 ---
  '/in-warehouse/receipt-query',
  // --- 交接单明细 ---
  '/in-warehouse/receipt-query-detail',
  // --- 二次转运记录 ---
  '/in-warehouse/second-transfer-record',
  // --- 转运调度管理 ---
  '/in-warehouse/transfer-scheduling-management',

  // --- 入库权限授权 ---
  '/sysconfig/inbound',
  '/inbound',
  // --- 多货处理 ---
  '/multiple-goods-deal',
  // --- 调拨管理 ---
  '/transferBill-manage',
  '/receive-goods-check',
  '/transfer-save-manage',
  '/inbound-examples',
  '/inbound-developer-tools',
  // --- 入库配置 - 销售目的国与市场 ---
  '/inbound-conf/inf-market-country',
  // --- 入库配置 - 园区与国家市场 ---
  '/inbound-conf/inf-market-park',
  // --- 入库配置 - 末级品类与国家市场 ---
  '/inbound-conf/inf-market-category',
  '/inbound-standard',
];

// 入库中台子应用 - 转发路径
export const inboundStandardPath = [
  '/inbound-standard',
];
export const specialPath = [
  '/qms/warehousing/into-scanning-upgrade',
  '/inbound/warehouse-packing',
  '/inbound/reject-check-manage/reject-check-scan',
];

export const wmsStandardPath = [
  '/examples/wms-standard-examples',
  '/wms-standard',
];

// 页面顶部高度，包含nav:48 tab栏:28 padding-top:12 padding-bottom:12
export const TopAreaHeight = 100;
